# 🧪 Testing Guide - AI Document Analysis

## Prerequisites

### 1. Environment Setup

#### A. OpenAI API Key
You need a valid OpenAI API key to test the AI analysis functionality:

1. Go to [OpenAI Platform](https://platform.openai.com/api-keys)
2. Create an API key
3. Update your `.env` file:
```bash
OPENAI_API_KEY=sk-your-actual-api-key-here
```

#### B. System Dependencies
Install required system packages:

**On macOS:**
```bash
brew install tesseract poppler
```

**On Ubuntu/Debian:**
```bash
sudo apt-get update
sudo apt-get install tesseract-ocr poppler-utils
```

**On Windows:**
- Download Tesseract from [GitHub releases](https://github.com/UB-Mannheim/tesseract/wiki)
- Download Poppler from [releases](https://github.com/oschwartz10612/poppler-windows/releases)

### 2. Start the Application

#### A. Backend
```bash
# Install dependencies
cd backend
pip install -r requirements.txt  # or use uv/poetry

# Run migrations (if needed)
alembic upgrade head

# Start backend
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

#### B. Frontend
```bash
# Install dependencies
cd frontend
npm install

# Start frontend
npm run dev
```

#### C. Using Docker (Recommended)
```bash
# Start all services
docker-compose up -d

# Check logs
docker-compose logs -f backend
```

## Test Scenarios

### Scenario 1: Basic Document Upload and Analysis

#### Step 1: Access the Feature
1. Open browser: `http://localhost:5173`
2. Login as a lawyer or admin user
3. Navigate to **"AI Document Analysis"** in the sidebar

#### Step 2: Upload a Test Document
1. Click on the file input
2. Select a test document (PDF or image)
3. Choose **"Full AI Analysis"**
4. Add description: "Test legal document"
5. Click **"Upload & Analyze"**

#### Expected Results:
- ✅ File uploads successfully
- ✅ Progress indicator shows analysis stages
- ✅ Analysis completes without errors
- ✅ Extracted information appears

### Scenario 2: Information Validation

#### Step 1: Review Extracted Data
After analysis completes, review the extracted information:
- Parties (plaintiff, defendant, witnesses)
- Case type and confidence level
- Document summary
- Key facts
- Important dates and amounts

#### Step 2: Validate Information
1. Switch to **"Edit Mode"**
2. Modify any incorrect information
3. Set case title and client name
4. Choose validation status: **"Approved"**
5. Enable **"Create legal case"**
6. Add validation notes
7. Click **"Save Validation"**

#### Expected Results:
- ✅ Information can be edited
- ✅ Validation saves successfully
- ✅ Confidence indicators are visible

### Scenario 3: Case Creation

#### Step 1: Create Case from Validation
1. After validation is approved
2. Click **"Create Legal Case"**
3. Confirm case details
4. Wait for case creation

#### Expected Results:
- ✅ Case is created successfully
- ✅ Document is associated with the case
- ✅ Case appears in legal cases list
- ✅ Activity log shows case creation

### Scenario 4: Incremental Updates

#### Step 1: Add Document to Existing Case
1. Go to an existing legal case
2. Upload a new document to that case
3. Analyze the new document
4. Check for update suggestions

#### Step 2: Apply Updates
1. Review suggested updates
2. Select updates to apply
3. Apply selected updates
4. Verify case information is updated

#### Expected Results:
- ✅ New document analysis completes
- ✅ Update suggestions are generated
- ✅ Case information is enriched
- ✅ History tracks all changes

## Test Documents

### Sample Documents to Test

#### 1. Simple Contract (PDF)
Create a simple PDF with:
```
LEGAL AGREEMENT

Plaintiff: John Smith
Defendant: ABC Corporation
Case Type: Commercial
Amount: $50,000
Date: 2024-01-15

This is a commercial dispute regarding...
```

#### 2. Scanned Image
- Take a photo of a printed legal document
- Ensure text is clear and readable
- Test OCR functionality

#### 3. Complex Legal Document
- Use a real legal document (anonymized)
- Test extraction of multiple parties
- Verify legal references detection

## Troubleshooting

### Common Issues and Solutions

#### 1. OpenAI API Errors
**Error:** "OpenAI API key not configured"
**Solution:** 
- Verify OPENAI_API_KEY in .env file
- Restart backend service
- Check API key validity

#### 2. OCR Failures
**Error:** "Tesseract not found"
**Solution:**
- Install Tesseract OCR
- Verify installation: `tesseract --version`
- Check system PATH

#### 3. Upload Failures
**Error:** "File upload failed"
**Solution:**
- Check file size (max 50MB)
- Verify file format (PDF, JPEG, PNG, TIFF)
- Check backend logs for details

#### 4. Analysis Timeout
**Error:** "Analysis timed out"
**Solution:**
- Check OpenAI API limits
- Reduce document size
- Check network connectivity

### Debug Commands

#### Check Backend Logs
```bash
# Docker
docker-compose logs -f backend

# Local
tail -f backend/logs/app.log
```

#### Test API Endpoints
```bash
# Test document analysis
curl -X POST "http://localhost:8000/api/v1/ai-analysis/documents/{document_id}/analyze" \
  -H "Authorization: Bearer YOUR_TOKEN"

# Check analysis status
curl -X GET "http://localhost:8000/api/v1/ai-analysis/analysis/{analysis_id}/extracted-info" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

#### Database Queries
```sql
-- Check analysis records
SELECT * FROM documentanalysis ORDER BY created_at DESC LIMIT 5;

-- Check extracted information
SELECT * FROM extractedinformation ORDER BY created_at DESC LIMIT 5;

-- Check validations
SELECT * FROM uservalidation ORDER BY created_at DESC LIMIT 5;
```

## Performance Testing

### Load Testing
Test with multiple documents:
1. Upload 5-10 documents simultaneously
2. Monitor system performance
3. Check analysis completion times

### Large Document Testing
Test with large files:
1. Upload 20-30MB PDF files
2. Monitor memory usage
3. Verify OCR performance

## Security Testing

### Access Control
1. Test with different user roles
2. Verify lawyers/admins can access
3. Confirm clients cannot access

### Data Privacy
1. Verify documents are stored securely
2. Check analysis history tracking
3. Test data deletion capabilities

## Automated Testing

### Unit Tests
```bash
cd backend
pytest tests/test_ai_analysis.py -v
```

### Integration Tests
```bash
cd backend
pytest tests/integration/test_ai_workflow.py -v
```

### Frontend Tests
```bash
cd frontend
npm run test
```

## Success Criteria

The feature is working correctly if:
- ✅ Documents upload without errors
- ✅ OCR extracts text from images
- ✅ AI analysis returns structured data
- ✅ Validation interface works properly
- ✅ Cases are created successfully
- ✅ Incremental updates function
- ✅ All security controls work
- ✅ Performance is acceptable

## Reporting Issues

If you find bugs or issues:
1. Check the troubleshooting section first
2. Collect error logs and screenshots
3. Create a GitHub issue with:
   - Steps to reproduce
   - Expected vs actual behavior
   - Environment details
   - Error logs

## Next Steps

After successful testing:
1. Merge the feature branch
2. Deploy to staging environment
3. Conduct user acceptance testing
4. Train end users
5. Monitor production performance
