# TunLeg LOCAL Development Environment
# Simple setup for daily development work
# No Traefik, direct port exposure, localhost only

version: '3.8'

services:
  # PostgreSQL Database (LOCAL)
  db-local:
    image: postgres:15-alpine
    container_name: tunleg-db-local
    restart: unless-stopped
    environment:
      POSTGRES_DB: ${POSTGRES_DB:-tunleg_local}
      POSTGRES_USER: ${POSTGRES_USER:-tunleg_user}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
    volumes:
      - postgres_local_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"  # Direct access for local development
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-tunleg_user} -d ${POSTGRES_DB:-tunleg_local}"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - local-backend

  # Backend API (LOCAL)
  backend-local:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: tunleg-backend-local
    restart: unless-stopped
    depends_on:
      db-local:
        condition: service_healthy
    volumes:
      - ./backend:/app
      - ./backend/uploads:/app/uploads
      - ./backend/logs:/app/logs
    environment:
      # Database
      POSTGRES_SERVER: db-local
      POSTGRES_PORT: 5432
      POSTGRES_DB: ${POSTGRES_DB:-tunleg_local}
      POSTGRES_USER: ${POSTGRES_USER:-tunleg_user}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}

      # Application
      ENVIRONMENT: local
      SECRET_KEY: ${SECRET_KEY}
      FIRST_SUPERUSER: ${FIRST_SUPERUSER}
      FIRST_SUPERUSER_PASSWORD: ${FIRST_SUPERUSER_PASSWORD}

      # CORS and Frontend (LOCAL)
      FRONTEND_HOST: http://localhost:3000
      BACKEND_CORS_ORIGINS: http://localhost:3000,http://localhost:3001

      # Email (optional for local)
      SMTP_HOST: ${SMTP_HOST:-}
      SMTP_PORT: ${SMTP_PORT:-587}
      SMTP_TLS: ${SMTP_TLS:-true}
      SMTP_USER: ${SMTP_USER:-}
      SMTP_PASSWORD: ${SMTP_PASSWORD:-}
      EMAILS_FROM_EMAIL: ${EMAILS_FROM_EMAIL:-noreply@localhost}
      EMAILS_FROM_NAME: ${EMAILS_FROM_NAME:-TunLeg Local}

      # OpenAI Configuration
      OPENAI_API_KEY: ${OPENAI_API_KEY}
      OPENAI_MODEL: ${OPENAI_MODEL:-gpt-4}
      OPENAI_MAX_TOKENS: ${OPENAI_MAX_TOKENS:-2000}
    ports:
      - "8000:8000"  # Direct backend access
    networks:
      - local-backend
    command: uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload

  # Frontend (LOCAL)
  frontend-local:
    image: node:20
    container_name: tunleg-frontend-local
    restart: unless-stopped
    working_dir: /app
    volumes:
      - ./frontend:/app
      - /app/node_modules
    environment:
      - VITE_API_URL=http://localhost:8000
      - NODE_ENV=development
    ports:
      - "3000:3000"  # Direct frontend access
    networks:
      - local-backend
    command: sh -c "npm install && npm run dev -- --host 0.0.0.0 --port 3000"

  # Database Admin (LOCAL) - Optional
  adminer-local:
    image: adminer:4.8.1
    container_name: tunleg-adminer-local
    restart: unless-stopped
    depends_on:
      - db-local
    environment:
      ADMINER_DEFAULT_SERVER: db-local
    ports:
      - "8080:8080"  # Direct adminer access
    networks:
      - local-backend

volumes:
  postgres_local_data:

networks:
  local-backend:
    driver: bridge
