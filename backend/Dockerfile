FROM python:3.10

# Étape 1: Installer les dépendances système pour OCR
RUN apt-get update && apt-get install -y \
    tesseract-ocr \
    tesseract-ocr-fra \
    tesseract-ocr-eng \
    poppler-utils \
    && rm -rf /var/lib/apt/lists/*

# Étape 2: Mettre à jour pip
RUN pip install --upgrade pip

# Étape 3: Installer uv
RUN pip install uv==0.5.11

# Étape 4: Définir le répertoire de travail
WORKDIR /app/

# Étape 5: Copier les fichiers de configuration
COPY ./pyproject.toml ./alembic.ini /app/

# Étape 6: Gén<PERSON>rer le lockfile
RUN uv pip compile pyproject.toml -o uv.lock --resolution=highest

# Étape 7: Installer les dépendances (y compris les dépendances de développement)
RUN --mount=type=cache,target=/root/.cache/uv \
    uv pip install -r uv.lock --no-cache --system

# Étape 7: Copier le reste de l'application
COPY ./app /app/app
COPY ./scripts /app/scripts

# Étape 8: Définir les variables d'environnement
ENV PYTHONPATH=/app

# Exécuter les migrations
# RUN alembic upgrade head
# In your backend service Dockerfile
RUN pip install pytest pytest-cov
RUN pip install fastapi
# Étape 9: Commande par défaut
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]