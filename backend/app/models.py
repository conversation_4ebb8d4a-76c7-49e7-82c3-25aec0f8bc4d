import uuid
from datetime import date, datetime
from enum import Enum
from typing import Optional, Dict, List, Any

from pydantic import EmailStr
from sqlmodel import Field, Relationship, SQLModel, JSON, Column
from sqlalchemy import UniqueConstraint, Text, BigInteger


class UserRole(str, Enum):
    LAWYER = "lawyer"
    CLIENT = "client"
    ADMIN = "admin"
    ASSISTANT = "assistant"


class CaseType(str, Enum):
    CIVIL = "civil"
    CRIMINAL = "criminal"
    FAMILY = "family"
    CORPORATE = "corporate"
    IMMIGRATION = "immigration"
    PERSONAL_INJURY = "personal_injury"
    REAL_ESTATE = "real_estate"
    INTELLECTUAL_PROPERTY = "intellectual_property"
    EMPLOYMENT = "employment"
    OTHER = "other"


class CaseStatus(str, Enum):
    OPEN = "open"
    IN_PROGRESS = "in_progress"
    UNDER_REVIEW = "under_review"
    CLOSED = "closed"
    ARCHIVED = "archived"

    @classmethod
    def get_valid_transitions(cls, current_status: str, user_role: str) -> list[str]:
        """Get valid status transitions based on current status and user role"""
        transitions = {
            cls.OPEN.value: [cls.IN_PROGRESS.value, cls.ARCHIVED.value],
            cls.IN_PROGRESS.value: [cls.UNDER_REVIEW.value, cls.CLOSED.value, cls.ARCHIVED.value],
            cls.UNDER_REVIEW.value: [cls.IN_PROGRESS.value, cls.CLOSED.value, cls.ARCHIVED.value],
            cls.CLOSED.value: [cls.IN_PROGRESS.value, cls.ARCHIVED.value],  # Reopening cases
            cls.ARCHIVED.value: [cls.OPEN.value] if user_role in ["admin", "lawyer"] else []  # Admins and lawyers can unarchive
        }

        valid_transitions = transitions.get(current_status, [])

        # Additional role-based restrictions
        if user_role == "assistant":
            # Assistants cannot archive cases
            valid_transitions = [t for t in valid_transitions if t != cls.ARCHIVED.value]
        elif user_role == "client":
            # Clients cannot change status
            valid_transitions = []

        return valid_transitions

    @classmethod
    def is_valid_transition(cls, from_status: str, to_status: str, user_role: str) -> bool:
        """Check if a status transition is valid"""
        valid_transitions = cls.get_valid_transitions(from_status, user_role)
        return to_status in valid_transitions


class CasePriority(str, Enum):
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    URGENT = "urgent"


# Shared properties
class UserBase(SQLModel):
    email: EmailStr = Field(unique=True, index=True, max_length=255)
    is_active: bool = True
    is_superuser: bool = False
    full_name: str | None = Field(default=None, max_length=255)
    role: UserRole = Field(default=UserRole.LAWYER)
    bar_association_id: str | None = Field(
        default=None, max_length=255, description="ID de l'association du barreau"
    )
    specialization: str | None = Field(
        default=None, max_length=255, description="Spécialisation de l'avocat"
    )
    assigned_lawyer_id: uuid.UUID | None = Field(
        default=None, description="Lawyer assigned to this client/assistant"
    )


# Properties to receive via API on creation
class UserCreate(UserBase):
    password: str = Field(min_length=8, max_length=40)
    role: UserRole = Field(default=UserRole.CLIENT)


class UserRegister(SQLModel):
    email: EmailStr = Field(max_length=255)
    password: str = Field(min_length=8, max_length=40)
    full_name: str | None = Field(default=None, max_length=255)
    role: UserRole | None = None


# Properties to receive via API on update, all are optional
class UserUpdate(UserBase):
    email: EmailStr | None = Field(default=None, max_length=255)  # type: ignore
    password: str | None = Field(default=None, min_length=8, max_length=40)


class UserUpdateMe(SQLModel):
    full_name: str | None = Field(default=None, max_length=255)
    email: EmailStr | None = Field(default=None, max_length=255)


class UpdatePassword(SQLModel):
    current_password: str = Field(min_length=8, max_length=40)
    new_password: str = Field(min_length=8, max_length=40)


# Database model, database table inferred from class name
class User(UserBase, table=True):
    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    hashed_password: str
    assigned_lawyer_id: uuid.UUID | None = Field(
        default=None, foreign_key="user.id", description="Lawyer assigned to this client/assistant"
    )
    items: list["Item"] = Relationship(back_populates="owner", cascade_delete=True)
    legal_cases: list["LegalCase"] = Relationship(
        back_populates="lawyer", cascade_delete=True
    )
    case_templates: list["CaseTemplate"] = Relationship(back_populates="creator")
    document_templates: list["DocumentTemplate"] = Relationship(back_populates="creator")
    workflow_templates: list["WorkflowTemplate"] = Relationship(back_populates="creator")

    # Client Portal relationships
    notifications: list["Notification"] = Relationship(back_populates="user", cascade_delete=True)
    sent_messages: list["ClientMessage"] = Relationship(
        back_populates="sender",
        sa_relationship_kwargs={"foreign_keys": "[ClientMessage.sender_id]"},
        cascade_delete=True
    )
    received_messages: list["ClientMessage"] = Relationship(
        back_populates="recipient",
        sa_relationship_kwargs={"foreign_keys": "[ClientMessage.recipient_id]"},
        cascade_delete=True
    )

    # Extended Notification System relationships
    notification_preferences: list["NotificationPreferences"] = Relationship(cascade_delete=True)
    created_templates: list["NotificationTemplate"] = Relationship(
        back_populates="creator",
        cascade_delete=True
    )
    created_rules: list["NotificationRule"] = Relationship(
        back_populates="creator",
        cascade_delete=True
    )

    # Team Collaboration relationships
    team_memberships: list["CaseTeamMember"] = Relationship(
        back_populates="user",
        sa_relationship_kwargs={"foreign_keys": "[CaseTeamMember.user_id]"},
        cascade_delete=True
    )
    team_assignments: list["CaseTeamMember"] = Relationship(
        back_populates="assigner",
        sa_relationship_kwargs={"foreign_keys": "[CaseTeamMember.assigned_by]"},
        cascade_delete=True
    )
    sent_team_messages: list["TeamMessage"] = Relationship(
        back_populates="sender",
        cascade_delete=True
    )
    created_tasks: list["TeamTask"] = Relationship(
        back_populates="creator",
        sa_relationship_kwargs={"foreign_keys": "[TeamTask.created_by]"},
        cascade_delete=True
    )
    assigned_tasks: list["TeamTask"] = Relationship(
        back_populates="assignee",
        sa_relationship_kwargs={"foreign_keys": "[TeamTask.assigned_to]"},
        cascade_delete=True
    )
    # Relationship for assigned lawyer
    assigned_lawyer: Optional["User"] = Relationship(
        back_populates="assigned_users",
        sa_relationship_kwargs={"remote_side": "User.id", "post_update": True}
    )
    # Relationship for users assigned to this lawyer
    assigned_users: list["User"] = Relationship(
        back_populates="assigned_lawyer",
        sa_relationship_kwargs={"remote_side": "User.assigned_lawyer_id", "post_update": True}
    )


# Properties to return via API, id is always required
class UserPublic(UserBase):
    id: uuid.UUID


class UsersPublic(SQLModel):
    data: list[UserPublic]
    count: int


# User assignment models
class UserAssignment(SQLModel):
    user_id: uuid.UUID
    assigned_lawyer_id: uuid.UUID


class UserAssignmentUpdate(SQLModel):
    assigned_lawyer_id: uuid.UUID | None = None


# Enhanced user model with assigned lawyer info
class UserWithLawyer(UserBase):
    id: uuid.UUID
    assigned_lawyer: UserPublic | None = None


class UsersWithLawyer(SQLModel):
    data: list[UserWithLawyer]
    count: int


# Shared properties
class ItemBase(SQLModel):
    title: str = Field(min_length=1, max_length=255)
    description: str | None = Field(default=None, max_length=255)


# Properties to receive on item creation
class ItemCreate(ItemBase):
    pass


# Properties to receive on item update
class ItemUpdate(ItemBase):
    title: str | None = Field(default=None, min_length=1, max_length=255)  # type: ignore


# Database model, database table inferred from class name
class Item(ItemBase, table=True):
    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    owner_id: uuid.UUID = Field(
        foreign_key="user.id", nullable=False, ondelete="CASCADE"
    )
    owner: User | None = Relationship(back_populates="items")


# Properties to return via API, id is always required
class ItemPublic(ItemBase):
    id: uuid.UUID
    owner_id: uuid.UUID


class ItemsPublic(SQLModel):
    data: list[ItemPublic]
    count: int


# Shared properties
class LegalCaseBase(SQLModel):
    title: str = Field(min_length=1, max_length=255)
    client_name: str = Field(min_length=1, max_length=255)
    case_type: CaseType = Field(default=CaseType.OTHER)
    opening_date: date = Field(default_factory=date.today)
    description: str | None = Field(default=None, max_length=2000)
    status: CaseStatus = Field(default=CaseStatus.OPEN)
    priority: CasePriority = Field(default=CasePriority.MEDIUM)


# Properties to receive on legal case creation
class LegalCaseCreate(LegalCaseBase):
    lawyer_id: uuid.UUID  # Ajout de lawyer_id comme champ requis


# Properties to receive on legal case update
class LegalCaseUpdate(LegalCaseBase):
    title: str | None = Field(default=None, min_length=1, max_length=255)  # type: ignore
    client_name: str | None = Field(default=None, min_length=1, max_length=255)  # type: ignore
    case_type: CaseType | None = Field(default=None)  # type: ignore
    opening_date: date | None = Field(default=None)  # type: ignore
    description: str | None = Field(default=None, max_length=2000)
    status: CaseStatus | None = Field(default=None)  # type: ignore
    priority: CasePriority | None = Field(default=None)  # type: ignore


# Database model, database table inferred from class name
class LegalCase(LegalCaseBase, table=True):
    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    lawyer_id: uuid.UUID = Field(
        foreign_key="user.id", nullable=False, ondelete="CASCADE"
    )
    lawyer: User | None = Relationship(back_populates="legal_cases")

    # Client Portal relationships
    notifications: list["Notification"] = Relationship(back_populates="case", cascade_delete=True)
    messages: list["ClientMessage"] = Relationship(back_populates="case", cascade_delete=True)

    # Team Collaboration relationships
    team_members: list["CaseTeamMember"] = Relationship(back_populates="case", cascade_delete=True)
    team_messages: list["TeamMessage"] = Relationship(back_populates="case", cascade_delete=True)
    team_tasks: list["TeamTask"] = Relationship(back_populates="case", cascade_delete=True)

    # Document Analysis relationships
    documents: list["Document"] = Relationship(back_populates="legal_case", cascade_delete=True)

    # Override enum fields to use strings in the database
    status: str = Field(default="open")
    priority: str = Field(default="medium")


# Properties to return via API, id is always required
class LegalCasePublic(LegalCaseBase):
    id: uuid.UUID
    lawyer_id: uuid.UUID


# Enhanced version with lawyer information for list views
class LegalCaseWithLawyer(LegalCaseBase):
    id: uuid.UUID
    lawyer_id: uuid.UUID
    lawyer: UserPublic | None = None


class LegalCasesPublic(SQLModel):
    data: list[LegalCaseWithLawyer]
    count: int


# Case Activity Models
class ActivityType(str, Enum):
    CASE_CREATED = "case_created"
    CASE_UPDATED = "case_updated"
    STATUS_CHANGED = "status_changed"
    PRIORITY_CHANGED = "priority_changed"
    DOCUMENT_UPLOADED = "document_uploaded"
    DOCUMENT_DOWNLOADED = "document_downloaded"
    DOCUMENT_DELETED = "document_deleted"
    NOTE_ADDED = "note_added"
    NOTE_UPDATED = "note_updated"
    NOTE_DELETED = "note_deleted"
    USER_ASSIGNED = "user_assigned"
    MILESTONE_CREATED = "milestone_created"
    MILESTONE_UPDATED = "milestone_updated"
    MILESTONE_DELETED = "milestone_deleted"
    DEADLINE_CREATED = "deadline_created"
    DEADLINE_UPDATED = "deadline_updated"
    DEADLINE_DELETED = "deadline_deleted"
    MILESTONES_CREATED_FROM_TEMPLATE = "milestones_created_from_template"
    TEMPLATE_APPLIED = "template_applied"


# Case Notes Models
class NoteType(str, Enum):
    PRIVATE = "private"  # Lawyer only
    TEAM = "team"  # Case team access
    CLIENT = "client"  # Shared with clients
    ADMIN = "admin"  # System-wide access
    TASK = "task"  # Task assignments
    COMMUNICATION = "communication"  # Client communication logs


class NoteCategory(str, Enum):
    GENERAL = "general"
    RESEARCH = "research"
    MEETING = "meeting"
    DEADLINE = "deadline"
    STRATEGY = "strategy"
    EVIDENCE = "evidence"
    CORRESPONDENCE = "correspondence"
    BILLING = "billing"


# Document Categories
class DocumentCategory(str, Enum):
    EVIDENCE = "evidence"  # Photos, recordings, witness statements
    CONTRACTS = "contracts"  # Legal agreements, terms of service
    CORRESPONDENCE = "correspondence"  # Emails, letters, communications
    COURT_DOCUMENTS = "court_documents"  # Filings, orders, judgments
    RESEARCH = "research"  # Legal research, case law, precedents
    BILLING = "billing"  # Invoices, payment records
    INTERNAL = "internal"  # Internal notes, drafts
    OTHER = "other"  # Miscellaneous case-related documents


# AI Document Analysis Enums
class AnalysisStatus(str, Enum):
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    REQUIRES_VALIDATION = "requires_validation"


class ExtractionConfidence(str, Enum):
    LOW = "low"  # 0-40%
    MEDIUM = "medium"  # 40-70%
    HIGH = "high"  # 70-90%
    VERY_HIGH = "very_high"  # 90-100%


class ValidationStatus(str, Enum):
    PENDING = "pending"
    APPROVED = "approved"
    REJECTED = "rejected"
    MODIFIED = "modified"


class DocumentProcessingType(str, Enum):
    OCR_ONLY = "ocr_only"  # Just text extraction
    AI_ANALYSIS = "ai_analysis"  # Full AI analysis
    INCREMENTAL_UPDATE = "incremental_update"  # Update existing case


class CaseActivityBase(SQLModel):
    case_id: uuid.UUID = Field(foreign_key="legalcase.id")
    activity_type: ActivityType
    description: str
    user_id: uuid.UUID = Field(foreign_key="user.id")
    activity_metadata: dict | None = Field(default=None, sa_column=Column(JSON))


class CaseActivity(CaseActivityBase, table=True):
    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    created_at: date = Field(default_factory=date.today)

    # Override enum field to use string in the database
    activity_type: str


class CaseActivityPublic(CaseActivityBase):
    id: uuid.UUID
    created_at: date
    user: UserPublic | None = None


class CaseActivitiesPublic(SQLModel):
    data: list[CaseActivityPublic]
    count: int


# Case Notes Models
class CaseNoteBase(SQLModel):
    case_id: uuid.UUID = Field(foreign_key="legalcase.id")
    content: str = Field(min_length=1, max_length=5000)
    note_type: NoteType = Field(default=NoteType.PRIVATE)
    category: NoteCategory = Field(default=NoteCategory.GENERAL)
    is_pinned: bool = Field(default=False)
    tags: list[str] | None = Field(default=None, sa_column=Column(JSON))
    mentioned_users: list[str] | None = Field(default=None, sa_column=Column(JSON))
    parent_note_id: uuid.UUID | None = Field(default=None, foreign_key="casenote.id")


class CaseNote(CaseNoteBase, table=True):
    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    author_id: uuid.UUID = Field(foreign_key="user.id", nullable=False)
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)

    # Override enum fields to use strings in the database
    note_type: str = Field(default="private")
    category: str = Field(default="general")


class CaseNoteCreate(SQLModel):
    content: str = Field(min_length=1, max_length=5000)
    note_type: NoteType = Field(default=NoteType.PRIVATE)
    category: NoteCategory = Field(default=NoteCategory.GENERAL)
    is_pinned: bool = Field(default=False)
    tags: list[str] | None = Field(default=None)
    mentioned_users: list[str] | None = Field(default=None)
    parent_note_id: uuid.UUID | None = Field(default=None)


class CaseNoteUpdate(SQLModel):
    content: str | None = Field(default=None, min_length=1, max_length=5000)
    note_type: NoteType | None = Field(default=None)
    category: NoteCategory | None = Field(default=None)
    is_pinned: bool | None = Field(default=None)
    tags: list[str] | None = Field(default=None)


class CaseNotePublic(CaseNoteBase):
    id: uuid.UUID
    author_id: uuid.UUID
    created_at: datetime
    updated_at: datetime
    author: UserPublic | None = None
    replies_count: int = Field(default=0)
    can_edit: bool = Field(default=False)
    can_delete: bool = Field(default=False)


class CaseNotesPublic(SQLModel):
    data: list[CaseNotePublic]
    count: int


# Case Documents Models
class CaseDocumentBase(SQLModel):
    case_id: uuid.UUID = Field(foreign_key="legalcase.id")
    folder_id: uuid.UUID | None = Field(default=None, foreign_key="casefolder.id")
    filename: str = Field(min_length=1, max_length=255)
    original_filename: str = Field(min_length=1, max_length=255)
    file_path: str = Field(min_length=1, max_length=500)
    content_type: str = Field(min_length=1, max_length=100)
    file_size: int = Field(gt=0)
    category: DocumentCategory = Field(default=DocumentCategory.OTHER)
    description: str | None = Field(default=None, max_length=1000)
    tags: list[str] | None = Field(default=None, sa_column=Column(JSON))
    is_confidential: bool = Field(default=False)
    is_shared_with_client: bool = Field(default=False)


class CaseDocument(CaseDocumentBase, table=True):
    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    uploaded_by: uuid.UUID = Field(foreign_key="user.id", nullable=False)
    uploaded_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)

    # Override enum field to use string in the database
    category: str = Field(default="other")


class CaseDocumentCreate(SQLModel):
    folder_id: uuid.UUID | None = Field(default=None)
    filename: str = Field(min_length=1, max_length=255)
    original_filename: str = Field(min_length=1, max_length=255)
    file_path: str = Field(min_length=1, max_length=500)
    content_type: str = Field(min_length=1, max_length=100)
    file_size: int = Field(gt=0)
    category: DocumentCategory = Field(default=DocumentCategory.OTHER)
    description: str | None = Field(default=None, max_length=1000)
    tags: list[str] | None = Field(default=None)
    is_confidential: bool = Field(default=False)
    is_shared_with_client: bool = Field(default=False)


class CaseDocumentUpdate(SQLModel):
    folder_id: uuid.UUID | None = Field(default=None)
    category: DocumentCategory | None = Field(default=None)
    description: str | None = Field(default=None, max_length=1000)
    tags: list[str] | None = Field(default=None)
    is_confidential: bool | None = Field(default=None)
    is_shared_with_client: bool | None = Field(default=None)


class CaseDocumentPublic(CaseDocumentBase):
    id: uuid.UUID
    uploaded_by: uuid.UUID
    uploaded_at: datetime
    updated_at: datetime
    uploader: UserPublic | None = None
    folder_path: list[str] = Field(default=[])  # Breadcrumb path
    linked_cases: list["DocumentCaseLinkPublic"] = Field(default=[])  # Case links
    can_edit: bool = Field(default=False)
    can_delete: bool = Field(default=False)
    can_download: bool = Field(default=False)
    download_url: str | None = Field(default=None)


class CaseDocumentsPublic(SQLModel):
    data: list[CaseDocumentPublic]
    count: int


# Document-Case Linking Models
class DocumentCaseLink(SQLModel, table=True):
    """Many-to-many relationship between documents and cases"""
    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    document_id: uuid.UUID = Field(foreign_key="casedocument.id", nullable=False)
    case_id: uuid.UUID = Field(foreign_key="legalcase.id", nullable=False)
    linked_by: uuid.UUID = Field(foreign_key="user.id", nullable=False)
    linked_at: datetime = Field(default_factory=datetime.utcnow)
    link_type: str = Field(default="related", max_length=50)  # related, evidence, reference, etc.
    notes: str | None = Field(default=None, max_length=1000)

    # Ensure unique document-case pairs
    __table_args__ = (
        UniqueConstraint("document_id", "case_id", name="unique_document_case_link"),
    )


class DocumentCaseLinkCreate(SQLModel):
    case_id: uuid.UUID
    link_type: str = Field(default="related", max_length=50)
    notes: str | None = Field(default=None, max_length=1000)


class DocumentCaseLinkUpdate(SQLModel):
    link_type: str | None = Field(default=None, max_length=50)
    notes: str | None = Field(default=None, max_length=1000)


class DocumentCaseLinkPublic(SQLModel):
    id: uuid.UUID
    document_id: uuid.UUID
    case_id: uuid.UUID
    linked_by: uuid.UUID
    linked_at: datetime
    link_type: str
    notes: str | None = None
    case: LegalCasePublic | None = None
    linked_by_user: UserPublic | None = None


class DocumentCaseLinksPublic(SQLModel):
    data: list[DocumentCaseLinkPublic]
    count: int


# Case Folders Models
class CaseFolderBase(SQLModel):
    case_id: uuid.UUID = Field(foreign_key="legalcase.id")
    name: str = Field(min_length=1, max_length=255)
    description: str | None = Field(default=None, max_length=1000)
    parent_folder_id: uuid.UUID | None = Field(default=None, foreign_key="casefolder.id")
    color: str | None = Field(default=None, max_length=7)  # Hex color code
    is_system_folder: bool = Field(default=False)  # For auto-created folders
    folder_order: int = Field(default=0)  # For custom ordering


class CaseFolder(CaseFolderBase, table=True):
    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    created_by: uuid.UUID = Field(foreign_key="user.id", nullable=False)
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)


class CaseFolderCreate(SQLModel):
    name: str = Field(min_length=1, max_length=255)
    description: str | None = Field(default=None, max_length=1000)
    parent_folder_id: uuid.UUID | None = Field(default=None)
    color: str | None = Field(default=None, max_length=7)


class CaseFolderUpdate(SQLModel):
    name: str | None = Field(default=None, min_length=1, max_length=255)
    description: str | None = Field(default=None, max_length=1000)
    parent_folder_id: uuid.UUID | None = Field(default=None)
    color: str | None = Field(default=None, max_length=7)
    folder_order: int | None = Field(default=None)


class CaseFolderPublic(CaseFolderBase):
    id: uuid.UUID
    created_by: uuid.UUID
    created_at: datetime
    updated_at: datetime
    creator: UserPublic | None = None
    document_count: int = Field(default=0)
    subfolder_count: int = Field(default=0)
    total_size: int = Field(default=0)
    can_edit: bool = Field(default=False)
    can_delete: bool = Field(default=False)
    path: list[str] = Field(default=[])  # Breadcrumb path


class CaseFoldersPublic(SQLModel):
    data: list[CaseFolderPublic]
    count: int


# Case Status History Models
class CaseStatusHistoryBase(SQLModel):
    case_id: uuid.UUID = Field(foreign_key="legalcase.id")
    old_status: str | None = Field(default=None)
    new_status: str
    changed_by: uuid.UUID = Field(foreign_key="user.id")
    notes: str | None = Field(default=None, max_length=1000)


class CaseStatusHistory(CaseStatusHistoryBase, table=True):
    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    changed_at: date = Field(default_factory=date.today)


class CaseStatusHistoryPublic(CaseStatusHistoryBase):
    id: uuid.UUID
    changed_at: date
    changed_by_user: UserPublic | None = None


class CaseStatusHistoriesPublic(SQLModel):
    data: list[CaseStatusHistoryPublic]
    count: int


# Status Change Request Models
class StatusChangeRequest(SQLModel):
    new_status: CaseStatus
    notes: str | None = Field(default=None, max_length=1000)


# Enhanced case detail model with lawyer and activity info
class LegalCaseDetail(LegalCaseBase):
    id: uuid.UUID
    lawyer_id: uuid.UUID
    lawyer: UserPublic | None = None
    recent_activities: list[CaseActivityPublic] = []
    status_history: list[CaseStatusHistoryPublic] = []


# Generic message
class Message(SQLModel):
    message: str


# JSON payload containing access token
class Token(SQLModel):
    access_token: str
    token_type: str = "bearer"
    role: str | None = None


# Contents of JWT token
class TokenPayload(SQLModel):
    sub: str  # | None = None
    role: UserRole  # | None = None


class NewPassword(SQLModel):
    token: str
    new_password: str = Field(min_length=8, max_length=40)


# Case Progress Tracking Models

class MilestoneStatus(str, Enum):
    NOT_STARTED = "not_started"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    OVERDUE = "overdue"
    CANCELLED = "cancelled"


class MilestoneType(str, Enum):
    CASE_OPENING = "case_opening"
    DISCOVERY = "discovery"
    FILING = "filing"
    HEARING = "hearing"
    NEGOTIATION = "negotiation"
    TRIAL = "trial"
    SETTLEMENT = "settlement"
    APPEAL = "appeal"
    CASE_CLOSURE = "case_closure"
    CUSTOM = "custom"


class DeadlineType(str, Enum):
    COURT_DEADLINE = "court_deadline"
    INTERNAL_DEADLINE = "internal_deadline"
    CLIENT_DEADLINE = "client_deadline"
    STATUTE_LIMITATION = "statute_limitation"
    FILING_DEADLINE = "filing_deadline"
    RESPONSE_DEADLINE = "response_deadline"
    DISCOVERY_DEADLINE = "discovery_deadline"
    OTHER = "other"


class CaseMilestoneBase(SQLModel):
    case_id: uuid.UUID = Field(foreign_key="legalcase.id")
    title: str = Field(min_length=1, max_length=255)
    description: str | None = Field(default=None, max_length=1000)
    milestone_type: MilestoneType = Field(default=MilestoneType.CUSTOM)
    status: MilestoneStatus = Field(default=MilestoneStatus.NOT_STARTED)
    target_date: date | None = Field(default=None)
    completion_date: date | None = Field(default=None)
    order_index: int = Field(default=0, description="Order of milestone in case progression")
    is_required: bool = Field(default=False, description="Whether this milestone is required for case completion")
    assigned_to: uuid.UUID | None = Field(default=None, foreign_key="user.id")
    progress_percentage: int = Field(default=0, ge=0, le=100)
    notes: str | None = Field(default=None, max_length=2000)


class CaseMilestone(CaseMilestoneBase, table=True):
    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    created_by: uuid.UUID = Field(foreign_key="user.id")

    # Override enum fields to use strings in the database
    milestone_type: str = Field(default="custom")
    status: str = Field(default="not_started")


class CaseMilestoneCreate(SQLModel):
    title: str = Field(min_length=1, max_length=255)
    description: str | None = Field(default=None, max_length=1000)
    milestone_type: MilestoneType = Field(default=MilestoneType.CUSTOM)
    target_date: date | None = Field(default=None)
    order_index: int = Field(default=0)
    is_required: bool = Field(default=False)
    assigned_to: uuid.UUID | None = Field(default=None)
    notes: str | None = Field(default=None, max_length=2000)


class CaseMilestoneUpdate(SQLModel):
    title: str | None = Field(default=None, min_length=1, max_length=255)
    description: str | None = Field(default=None, max_length=1000)
    milestone_type: MilestoneType | None = Field(default=None)
    status: MilestoneStatus | None = Field(default=None)
    target_date: date | None = Field(default=None)
    completion_date: date | None = Field(default=None)
    order_index: int | None = Field(default=None)
    is_required: bool | None = Field(default=None)
    assigned_to: uuid.UUID | None = Field(default=None)
    progress_percentage: int | None = Field(default=None, ge=0, le=100)
    notes: str | None = Field(default=None, max_length=2000)


class CaseMilestonePublic(CaseMilestoneBase):
    id: uuid.UUID
    created_at: datetime
    updated_at: datetime
    created_by: uuid.UUID
    assigned_user: UserPublic | None = None
    creator: UserPublic | None = None


class CaseMilestonesPublic(SQLModel):
    data: list[CaseMilestonePublic]
    count: int


class CaseDeadlineBase(SQLModel):
    case_id: uuid.UUID = Field(foreign_key="legalcase.id")
    title: str = Field(min_length=1, max_length=255)
    description: str | None = Field(default=None, max_length=1000)
    deadline_type: DeadlineType = Field(default=DeadlineType.OTHER)
    deadline_date: datetime
    is_critical: bool = Field(default=False, description="Whether missing this deadline has serious consequences")
    reminder_days: list[int] = Field(default_factory=lambda: [7, 3, 1], description="Days before deadline to send reminders", sa_column=Column(JSON))
    assigned_to: uuid.UUID | None = Field(default=None, foreign_key="user.id")
    is_completed: bool = Field(default=False)
    completion_date: datetime | None = Field(default=None)
    notes: str | None = Field(default=None, max_length=2000)


class CaseDeadline(CaseDeadlineBase, table=True):
    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    created_by: uuid.UUID = Field(foreign_key="user.id")

    # Override enum field to use string in the database
    deadline_type: str = Field(default="other")


class CaseDeadlineCreate(SQLModel):
    title: str = Field(min_length=1, max_length=255)
    description: str | None = Field(default=None, max_length=1000)
    deadline_type: DeadlineType = Field(default=DeadlineType.OTHER)
    deadline_date: datetime
    is_critical: bool = Field(default=False)
    reminder_days: list[int] = Field(default_factory=lambda: [7, 3, 1])
    assigned_to: uuid.UUID | None = Field(default=None)
    notes: str | None = Field(default=None, max_length=2000)


class CaseDeadlineUpdate(SQLModel):
    title: str | None = Field(default=None, min_length=1, max_length=255)
    description: str | None = Field(default=None, max_length=1000)
    deadline_type: DeadlineType | None = Field(default=None)
    deadline_date: datetime | None = Field(default=None)
    is_critical: bool | None = Field(default=None)
    reminder_days: list[int] | None = Field(default=None)
    assigned_to: uuid.UUID | None = Field(default=None)
    is_completed: bool | None = Field(default=None)
    completion_date: datetime | None = Field(default=None)
    notes: str | None = Field(default=None, max_length=2000)


class CaseDeadlinePublic(CaseDeadlineBase):
    id: uuid.UUID
    created_at: datetime
    updated_at: datetime
    created_by: uuid.UUID
    assigned_user: UserPublic | None = None
    creator: UserPublic | None = None


class CaseDeadlinesPublic(SQLModel):
    data: list[CaseDeadlinePublic]
    count: int


# Case Progress Summary Models
class CaseProgressSummary(SQLModel):
    case_id: uuid.UUID
    total_milestones: int
    completed_milestones: int
    progress_percentage: float
    overdue_milestones: int
    upcoming_deadlines: int
    critical_deadlines: int
    next_milestone: CaseMilestonePublic | None = None
    next_deadline: CaseDeadlinePublic | None = None


# Milestone Template Models for different case types
class MilestoneTemplateBase(SQLModel):
    case_type: CaseType
    title: str = Field(min_length=1, max_length=255)
    description: str | None = Field(default=None, max_length=1000)
    milestone_type: MilestoneType = Field(default=MilestoneType.CUSTOM)
    order_index: int = Field(default=0)
    is_required: bool = Field(default=False)
    estimated_days: int | None = Field(default=None, description="Estimated days from case opening")
    notes: str | None = Field(default=None, max_length=2000)


class MilestoneTemplate(MilestoneTemplateBase, table=True):
    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    created_at: datetime = Field(default_factory=datetime.utcnow)
    created_by: uuid.UUID = Field(foreign_key="user.id")
    is_active: bool = Field(default=True)

    # Override enum fields to use strings in the database
    case_type: str = Field(default="other")
    milestone_type: str = Field(default="custom")


class MilestoneTemplateCreate(SQLModel):
    case_type: CaseType
    title: str = Field(min_length=1, max_length=255)
    description: str | None = Field(default=None, max_length=1000)
    milestone_type: MilestoneType = Field(default=MilestoneType.CUSTOM)
    order_index: int = Field(default=0)
    is_required: bool = Field(default=False)
    estimated_days: int | None = Field(default=None)
    notes: str | None = Field(default=None, max_length=2000)


class MilestoneTemplatePublic(MilestoneTemplateBase):
    id: uuid.UUID
    created_at: datetime
    created_by: uuid.UUID
    is_active: bool
    creator: UserPublic | None = None


class MilestoneTemplatesPublic(SQLModel):
    data: list[MilestoneTemplatePublic]
    count: int


# Case Template Models for complete case automation
class CaseTemplateBase(SQLModel):
    name: str = Field(min_length=1, max_length=255)
    description: str | None = Field(default=None, max_length=1000)
    case_type: CaseType
    template_data: dict = Field(default_factory=dict, description="JSON data for template configuration", sa_column=Column(JSON))
    is_active: bool = Field(default=True)
    is_public: bool = Field(default=False, description="Whether template is available to all users")
    tags: list[str] = Field(default_factory=list, description="Tags for template categorization", sa_column=Column(JSON))


class CaseTemplate(CaseTemplateBase, table=True):
    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    created_by: uuid.UUID = Field(foreign_key="user.id", nullable=False)
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)

    # Relationships
    creator: User | None = Relationship(back_populates="case_templates")


class CaseTemplateCreate(SQLModel):
    name: str = Field(min_length=1, max_length=255)
    description: str | None = Field(default=None, max_length=1000)
    case_type: CaseType
    template_data: dict = Field(default_factory=dict)
    is_active: bool = Field(default=True)
    is_public: bool = Field(default=False)
    tags: list[str] = Field(default_factory=list)


class CaseTemplateUpdate(SQLModel):
    name: str | None = Field(default=None, min_length=1, max_length=255)
    description: str | None = Field(default=None, max_length=1000)
    case_type: CaseType | None = Field(default=None)
    template_data: dict | None = Field(default=None)
    is_active: bool | None = Field(default=None)
    is_public: bool | None = Field(default=None)
    tags: list[str] | None = Field(default=None)


class CaseTemplatePublic(CaseTemplateBase):
    id: uuid.UUID
    created_by: uuid.UUID
    created_at: datetime
    updated_at: datetime
    creator: UserPublic | None = None


class CaseTemplatesPublic(SQLModel):
    data: list[CaseTemplatePublic]
    count: int


# Document Template Models
class DocumentTemplateBase(SQLModel):
    name: str = Field(min_length=1, max_length=255)
    description: str | None = Field(default=None, max_length=1000)
    case_type: CaseType | None = Field(default=None, description="Associated case type, null for universal")
    document_type: str = Field(max_length=100, description="Type of document (contract, letter, etc.)")
    template_content: str = Field(description="Template content with placeholders")
    placeholders: list[str] = Field(default_factory=list, description="List of available placeholders", sa_column=Column(JSON))
    is_active: bool = Field(default=True)
    is_public: bool = Field(default=False)
    tags: list[str] = Field(default_factory=list, sa_column=Column(JSON))


class DocumentTemplate(DocumentTemplateBase, table=True):
    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    created_by: uuid.UUID = Field(foreign_key="user.id", nullable=False)
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)

    # Relationships
    creator: User | None = Relationship(back_populates="document_templates")


class DocumentTemplateCreate(SQLModel):
    name: str = Field(min_length=1, max_length=255)
    description: str | None = Field(default=None, max_length=1000)
    case_type: CaseType | None = Field(default=None)
    document_type: str = Field(max_length=100)
    template_content: str
    placeholders: list[str] = Field(default_factory=list)
    is_active: bool = Field(default=True)
    is_public: bool = Field(default=False)
    tags: list[str] = Field(default_factory=list)


class DocumentTemplateUpdate(SQLModel):
    name: str | None = Field(default=None, min_length=1, max_length=255)
    description: str | None = Field(default=None, max_length=1000)
    case_type: CaseType | None = Field(default=None)
    document_type: str | None = Field(default=None, max_length=100)
    template_content: str | None = Field(default=None)
    placeholders: list[str] | None = Field(default=None)
    is_active: bool | None = Field(default=None)
    is_public: bool | None = Field(default=None)
    tags: list[str] | None = Field(default=None)


class DocumentTemplatePublic(DocumentTemplateBase):
    id: uuid.UUID
    created_by: uuid.UUID
    created_at: datetime
    updated_at: datetime
    creator: UserPublic | None = None


class DocumentTemplatesPublic(SQLModel):
    data: list[DocumentTemplatePublic]
    count: int


# Workflow Template Models
class WorkflowTemplateBase(SQLModel):
    name: str = Field(min_length=1, max_length=255)
    description: str | None = Field(default=None, max_length=1000)
    case_type: CaseType
    workflow_steps: list[dict] = Field(default_factory=list, description="Ordered list of workflow steps", sa_column=Column(JSON))
    automation_rules: list[dict] = Field(default_factory=list, description="Automation rules and triggers", sa_column=Column(JSON))
    is_active: bool = Field(default=True)
    is_public: bool = Field(default=False)
    tags: list[str] = Field(default_factory=list, sa_column=Column(JSON))


class WorkflowTemplate(WorkflowTemplateBase, table=True):
    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    created_by: uuid.UUID = Field(foreign_key="user.id", nullable=False)
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)

    # Relationships
    creator: User | None = Relationship(back_populates="workflow_templates")


class WorkflowTemplateCreate(SQLModel):
    name: str = Field(min_length=1, max_length=255)
    description: str | None = Field(default=None, max_length=1000)
    case_type: CaseType
    workflow_steps: list[dict] = Field(default_factory=list)
    automation_rules: list[dict] = Field(default_factory=list)
    is_active: bool = Field(default=True)
    is_public: bool = Field(default=False)
    tags: list[str] = Field(default_factory=list)


class WorkflowTemplateUpdate(SQLModel):
    name: str | None = Field(default=None, min_length=1, max_length=255)
    description: str | None = Field(default=None, max_length=1000)
    case_type: CaseType | None = Field(default=None)
    workflow_steps: list[dict] | None = Field(default=None)
    automation_rules: list[dict] | None = Field(default=None)
    is_active: bool | None = Field(default=None)
    is_public: bool | None = Field(default=None)
    tags: list[str] | None = Field(default=None)


class WorkflowTemplatePublic(WorkflowTemplateBase):
    id: uuid.UUID
    created_by: uuid.UUID
    created_at: datetime
    updated_at: datetime
    creator: UserPublic | None = None


class WorkflowTemplatesPublic(SQLModel):
    data: list[WorkflowTemplatePublic]
    count: int


# Client Portal Models

# Notification System
class NotificationType(str, Enum):
    CASE_UPDATE = "case_update"
    DOCUMENT_SHARED = "document_shared"
    APPOINTMENT_REMINDER = "appointment_reminder"
    DEADLINE_APPROACHING = "deadline_approaching"
    MESSAGE_RECEIVED = "message_received"
    STATUS_CHANGE = "status_change"
    MILESTONE_COMPLETED = "milestone_completed"
    SYSTEM_ANNOUNCEMENT = "system_announcement"


class NotificationPriority(str, Enum):
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    URGENT = "urgent"


class NotificationBase(SQLModel):
    title: str = Field(max_length=255)
    message: str = Field(max_length=1000)
    notification_type: NotificationType
    priority: NotificationPriority = Field(default=NotificationPriority.MEDIUM)
    is_read: bool = Field(default=False)
    notification_metadata: dict = Field(default_factory=dict, sa_column=Column(JSON))
    expires_at: datetime | None = Field(default=None)


class Notification(NotificationBase, table=True):
    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    user_id: uuid.UUID = Field(foreign_key="user.id", nullable=False)
    case_id: uuid.UUID | None = Field(foreign_key="legalcase.id", nullable=True)
    created_at: datetime = Field(default_factory=datetime.utcnow)
    read_at: datetime | None = Field(default=None)

    # Relationships
    user: Optional[User] = Relationship(back_populates="notifications")
    case: Optional["LegalCase"] = Relationship(back_populates="notifications")


class NotificationCreate(SQLModel):
    title: str = Field(max_length=255)
    message: str = Field(max_length=1000)
    notification_type: NotificationType
    priority: NotificationPriority = Field(default=NotificationPriority.MEDIUM)
    user_id: uuid.UUID
    case_id: uuid.UUID | None = Field(default=None)
    notification_metadata: dict = Field(default_factory=dict)
    expires_at: datetime | None = Field(default=None)


class NotificationUpdate(SQLModel):
    is_read: bool | None = Field(default=None)
    read_at: datetime | None = Field(default=None)


class NotificationPublic(NotificationBase):
    id: uuid.UUID
    user_id: uuid.UUID
    case_id: uuid.UUID | None
    created_at: datetime
    read_at: datetime | None
    case: Optional["LegalCasePublic"] = None


class NotificationsPublic(SQLModel):
    data: list[NotificationPublic]
    count: int
    unread_count: int


# Extended Notification System Models

# Notification Delivery Status
class NotificationDeliveryStatus(str, Enum):
    PENDING = "pending"
    SENT = "sent"
    DELIVERED = "delivered"
    FAILED = "failed"
    BOUNCED = "bounced"
    OPENED = "opened"
    CLICKED = "clicked"


# Notification Channel
class NotificationChannel(str, Enum):
    IN_APP = "in_app"
    EMAIL = "email"
    SMS = "sms"
    PUSH = "push"
    WEBHOOK = "webhook"


# User Notification Preferences
class NotificationPreferencesBase(SQLModel):
    user_id: uuid.UUID = Field(foreign_key="user.id")
    notification_type: NotificationType
    channels: list[NotificationChannel] = Field(default_factory=list, sa_column=Column(JSON))
    is_enabled: bool = Field(default=True)
    frequency: str = Field(default="immediate")  # immediate, daily, weekly, never
    quiet_hours_start: str | None = Field(default=None)  # HH:MM format
    quiet_hours_end: str | None = Field(default=None)  # HH:MM format
    priority_threshold: NotificationPriority = Field(default=NotificationPriority.LOW)


class NotificationPreferences(NotificationPreferencesBase, table=True):
    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)

    # Relationships
    user: Optional[User] = Relationship()

    # Unique constraint to prevent duplicate preferences
    __table_args__ = (UniqueConstraint("user_id", "notification_type", name="unique_user_notification_type"),)


class NotificationPreferencesCreate(SQLModel):
    notification_type: NotificationType
    channels: list[NotificationChannel] = Field(default_factory=list)
    is_enabled: bool = Field(default=True)
    frequency: str = Field(default="immediate")
    quiet_hours_start: str | None = Field(default=None)
    quiet_hours_end: str | None = Field(default=None)
    priority_threshold: NotificationPriority = Field(default=NotificationPriority.LOW)


class NotificationPreferencesUpdate(SQLModel):
    channels: list[NotificationChannel] | None = Field(default=None)
    is_enabled: bool | None = Field(default=None)
    frequency: str | None = Field(default=None)
    quiet_hours_start: str | None = Field(default=None)
    quiet_hours_end: str | None = Field(default=None)
    priority_threshold: NotificationPriority | None = Field(default=None)


class NotificationPreferencesPublic(NotificationPreferencesBase):
    id: uuid.UUID
    created_at: datetime
    updated_at: datetime


class UserNotificationPreferencesPublic(SQLModel):
    data: list[NotificationPreferencesPublic]
    count: int


# Notification Templates
class NotificationTemplateBase(SQLModel):
    name: str = Field(max_length=255)
    description: str | None = Field(default=None, max_length=500)
    notification_type: NotificationType
    channel: NotificationChannel
    subject_template: str = Field(max_length=255)
    body_template: str = Field(max_length=5000)
    variables: list[str] = Field(default_factory=list, sa_column=Column(JSON))
    is_active: bool = Field(default=True)
    is_system: bool = Field(default=False)  # System templates can't be deleted
    template_metadata: dict = Field(default_factory=dict, sa_column=Column(JSON))


class NotificationTemplate(NotificationTemplateBase, table=True):
    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    created_by: uuid.UUID = Field(foreign_key="user.id", nullable=False)
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)

    # Relationships
    creator: Optional[User] = Relationship()


class NotificationTemplateCreate(SQLModel):
    name: str = Field(max_length=255)
    description: str | None = Field(default=None, max_length=500)
    notification_type: NotificationType
    channel: NotificationChannel
    subject_template: str = Field(max_length=255)
    body_template: str = Field(max_length=5000)
    variables: list[str] = Field(default_factory=list)
    template_metadata: dict = Field(default_factory=dict)


class NotificationTemplateUpdate(SQLModel):
    name: str | None = Field(default=None, max_length=255)
    description: str | None = Field(default=None, max_length=500)
    subject_template: str | None = Field(default=None, max_length=255)
    body_template: str | None = Field(default=None, max_length=5000)
    variables: list[str] | None = Field(default=None)
    is_active: bool | None = Field(default=None)
    template_metadata: dict | None = Field(default=None)


class NotificationTemplatePublic(NotificationTemplateBase):
    id: uuid.UUID
    created_by: uuid.UUID
    created_at: datetime
    updated_at: datetime
    creator: Optional[UserPublic] = None


class NotificationTemplatesPublic(SQLModel):
    data: list[NotificationTemplatePublic]
    count: int


# Notification Delivery Log
class NotificationDeliveryBase(SQLModel):
    notification_id: uuid.UUID = Field(foreign_key="notification.id")
    channel: NotificationChannel
    status: NotificationDeliveryStatus = Field(default=NotificationDeliveryStatus.PENDING)
    recipient_address: str = Field(max_length=255)  # email, phone, etc.
    delivery_metadata: dict = Field(default_factory=dict, sa_column=Column(JSON))
    error_message: str | None = Field(default=None, max_length=1000)
    external_id: str | None = Field(default=None, max_length=255)  # Provider message ID


class NotificationDelivery(NotificationDeliveryBase, table=True):
    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    sent_at: datetime | None = Field(default=None)
    delivered_at: datetime | None = Field(default=None)
    opened_at: datetime | None = Field(default=None)
    clicked_at: datetime | None = Field(default=None)
    failed_at: datetime | None = Field(default=None)
    created_at: datetime = Field(default_factory=datetime.utcnow)

    # Relationships
    notification: Optional[Notification] = Relationship()


class NotificationDeliveryPublic(NotificationDeliveryBase):
    id: uuid.UUID
    sent_at: datetime | None
    delivered_at: datetime | None
    opened_at: datetime | None
    clicked_at: datetime | None
    failed_at: datetime | None
    created_at: datetime


# Notification Rules
class NotificationRuleBase(SQLModel):
    name: str = Field(max_length=255)
    description: str | None = Field(default=None, max_length=500)
    notification_type: NotificationType
    target_roles: list[str] = Field(default_factory=list, sa_column=Column(JSON))
    conditions: dict = Field(default_factory=dict, sa_column=Column(JSON))
    template_id: uuid.UUID | None = Field(foreign_key="notificationtemplate.id", nullable=True)
    priority: NotificationPriority = Field(default=NotificationPriority.MEDIUM)
    is_active: bool = Field(default=True)
    rule_metadata: dict = Field(default_factory=dict, sa_column=Column(JSON))


class NotificationRule(NotificationRuleBase, table=True):
    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    created_by: uuid.UUID = Field(foreign_key="user.id", nullable=False)
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)

    # Relationships
    creator: Optional[User] = Relationship()
    template: Optional[NotificationTemplate] = Relationship()


class NotificationRuleCreate(SQLModel):
    name: str = Field(max_length=255)
    description: str | None = Field(default=None, max_length=500)
    notification_type: NotificationType
    target_roles: list[str] = Field(default_factory=list)
    conditions: dict = Field(default_factory=dict)
    template_id: uuid.UUID | None = Field(default=None)
    priority: NotificationPriority = Field(default=NotificationPriority.MEDIUM)
    rule_metadata: dict = Field(default_factory=dict)


class NotificationRuleUpdate(SQLModel):
    name: str | None = Field(default=None, max_length=255)
    description: str | None = Field(default=None, max_length=500)
    target_roles: list[str] | None = Field(default=None)
    conditions: dict | None = Field(default=None)
    template_id: uuid.UUID | None = Field(default=None)
    priority: NotificationPriority | None = Field(default=None)
    is_active: bool | None = Field(default=None)
    rule_metadata: dict | None = Field(default=None)


class NotificationRulePublic(NotificationRuleBase):
    id: uuid.UUID
    created_by: uuid.UUID
    created_at: datetime
    updated_at: datetime
    creator: Optional[UserPublic] = None
    template: Optional[NotificationTemplatePublic] = None


class NotificationRulesPublic(SQLModel):
    data: list[NotificationRulePublic]
    count: int


# Client Messaging System
class MessageStatus(str, Enum):
    SENT = "sent"
    DELIVERED = "delivered"
    READ = "read"


class ClientMessageBase(SQLModel):
    subject: str = Field(max_length=255)
    content: str = Field(max_length=5000)
    is_confidential: bool = Field(default=False)
    attachments: list[str] = Field(default_factory=list, sa_column=Column(JSON))
    message_metadata: dict = Field(default_factory=dict, sa_column=Column(JSON))


class ClientMessage(ClientMessageBase, table=True):
    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    sender_id: uuid.UUID = Field(foreign_key="user.id", nullable=False)
    recipient_id: uuid.UUID = Field(foreign_key="user.id", nullable=False)
    case_id: uuid.UUID | None = Field(foreign_key="legalcase.id", nullable=True)
    parent_message_id: uuid.UUID | None = Field(foreign_key="clientmessage.id", nullable=True)
    status: MessageStatus = Field(default=MessageStatus.SENT)
    created_at: datetime = Field(default_factory=datetime.utcnow)
    read_at: datetime | None = Field(default=None)

    # Relationships
    sender: Optional[User] = Relationship(
        back_populates="sent_messages",
        sa_relationship_kwargs={"foreign_keys": "[ClientMessage.sender_id]"}
    )
    recipient: Optional[User] = Relationship(
        back_populates="received_messages",
        sa_relationship_kwargs={"foreign_keys": "[ClientMessage.recipient_id]"}
    )
    case: Optional["LegalCase"] = Relationship(back_populates="messages")
    parent_message: Optional["ClientMessage"] = Relationship(
        back_populates="replies",
        sa_relationship_kwargs={"remote_side": "[ClientMessage.id]"}
    )
    replies: list["ClientMessage"] = Relationship(
        back_populates="parent_message",
        sa_relationship_kwargs={"remote_side": "[ClientMessage.parent_message_id]"}
    )


class ClientMessageCreate(SQLModel):
    subject: str = Field(max_length=255)
    content: str = Field(max_length=5000)
    recipient_id: uuid.UUID
    case_id: uuid.UUID | None = Field(default=None)
    parent_message_id: uuid.UUID | None = Field(default=None)
    is_confidential: bool = Field(default=False)
    attachments: list[str] = Field(default_factory=list)


class ClientMessageUpdate(SQLModel):
    status: MessageStatus | None = Field(default=None)
    read_at: datetime | None = Field(default=None)


class ClientMessagePublic(ClientMessageBase):
    id: uuid.UUID
    sender_id: uuid.UUID
    recipient_id: uuid.UUID
    case_id: uuid.UUID | None
    parent_message_id: uuid.UUID | None
    status: MessageStatus
    created_at: datetime
    read_at: datetime | None
    sender: Optional[UserPublic] = None
    recipient: Optional[UserPublic] = None
    case: Optional["LegalCasePublic"] = None
    replies_count: int = 0
    can_reply: bool = True


class ClientMessagesPublic(SQLModel):
    data: list[ClientMessagePublic]
    count: int
    unread_count: int


# Client Portal Dashboard
class ClientDashboardStats(SQLModel):
    active_cases: int
    total_documents: int
    unread_messages: int
    unread_notifications: int
    upcoming_appointments: int
    pending_deadlines: int


# Client Case Access (Limited view for clients)
class ClientCaseView(SQLModel):
    id: uuid.UUID
    title: str
    case_type: CaseType
    status: CaseStatus
    opening_date: date
    description: str | None = None
    next_hearing_date: date | None = None
    lawyer: Optional[UserPublic] = None
    recent_updates: list[str] = Field(default_factory=list)
    shared_documents_count: int = 0
    unread_messages_count: int = 0


class ClientCasesPublic(SQLModel):
    data: list[ClientCaseView]
    count: int


# Team Collaboration Models

# Team Member Roles for Cases
class TeamMemberRole(str, Enum):
    LEAD_LAWYER = "lead_lawyer"
    ASSOCIATE_LAWYER = "associate_lawyer"
    LEGAL_ASSISTANT = "legal_assistant"
    PARALEGAL = "paralegal"
    ADMINISTRATIVE_STAFF = "administrative_staff"
    EXTERNAL_CONSULTANT = "external_consultant"
    OBSERVER = "observer"


class CaseTeamMemberBase(SQLModel):
    case_id: uuid.UUID = Field(foreign_key="legalcase.id")
    user_id: uuid.UUID = Field(foreign_key="user.id")
    role: TeamMemberRole
    permissions: list[str] = Field(default_factory=list, sa_column=Column(JSON))
    is_active: bool = Field(default=True)
    notes: str | None = Field(default=None, max_length=500)


class CaseTeamMember(CaseTeamMemberBase, table=True):
    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    assigned_at: datetime = Field(default_factory=datetime.utcnow)
    assigned_by: uuid.UUID = Field(foreign_key="user.id")

    # Relationships
    case: Optional["LegalCase"] = Relationship(back_populates="team_members")
    user: Optional[User] = Relationship(
        back_populates="team_memberships",
        sa_relationship_kwargs={"foreign_keys": "[CaseTeamMember.user_id]"}
    )
    assigner: Optional[User] = Relationship(
        back_populates="team_assignments",
        sa_relationship_kwargs={"foreign_keys": "[CaseTeamMember.assigned_by]"}
    )

    # Unique constraint to prevent duplicate assignments
    __table_args__ = (UniqueConstraint("case_id", "user_id", name="unique_case_user"),)


class CaseTeamMemberCreate(SQLModel):
    user_id: uuid.UUID
    role: TeamMemberRole
    permissions: list[str] = Field(default_factory=list)
    notes: str | None = Field(default=None, max_length=500)


class CaseTeamMemberUpdate(SQLModel):
    role: TeamMemberRole | None = Field(default=None)
    permissions: list[str] | None = Field(default=None)
    is_active: bool | None = Field(default=None)
    notes: str | None = Field(default=None, max_length=500)


class CaseTeamMemberPublic(CaseTeamMemberBase):
    id: uuid.UUID
    assigned_at: datetime
    assigned_by: uuid.UUID
    user: Optional[UserPublic] = None
    assigner: Optional[UserPublic] = None


class CaseTeamMembersPublic(SQLModel):
    data: list[CaseTeamMemberPublic]
    count: int


# Internal Team Messaging
class TeamMessageType(str, Enum):
    GENERAL = "general"
    CASE_DISCUSSION = "case_discussion"
    TASK_ASSIGNMENT = "task_assignment"
    ANNOUNCEMENT = "announcement"
    URGENT = "urgent"


class TeamMessageBase(SQLModel):
    case_id: uuid.UUID | None = Field(foreign_key="legalcase.id", nullable=True)
    message_type: TeamMessageType = Field(default=TeamMessageType.GENERAL)
    subject: str = Field(max_length=255)
    content: str = Field(max_length=5000)
    mentions: list[uuid.UUID] = Field(default_factory=list, sa_column=Column(JSON))
    attachments: list[str] = Field(default_factory=list, sa_column=Column(JSON))
    is_pinned: bool = Field(default=False)
    is_urgent: bool = Field(default=False)


class TeamMessage(TeamMessageBase, table=True):
    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    sender_id: uuid.UUID = Field(foreign_key="user.id", nullable=False)
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)

    # Relationships
    sender: Optional[User] = Relationship(
        back_populates="sent_team_messages",
        sa_relationship_kwargs={"foreign_keys": "[TeamMessage.sender_id]"}
    )
    case: Optional["LegalCase"] = Relationship(back_populates="team_messages")
    replies: list["TeamMessageReply"] = Relationship(back_populates="parent_message", cascade_delete=True)


class TeamMessageCreate(SQLModel):
    case_id: uuid.UUID | None = Field(default=None)
    message_type: TeamMessageType = Field(default=TeamMessageType.GENERAL)
    subject: str = Field(max_length=255)
    content: str = Field(max_length=5000)
    mentions: list[uuid.UUID] = Field(default_factory=list)
    attachments: list[str] = Field(default_factory=list)
    is_urgent: bool = Field(default=False)


class TeamMessageUpdate(SQLModel):
    subject: str | None = Field(default=None, max_length=255)
    content: str | None = Field(default=None, max_length=5000)
    is_pinned: bool | None = Field(default=None)
    is_urgent: bool | None = Field(default=None)


class TeamMessagePublic(TeamMessageBase):
    id: uuid.UUID
    sender_id: uuid.UUID
    created_at: datetime
    updated_at: datetime
    sender: Optional[UserPublic] = None
    case: Optional["LegalCasePublic"] = None
    replies_count: int = 0
    is_read: bool = False


class TeamMessagesPublic(SQLModel):
    data: list[TeamMessagePublic]
    count: int
    unread_count: int


# Team Message Replies
class TeamMessageReplyBase(SQLModel):
    parent_message_id: uuid.UUID = Field(foreign_key="teammessage.id")
    content: str = Field(max_length=2000)
    mentions: list[uuid.UUID] = Field(default_factory=list, sa_column=Column(JSON))


class TeamMessageReply(TeamMessageReplyBase, table=True):
    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    sender_id: uuid.UUID = Field(foreign_key="user.id", nullable=False)
    created_at: datetime = Field(default_factory=datetime.utcnow)

    # Relationships
    sender: Optional[User] = Relationship(
        sa_relationship_kwargs={"foreign_keys": "[TeamMessageReply.sender_id]"}
    )
    parent_message: Optional[TeamMessage] = Relationship(back_populates="replies")


class TeamMessageReplyCreate(SQLModel):
    content: str = Field(max_length=2000)
    mentions: list[uuid.UUID] = Field(default_factory=list)


# Document Analysis Models for AI Document Processing

class Document(SQLModel, table=True):
    __tablename__ = "documents"

    id: int = Field(primary_key=True)

    # File information
    filename: str = Field(max_length=255)
    original_filename: str = Field(max_length=255)
    file_path: str = Field(max_length=500)
    file_size: int = Field()  # Size in bytes
    content_type: str = Field(max_length=100)
    file_hash: str | None = Field(default=None, max_length=64)  # SHA-256 hash

    # Upload information
    uploaded_by: uuid.UUID = Field(foreign_key="user.id")
    upload_status: str = Field(default="uploaded", max_length=50)

    # Document metadata
    title: str | None = Field(default=None, max_length=500)
    description: str | None = Field(default=None, sa_column=Column(Text))
    tags: str | None = Field(default=None, max_length=500)

    # OCR and processing
    ocr_text: str | None = Field(default=None, sa_column=Column(Text))
    ocr_status: str = Field(default="pending", max_length=50)
    ocr_confidence: str | None = Field(default=None, max_length=10)

    # Legal case association
    legal_case_id: uuid.UUID | None = Field(default=None, foreign_key="legalcase.id")

    # Metadata
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)


class DocumentAnalysis(SQLModel, table=True):
    __tablename__ = "document_analyses"

    id: int = Field(primary_key=True)
    document_id: int = Field(foreign_key="documents.id")
    user_id: uuid.UUID = Field(foreign_key="user.id")

    # Analysis results
    document_type: str = Field(max_length=100)
    extracted_text: str | None = Field(default=None, sa_column=Column(Text))
    key_entities: list[str] | None = Field(default=None, sa_column=Column(JSON))
    suggested_actions: list[str] | None = Field(default=None, sa_column=Column(JSON))
    confidence: float = Field(default=0.0)

    # OCR and AI processing info
    ocr_status: str = Field(default="pending", max_length=50)
    ai_analysis_status: str = Field(default="pending", max_length=50)
    processing_time: float | None = Field(default=None)

    # Metadata
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)


class ExtractedInformation(SQLModel, table=True):
    __tablename__ = "extracted_information"

    id: int = Field(primary_key=True)
    analysis_id: int = Field(foreign_key="document_analyses.id")

    # Parties involved
    plaintiff: str | None = Field(default=None, max_length=255)
    defendant: str | None = Field(default=None, max_length=255)
    witnesses: list[str] | None = Field(default=None, sa_column=Column(JSON))
    lawyers: list[str] | None = Field(default=None, sa_column=Column(JSON))

    # Case information
    case_type: str | None = Field(default=None, max_length=100)
    case_title: str | None = Field(default=None, max_length=500)
    client_name: str | None = Field(default=None, max_length=255)

    # Document details
    document_summary: str | None = Field(default=None, sa_column=Column(Text))
    key_facts: list[str] | None = Field(default=None, sa_column=Column(JSON))
    important_dates: dict | None = Field(default=None, sa_column=Column(JSON))
    amounts: dict | None = Field(default=None, sa_column=Column(JSON))
    legal_references: list[str] | None = Field(default=None, sa_column=Column(JSON))

    # Metadata
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)


class UserValidation(SQLModel, table=True):
    __tablename__ = "user_validations"

    id: int = Field(primary_key=True)
    analysis_id: int = Field(foreign_key="document_analyses.id")
    user_id: uuid.UUID = Field(foreign_key="user.id")

    # Validation status
    status: str = Field(max_length=50)  # pending, approved, modified, rejected

    # Validated information
    validated_data: dict | None = Field(default=None, sa_column=Column(JSON))

    # Validation notes
    validation_notes: str | None = Field(default=None, sa_column=Column(Text))
    create_case: str = Field(default="true", max_length=10)

    # Metadata
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)


class AnalysisHistory(SQLModel, table=True):
    __tablename__ = "analysis_history"

    id: int = Field(primary_key=True)
    analysis_id: int = Field(foreign_key="document_analyses.id")
    user_id: uuid.UUID = Field(foreign_key="user.id")

    # History details
    action: str = Field(max_length=100)
    description: str | None = Field(default=None, sa_column=Column(Text))
    old_data: dict | None = Field(default=None, sa_column=Column(JSON))
    new_data: dict | None = Field(default=None, sa_column=Column(JSON))

    # Metadata
    created_at: datetime = Field(default_factory=datetime.now)


class TeamMessageReplyPublic(TeamMessageReplyBase):
    id: uuid.UUID
    sender_id: uuid.UUID
    created_at: datetime
    sender: Optional[UserPublic] = None


# Team Task Management
class TaskStatus(str, Enum):
    NOT_STARTED = "not_started"
    IN_PROGRESS = "in_progress"
    UNDER_REVIEW = "under_review"
    COMPLETED = "completed"
    CANCELLED = "cancelled"


class TaskPriority(str, Enum):
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    URGENT = "urgent"


class TeamTaskBase(SQLModel):
    case_id: uuid.UUID = Field(foreign_key="legalcase.id")
    title: str = Field(max_length=255)
    description: str | None = Field(default=None, max_length=2000)
    status: TaskStatus = Field(default=TaskStatus.NOT_STARTED)
    priority: TaskPriority = Field(default=TaskPriority.MEDIUM)
    assigned_to: uuid.UUID | None = Field(foreign_key="user.id", nullable=True)
    due_date: datetime | None = Field(default=None)
    estimated_hours: float | None = Field(default=None, ge=0)
    actual_hours: float | None = Field(default=None, ge=0)
    tags: list[str] = Field(default_factory=list, sa_column=Column(JSON))
    dependencies: list[uuid.UUID] = Field(default_factory=list, sa_column=Column(JSON))


class TeamTask(TeamTaskBase, table=True):
    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    created_by: uuid.UUID = Field(foreign_key="user.id", nullable=False)
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    completed_at: datetime | None = Field(default=None)

    # Relationships
    case: Optional["LegalCase"] = Relationship(back_populates="team_tasks")
    creator: Optional[User] = Relationship(
        back_populates="created_tasks",
        sa_relationship_kwargs={"foreign_keys": "[TeamTask.created_by]"}
    )
    assignee: Optional[User] = Relationship(
        back_populates="assigned_tasks",
        sa_relationship_kwargs={"foreign_keys": "[TeamTask.assigned_to]"}
    )


class TeamTaskCreate(SQLModel):
    title: str = Field(max_length=255)
    description: str | None = Field(default=None, max_length=2000)
    priority: TaskPriority = Field(default=TaskPriority.MEDIUM)
    assigned_to: uuid.UUID | None = Field(default=None)
    due_date: datetime | None = Field(default=None)
    estimated_hours: float | None = Field(default=None, ge=0)
    tags: list[str] = Field(default_factory=list)
    dependencies: list[uuid.UUID] = Field(default_factory=list)


class TeamTaskUpdate(SQLModel):
    title: str | None = Field(default=None, max_length=255)
    description: str | None = Field(default=None, max_length=2000)
    status: TaskStatus | None = Field(default=None)
    priority: TaskPriority | None = Field(default=None)
    assigned_to: uuid.UUID | None = Field(default=None)
    due_date: datetime | None = Field(default=None)
    estimated_hours: float | None = Field(default=None, ge=0)
    actual_hours: float | None = Field(default=None, ge=0)
    tags: list[str] | None = Field(default=None)
    dependencies: list[uuid.UUID] | None = Field(default=None)


class TeamTaskPublic(TeamTaskBase):
    id: uuid.UUID
    created_by: uuid.UUID
    created_at: datetime
    updated_at: datetime
    completed_at: datetime | None
    creator: Optional[UserPublic] = None
    assignee: Optional[UserPublic] = None
    case: Optional["LegalCasePublic"] = None
    is_overdue: bool = False
    progress_percentage: float = 0.0


class TeamTasksPublic(SQLModel):
    data: list[TeamTaskPublic]
    count: int


# AI Document Analysis Models

class DocumentAnalysisBase(SQLModel):
    """Base model for document analysis results"""
    document_id: uuid.UUID = Field(foreign_key="casedocument.id")
    analysis_type: DocumentProcessingType = Field(default=DocumentProcessingType.AI_ANALYSIS)
    status: AnalysisStatus = Field(default=AnalysisStatus.PENDING)
    extracted_text: str | None = Field(default=None, description="Raw text extracted from document")
    processing_metadata: Dict[str, Any] | None = Field(default=None, sa_column=Column(JSON))
    error_message: str | None = Field(default=None, max_length=1000)
    confidence_score: float | None = Field(default=None, ge=0.0, le=1.0)


class DocumentAnalysis(DocumentAnalysisBase, table=True):
    """Document analysis results table"""
    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    analyzed_by: uuid.UUID = Field(foreign_key="user.id")


class ExtractedInformationBase(SQLModel):
    """Base model for extracted legal information"""
    analysis_id: uuid.UUID = Field(foreign_key="documentanalysis.id")

    # Parties involved
    plaintiff: str | None = Field(default=None, max_length=500)
    defendant: str | None = Field(default=None, max_length=500)
    witnesses: List[str] | None = Field(default=None, sa_column=Column(JSON))
    lawyers: List[str] | None = Field(default=None, sa_column=Column(JSON))

    # Case information
    suggested_case_type: CaseType | None = Field(default=None)
    case_type_confidence: ExtractionConfidence | None = Field(default=None)

    # Document summary
    document_summary: str | None = Field(default=None, max_length=2000)
    key_facts: List[str] | None = Field(default=None, sa_column=Column(JSON))

    # Important dates
    important_dates: Dict[str, str] | None = Field(default=None, sa_column=Column(JSON))

    # Financial information
    amounts: Dict[str, float] | None = Field(default=None, sa_column=Column(JSON))

    # Legal references
    legal_references: List[str] | None = Field(default=None, sa_column=Column(JSON))

    # Additional extracted data
    additional_data: Dict[str, Any] | None = Field(default=None, sa_column=Column(JSON))


class ExtractedInformation(ExtractedInformationBase, table=True):
    """Extracted legal information table"""
    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    created_at: datetime = Field(default_factory=datetime.utcnow)


class UserValidationBase(SQLModel):
    """Base model for user validation of extracted information"""
    extracted_info_id: uuid.UUID = Field(foreign_key="extractedinformation.id")
    validated_by: uuid.UUID = Field(foreign_key="user.id")
    validation_status: ValidationStatus = Field(default=ValidationStatus.PENDING)

    # Validated/corrected information
    validated_data: Dict[str, Any] | None = Field(default=None, sa_column=Column(JSON))
    validation_notes: str | None = Field(default=None, max_length=1000)

    # Case creation data if approved
    should_create_case: bool = Field(default=False)
    suggested_case_title: str | None = Field(default=None, max_length=255)
    suggested_client_name: str | None = Field(default=None, max_length=255)


class UserValidation(UserValidationBase, table=True):
    """User validation table"""
    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)


class AnalysisHistoryBase(SQLModel):
    """Base model for analysis history tracking"""
    case_id: uuid.UUID | None = Field(default=None, foreign_key="legalcase.id")
    document_analysis_id: uuid.UUID = Field(foreign_key="documentanalysis.id")
    action_type: str = Field(max_length=100)  # "created", "updated", "validated", etc.
    action_description: str = Field(max_length=500)
    performed_by: uuid.UUID = Field(foreign_key="user.id")
    changes_made: Dict[str, Any] | None = Field(default=None, sa_column=Column(JSON))


class AnalysisHistory(AnalysisHistoryBase, table=True):
    """Analysis history tracking table"""
    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    created_at: datetime = Field(default_factory=datetime.utcnow)


# Public models for API responses

class DocumentAnalysisPublic(SQLModel):
    """Public model for document analysis"""
    id: uuid.UUID
    document_id: uuid.UUID
    analysis_type: DocumentProcessingType
    status: AnalysisStatus
    extracted_text: str | None = None
    confidence_score: float | None = None
    error_message: str | None = None
    created_at: datetime
    updated_at: datetime


class ExtractedInformationPublic(SQLModel):
    """Public model for extracted information"""
    id: uuid.UUID
    analysis_id: uuid.UUID
    plaintiff: str | None = None
    defendant: str | None = None
    witnesses: List[str] | None = None
    lawyers: List[str] | None = None
    suggested_case_type: CaseType | None = None
    case_type_confidence: ExtractionConfidence | None = None
    document_summary: str | None = None
    key_facts: List[str] | None = None
    important_dates: Dict[str, str] | None = None
    amounts: Dict[str, float] | None = None
    legal_references: List[str] | None = None
    additional_data: Dict[str, Any] | None = None
    created_at: datetime


class UserValidationPublic(SQLModel):
    """Public model for user validation"""
    id: uuid.UUID
    extracted_info_id: uuid.UUID
    validation_status: ValidationStatus
    validated_data: Dict[str, Any] | None = None
    validation_notes: str | None = None
    should_create_case: bool
    suggested_case_title: str | None = None
    suggested_client_name: str | None = None
    created_at: datetime
    updated_at: datetime


class AnalysisHistoryPublic(SQLModel):
    """Public model for analysis history"""
    id: uuid.UUID
    case_id: uuid.UUID | None = None
    document_analysis_id: uuid.UUID
    action_type: str
    action_description: str
    changes_made: Dict[str, Any] | None = None
    created_at: datetime


# Create/Update models

class DocumentAnalysisCreate(SQLModel):
    """Model for creating document analysis"""
    document_id: uuid.UUID
    analysis_type: DocumentProcessingType = DocumentProcessingType.AI_ANALYSIS


class ExtractedInformationCreate(SQLModel):
    """Model for creating extracted information"""
    analysis_id: uuid.UUID
    plaintiff: str | None = None
    defendant: str | None = None
    witnesses: List[str] | None = None
    lawyers: List[str] | None = None
    suggested_case_type: CaseType | None = None
    case_type_confidence: ExtractionConfidence | None = None
    document_summary: str | None = None
    key_facts: List[str] | None = None
    important_dates: Dict[str, str] | None = None
    amounts: Dict[str, float] | None = None
    legal_references: List[str] | None = None
    additional_data: Dict[str, Any] | None = None


class UserValidationCreate(SQLModel):
    """Model for creating user validation"""
    extracted_info_id: uuid.UUID
    validation_status: ValidationStatus = ValidationStatus.PENDING
    validated_data: Dict[str, Any] | None = None
    validation_notes: str | None = None
    should_create_case: bool = False
    suggested_case_title: str | None = None
    suggested_client_name: str | None = None


class UserValidationUpdate(SQLModel):
    """Model for updating user validation"""
    validation_status: ValidationStatus | None = None
    validated_data: Dict[str, Any] | None = None
    validation_notes: str | None = None
    should_create_case: bool | None = None
    suggested_case_title: str | None = None
    suggested_client_name: str | None = None


# Response models for collections

class DocumentAnalysesPublic(SQLModel):
    """Public model for document analyses collection"""
    data: List[DocumentAnalysisPublic]
    count: int


class ExtractedInformationsPublic(SQLModel):
    """Public model for extracted informations collection"""
    data: List[ExtractedInformationPublic]
    count: int


class UserValidationsPublic(SQLModel):
    """Public model for user validations collection"""
    data: List[UserValidationPublic]
    count: int


class AnalysisHistoriesPublic(SQLModel):
    """Public model for analysis histories collection"""
    data: List[AnalysisHistoryPublic]
    count: int
