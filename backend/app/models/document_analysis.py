from sqlalchemy import Column, Integer, String, Text, Float, DateTime, ForeignKey, JSON
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.database import Base

class DocumentAnalysis(Base):
    __tablename__ = "document_analyses"

    id = Column(Integer, primary_key=True, index=True)
    document_id = Column(Integer, ForeignKey("documents.id"), nullable=False)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    
    # Analysis results
    document_type = Column(String(100), nullable=False)
    extracted_text = Column(Text, nullable=True)
    key_entities = Column(JSON, nullable=True)  # List of extracted entities
    suggested_actions = Column(JSON, nullable=True)  # List of suggested actions
    confidence = Column(Float, nullable=False, default=0.0)
    
    # OCR and AI processing info
    ocr_status = Column(String(50), default="pending")  # pending, completed, failed
    ai_analysis_status = Column(String(50), default="pending")  # pending, completed, failed
    processing_time = Column(Float, nullable=True)  # Processing time in seconds
    
    # Metadata
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    document = relationship("Document", back_populates="analyses")
    user = relationship("User")
    extracted_info = relationship("ExtractedInformation", back_populates="analysis", uselist=False)
    validations = relationship("UserValidation", back_populates="analysis")


class ExtractedInformation(Base):
    __tablename__ = "extracted_information"

    id = Column(Integer, primary_key=True, index=True)
    analysis_id = Column(Integer, ForeignKey("document_analyses.id"), nullable=False)
    
    # Parties involved
    plaintiff = Column(String(255), nullable=True)
    defendant = Column(String(255), nullable=True)
    witnesses = Column(JSON, nullable=True)  # List of witnesses
    lawyers = Column(JSON, nullable=True)  # List of lawyers
    
    # Case information
    case_type = Column(String(100), nullable=True)
    case_title = Column(String(500), nullable=True)
    client_name = Column(String(255), nullable=True)
    
    # Document details
    document_summary = Column(Text, nullable=True)
    key_facts = Column(JSON, nullable=True)  # List of key facts
    important_dates = Column(JSON, nullable=True)  # Dict of date_type: date
    amounts = Column(JSON, nullable=True)  # Dict of amount_type: amount
    legal_references = Column(JSON, nullable=True)  # List of legal references
    
    # Metadata
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    analysis = relationship("DocumentAnalysis", back_populates="extracted_info")


class UserValidation(Base):
    __tablename__ = "user_validations"

    id = Column(Integer, primary_key=True, index=True)
    analysis_id = Column(Integer, ForeignKey("document_analyses.id"), nullable=False)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    
    # Validation status
    status = Column(String(50), nullable=False)  # pending, approved, modified, rejected
    
    # Validated information (JSON copy of ExtractedInformation with modifications)
    validated_data = Column(JSON, nullable=True)
    
    # Validation notes
    validation_notes = Column(Text, nullable=True)
    create_case = Column(String(10), default="true")  # "true" or "false"
    
    # Metadata
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    analysis = relationship("DocumentAnalysis", back_populates="validations")
    user = relationship("User")


class AnalysisHistory(Base):
    __tablename__ = "analysis_history"

    id = Column(Integer, primary_key=True, index=True)
    analysis_id = Column(Integer, ForeignKey("document_analyses.id"), nullable=False)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    
    # History details
    action = Column(String(100), nullable=False)  # created, updated, validated, case_created
    description = Column(Text, nullable=True)
    old_data = Column(JSON, nullable=True)  # Previous state
    new_data = Column(JSON, nullable=True)  # New state
    
    # Metadata
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships
    analysis = relationship("DocumentAnalysis")
    user = relationship("User")
