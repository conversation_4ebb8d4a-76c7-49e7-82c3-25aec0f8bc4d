from sqlalchemy import Column, Integer, String, Text, DateTime, ForeignKey, BigInteger
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.database import Base

class Document(Base):
    __tablename__ = "documents"

    id = Column(Integer, primary_key=True, index=True)
    
    # File information
    filename = Column(String(255), nullable=False)
    original_filename = Column(String(255), nullable=False)
    file_path = Column(String(500), nullable=False)
    file_size = Column(BigInteger, nullable=False)  # Size in bytes
    content_type = Column(String(100), nullable=False)
    file_hash = Column(String(64), nullable=True)  # SHA-256 hash for deduplication
    
    # Upload information
    uploaded_by = Column(Integer, ForeignKey("users.id"), nullable=False)
    upload_status = Column(String(50), default="uploaded")  # uploaded, processing, completed, failed
    
    # Document metadata
    title = Column(String(500), nullable=True)
    description = Column(Text, nullable=True)
    tags = Column(String(500), nullable=True)  # Comma-separated tags
    
    # OCR and processing
    ocr_text = Column(Text, nullable=True)  # Extracted text from OCR
    ocr_status = Column(String(50), default="pending")  # pending, processing, completed, failed
    ocr_confidence = Column(String(10), nullable=True)  # OCR confidence score
    
    # Legal case association
    legal_case_id = Column(Integer, ForeignKey("legal_cases.id"), nullable=True)
    
    # Metadata
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    uploader = relationship("User")
    legal_case = relationship("LegalCase", back_populates="documents")
    analyses = relationship("DocumentAnalysis", back_populates="document")
