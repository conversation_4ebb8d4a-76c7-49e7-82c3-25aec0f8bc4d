from fastapi import APIRouter

from app.api.routes import (
    ai_analysis,
    ai_document_analysis,
    case_analytics,
    case_documents,
    case_folders,
    case_notes,
    case_progress,
    case_templates,
    client_portal,
    document_case_links,
    documents,
    internal,
    items,
    legal_cases,
    login,
    notifications,
    protected,
    reports,
    team_collaboration,
    team_tasks,
    users,
    utils,
)
from app.core.config import settings

api_router = APIRouter()

# Pas de prefix pour items, comme avant
api_router.include_router(users.router, prefix="/users", tags=["users"])
api_router.include_router(items.router)  # <-- ici, pas de prefix
api_router.include_router(login.router, prefix="/login", tags=["login"])
api_router.include_router(utils.router)  # pas de prefix aussi pour utils (comme avant)
api_router.include_router(protected.router, prefix="", tags=["protected"])
api_router.include_router(
    legal_cases.router, prefix="/legal-cases", tags=["legal_cases"]
)
api_router.include_router(
    case_notes.router, prefix="/legal-cases", tags=["case_notes"]
)
api_router.include_router(
    case_documents.router, prefix="/legal-cases", tags=["case_documents"]
)
api_router.include_router(
    case_folders.router, prefix="/legal-cases", tags=["case_folders"]
)
api_router.include_router(
    case_progress.router, prefix="/legal-cases", tags=["case_progress"]
)
api_router.include_router(
    case_analytics.router, prefix="/legal-cases", tags=["case_analytics"]
)
api_router.include_router(
    case_templates.router, prefix="/api/v1/templates", tags=["case_templates"]
)
api_router.include_router(
    document_case_links.router, prefix="/api/v1", tags=["document_case_links"]
)
api_router.include_router(reports.router, prefix="/reports", tags=["reports"])
api_router.include_router(documents.router)
api_router.include_router(
    client_portal.router, prefix="/client-portal", tags=["client_portal"]
)
api_router.include_router(
    team_collaboration.router, prefix="/team", tags=["team_collaboration"]
)
api_router.include_router(
    team_tasks.router, prefix="/team", tags=["team_tasks"]
)
api_router.include_router(
    notifications.router, prefix="/notifications", tags=["notifications"]
)
api_router.include_router(
    ai_analysis.router, prefix="/ai-analysis", tags=["ai_analysis"]
)
api_router.include_router(
    ai_document_analysis.router, prefix="/ai-document-analysis", tags=["ai_document_analysis"]
)

if settings.ENVIRONMENT == "local":
    api_router.include_router(internal.router, prefix="/internal", tags=["internal"])
