import os
import hashlib
import uuid
from datetime import datetime
from typing import List, Optional
from pathlib import Path
from fastapi import APIRouter, Depends, HTTPException, UploadFile, File, Form
from sqlmodel import Session, select
from app.core.db import get_session
from app.api.deps import LegalAccessUser
from app.models import (
    Document, 
    DocumentAnalysis, 
    ExtractedInformation, 
    UserValidation, 
    AnalysisHistory,
    LegalCase,
    LegalCaseCreate,
    User
)
from app.crud import crud_legal_case
from app.services.openai_service import openai_service
from app.services.ocr_service import ocr_service
import json

router = APIRouter()

@router.post("/test-ocr")
async def test_ocr_only(file: UploadFile = File(...)):
    """Test OCR extraction only - show extracted text"""
    import tempfile
    import os

    try:
        # Read file content
        file_content = await file.read()

        # Save file temporarily for OCR processing
        with tempfile.NamedTemporaryFile(delete=False, suffix=Path(file.filename).suffix) as temp_file:
            temp_file.write(file_content)
            temp_file_path = temp_file.name

        try:
            # Extract text using OCR service
            extracted_text = ocr_service.extract_text_from_file(temp_file_path, file.filename)
            ocr_confidence = ocr_service.get_extraction_confidence(extracted_text)

            return {
                "success": True,
                "filename": file.filename,
                "file_size": len(file_content),
                "file_type": Path(file.filename).suffix.lower(),
                "extracted_text_length": len(extracted_text),
                "extracted_text": extracted_text,
                "ocr_confidence": ocr_confidence,
                "is_fallback": "[OCR/Text extraction not available" in extracted_text
            }
        finally:
            # Clean up temporary file
            if os.path.exists(temp_file_path):
                os.unlink(temp_file_path)

    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "filename": file.filename
        }

@router.post("/complete-analysis")
async def complete_ocr_and_ai_analysis(
    file: UploadFile = File(...),
    output_language: str = Form("auto")
):
    """Complete workflow: OCR extraction + OpenAI analysis with language choice"""
    import tempfile
    import os

    try:
        # Read file content
        file_content = await file.read()

        # Save file temporarily for OCR processing
        with tempfile.NamedTemporaryFile(delete=False, suffix=Path(file.filename).suffix) as temp_file:
            temp_file.write(file_content)
            temp_file_path = temp_file.name

        try:
            # Step 1: Extract text using OCR service
            extracted_text = ocr_service.extract_text_from_file(temp_file_path, file.filename)
            ocr_confidence = ocr_service.get_extraction_confidence(extracted_text)

            # Step 2: Analyze with OpenAI if we have good text
            if not extracted_text or len(extracted_text.strip()) < 50:
                # Fallback: use filename for analysis
                extracted_text = f"Document: {file.filename}\nContent: Legal document requiring analysis."

            # Step 3: AI analysis with language preference
            analysis_result = openai_service.analyze_legal_document(extracted_text, file.filename, output_language)

            return {
                "success": True,
                "filename": file.filename,
                "file_size": len(file_content),
                "file_type": Path(file.filename).suffix.lower(),
                "output_language": output_language,
                "ocr_extraction": {
                    "extracted_text_length": len(extracted_text),
                    "extracted_text_preview": extracted_text[:500] + "..." if len(extracted_text) > 500 else extracted_text,
                    "ocr_confidence": ocr_confidence,
                    "is_fallback": "[OCR/Text extraction not available" in extracted_text
                },
                "ai_analysis": analysis_result,
                "openai_enabled": openai_service.enabled
            }
        finally:
            # Clean up temporary file
            if os.path.exists(temp_file_path):
                os.unlink(temp_file_path)

    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "filename": file.filename,
            "openai_enabled": openai_service.enabled
        }

@router.post("/test-openai")
async def test_openai_analysis(file: UploadFile = File(...)):
    """Test OpenAI analysis without database"""
    import tempfile
    import os

    try:
        # Read file content
        file_content = await file.read()

        # Save file temporarily for OCR processing
        with tempfile.NamedTemporaryFile(delete=False, suffix=Path(file.filename).suffix) as temp_file:
            temp_file.write(file_content)
            temp_file_path = temp_file.name

        try:
            # Extract text using OCR service
            extracted_text = ocr_service.extract_text_from_file(temp_file_path, file.filename)
            if not extracted_text or len(extracted_text.strip()) < 50:
                # Fallback: use filename for analysis
                extracted_text = f"Document: {file.filename}\nContent: Legal document requiring analysis."
        finally:
            # Clean up temporary file
            if os.path.exists(temp_file_path):
                os.unlink(temp_file_path)

        # Analyze with OpenAI
        analysis_result = openai_service.analyze_legal_document(extracted_text, file.filename)

        return {
            "success": True,
            "filename": file.filename,
            "extracted_text_length": len(extracted_text),
            "extracted_text_preview": extracted_text[:500] + "..." if len(extracted_text) > 500 else extracted_text,
            "extracted_text_full": extracted_text,
            "ocr_confidence": ocr_service.get_extraction_confidence(extracted_text),
            "analysis_result": analysis_result,
            "openai_enabled": openai_service.enabled
        }

    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "filename": file.filename,
            "openai_enabled": openai_service.enabled
        }

# Configuration
UPLOAD_DIR = "uploads"
MAX_FILE_SIZE = 50 * 1024 * 1024  # 50MB
ALLOWED_EXTENSIONS = {".pdf", ".doc", ".docx", ".jpg", ".jpeg", ".png", ".tiff"}

# Ensure upload directory exists
os.makedirs(UPLOAD_DIR, exist_ok=True)

def get_file_hash(file_content: bytes) -> str:
    """Generate SHA-256 hash of file content"""
    return hashlib.sha256(file_content).hexdigest()

def simulate_ocr_extraction(file_content: bytes, filename: str) -> str:
    """Simulate OCR text extraction"""
    # In real implementation, use Tesseract OCR here
    if filename.lower().endswith('.pdf'):
        return f"Extracted text from PDF: {filename}\n\nCONTRAT D'ACHAT\n\nAcheteur: Jean Dupont\nVendeur: Marie Martin\nObjet: Appartement 3 pièces\nPrix: 250,000 EUR\nDate: {datetime.now().strftime('%Y-%m-%d')}\n\nClauses importantes:\n- Livraison prévue le 15 mars 2024\n- Garantie décennale incluse\n- Frais de notaire à la charge de l'acheteur"
    else:
        return f"Extracted text from image: {filename}\n\nDocument juridique scanné\nParties: Client A vs Client B\nType: Litige commercial\nMontant: 15,000 EUR"

def simulate_ai_analysis(extracted_text: str, filename: str) -> dict:
    """Simulate OpenAI analysis"""
    # In real implementation, call OpenAI API here
    if "contrat" in extracted_text.lower() or "achat" in extracted_text.lower():
        return {
            "document_type": "contract",
            "key_entities": ["Jean Dupont", "Marie Martin", "Appartement 3 pièces"],
            "suggested_actions": ["Vérifier les clauses", "Programmer signature", "Préparer financement"],
            "confidence": 0.92,
            "extracted_info": {
                "plaintiff": "Jean Dupont",
                "defendant": "Marie Martin", 
                "case_type": "real_estate",
                "case_title": "Achat Appartement - Jean Dupont",
                "client_name": "Jean Dupont",
                "document_summary": "Contrat d'achat d'un appartement 3 pièces pour 250,000 EUR",
                "key_facts": ["Prix: 250,000 EUR", "Livraison: 15 mars 2024", "Garantie décennale"],
                "important_dates": {"signature": "2024-03-15"},
                "amounts": {"prix_achat": 250000},
                "legal_references": []
            }
        }
    else:
        return {
            "document_type": "legal_document",
            "key_entities": ["Client A", "Client B", "Litige commercial"],
            "suggested_actions": ["Analyser le litige", "Préparer défense", "Évaluer dommages"],
            "confidence": 0.85,
            "extracted_info": {
                "plaintiff": "Client A",
                "defendant": "Client B",
                "case_type": "civil",
                "case_title": "Litige Commercial - Client A vs Client B", 
                "client_name": "Client A",
                "document_summary": "Litige commercial concernant un montant de 15,000 EUR",
                "key_facts": ["Montant: 15,000 EUR", "Nature: Commercial"],
                "important_dates": {},
                "amounts": {"montant_litige": 15000},
                "legal_references": []
            }
        }

@router.post("/upload")
async def upload_document(
    session: Session = Depends(get_session),
    file: UploadFile = File(...),
    title: Optional[str] = Form(None),
    description: Optional[str] = Form(None)
):
    """Upload a document for AI analysis"""
    
    # Validate file
    if not file.filename:
        raise HTTPException(status_code=400, detail="No file provided")
    
    file_ext = os.path.splitext(file.filename)[1].lower()
    if file_ext not in ALLOWED_EXTENSIONS:
        raise HTTPException(
            status_code=400, 
            detail=f"File type not allowed. Allowed types: {', '.join(ALLOWED_EXTENSIONS)}"
        )
    
    # Read file content
    file_content = await file.read()
    if len(file_content) > MAX_FILE_SIZE:
        raise HTTPException(status_code=400, detail="File too large")
    
    # Generate unique filename and hash
    file_hash = get_file_hash(file_content)
    unique_filename = f"{uuid.uuid4()}_{file.filename}"
    file_path = os.path.join(UPLOAD_DIR, unique_filename)
    
    # Check for duplicate files
    existing_doc = session.exec(
        select(Document).where(Document.file_hash == file_hash)
    ).first()
    
    if existing_doc:
        return {
            "message": "File already exists",
            "document_id": existing_doc.id,
            "duplicate": True
        }
    
    # Save file to disk
    with open(file_path, "wb") as f:
        f.write(file_content)
    
    # Create document record
    document = Document(
        filename=unique_filename,
        original_filename=file.filename,
        file_path=file_path,
        file_size=len(file_content),
        content_type=file.content_type or "application/octet-stream",
        file_hash=file_hash,
        uploaded_by=None,  # Temporary: no user required
        title=title or file.filename,
        description=description,
        upload_status="uploaded"
    )
    
    session.add(document)
    session.commit()
    session.refresh(document)
    
    return {
        "message": "File uploaded successfully",
        "document_id": document.id,
        "filename": document.original_filename,
        "file_size": document.file_size,
        "duplicate": False
    }

@router.post("/documents/{document_id}/analyze")
async def analyze_document(
    document_id: int,
    session: Session = Depends(get_session)
):
    """Analyze a document with AI"""
    
    # Get document
    document = session.get(Document, document_id)
    if not document:
        raise HTTPException(status_code=404, detail="Document not found")
    
    # Check if analysis already exists
    existing_analysis = session.exec(
        select(DocumentAnalysis).where(DocumentAnalysis.document_id == document_id)
    ).first()
    
    if existing_analysis:
        return {
            "message": "Document already analyzed",
            "analysis_id": existing_analysis.id,
            "already_analyzed": True
        }
    
    try:
        # Read file content
        with open(document.file_path, "rb") as f:
            file_content = f.read()
        
        # Real OCR extraction
        extracted_text = ocr_service.extract_text_from_file(document.file_path, document.original_filename)
        
        # Update document with OCR text
        document.ocr_text = extracted_text
        document.ocr_status = "completed"
        
        # Real AI analysis with OpenAI
        ai_results = openai_service.analyze_legal_document(extracted_text, document.original_filename)
        
        # Create analysis record
        analysis = DocumentAnalysis(
            document_id=document_id,
            user_id=current_user.id,
            document_type=ai_results["document_type"],
            extracted_text=extracted_text,
            key_entities=ai_results["key_entities"],
            suggested_actions=ai_results["suggested_actions"],
            confidence=ai_results["confidence"],
            ocr_status="completed",
            ai_analysis_status="completed",
            processing_time=2.5  # Simulated processing time
        )
        
        session.add(analysis)
        session.commit()
        session.refresh(analysis)
        
        # Create extracted information record
        extracted_info = ExtractedInformation(
            analysis_id=analysis.id,
            **ai_results["extracted_info"]
        )
        
        session.add(extracted_info)
        session.commit()
        session.refresh(extracted_info)
        
        # Create history record
        history = AnalysisHistory(
            analysis_id=analysis.id,
            user_id=current_user.id,
            action="analysis_completed",
            description=f"AI analysis completed for document {document.original_filename}",
            new_data=ai_results
        )
        
        session.add(history)
        session.commit()
        
        return {
            "message": "Document analyzed successfully",
            "analysis_id": analysis.id,
            "document_type": analysis.document_type,
            "confidence": analysis.confidence,
            "key_entities": analysis.key_entities,
            "suggested_actions": analysis.suggested_actions,
            "extracted_info": ai_results["extracted_info"],
            "already_analyzed": False
        }
        
    except Exception as e:
        # Update analysis status to failed
        if 'analysis' in locals():
            analysis.ai_analysis_status = "failed"
            session.commit()
        
        raise HTTPException(status_code=500, detail=f"Analysis failed: {str(e)}")

@router.post("/validations")
async def create_validation(
    validation_request: dict,
    current_user: LegalAccessUser,
    session: Session = Depends(get_session)
):
    """Create a validation for analyzed information"""

    # Extract parameters from request
    analysis_id = validation_request.get("analysis_id")
    validated_data = validation_request.get("validated_data", {})
    status = validation_request.get("status", "approved")
    validation_notes = validation_request.get("validation_notes")
    create_case = validation_request.get("create_case", True)

    # Get analysis
    analysis = session.get(DocumentAnalysis, analysis_id)
    if not analysis:
        raise HTTPException(status_code=404, detail="Analysis not found")
    
    # Create validation record
    validation = UserValidation(
        analysis_id=analysis_id,
        user_id=current_user.id,
        status=status,
        validated_data=validated_data,
        validation_notes=validation_notes,
        create_case="true" if create_case else "false"
    )
    
    session.add(validation)
    session.commit()
    session.refresh(validation)
    
    # Create history record
    history = AnalysisHistory(
        analysis_id=analysis_id,
        user_id=current_user.id,
        action="validation_created",
        description=f"Validation created with status: {status}",
        new_data={"validation_id": validation.id, "status": status}
    )
    
    session.add(history)
    session.commit()
    
    return {
        "message": "Validation created successfully",
        "validation_id": validation.id,
        "status": validation.status,
        "create_case": create_case
    }

@router.post("/validations/{validation_id}/create-case")
async def create_case_from_validation(
    validation_id: int,
    current_user: LegalAccessUser,
    session: Session = Depends(get_session)
):
    """Create a legal case from validated information"""
    
    # Get validation with analysis
    validation = session.get(UserValidation, validation_id)
    if not validation:
        raise HTTPException(status_code=404, detail="Validation not found")
    
    analysis = session.get(DocumentAnalysis, validation.analysis_id)
    if not analysis:
        raise HTTPException(status_code=404, detail="Analysis not found")
    
    document = session.get(Document, analysis.document_id)
    if not document:
        raise HTTPException(status_code=404, detail="Document not found")
    
    # Get validated data
    validated_data = validation.validated_data or {}
    
    # Create legal case
    case_data = LegalCaseCreate(
        title=validated_data.get("case_title", f"Case from {document.original_filename}"),
        client_name=validated_data.get("client_name", "Unknown Client"),
        case_type=validated_data.get("case_type", "other"),
        description=validated_data.get("document_summary", "Case created from AI document analysis"),
        lawyer_id=current_user.id
    )
    
    legal_case = crud_legal_case.create(session=session, obj_in=case_data)
    
    # Associate document with case
    document.legal_case_id = legal_case.id
    session.commit()
    
    # Create history record
    history = AnalysisHistory(
        analysis_id=analysis.id,
        user_id=current_user.id,
        action="case_created",
        description=f"Legal case created: {legal_case.title}",
        new_data={"case_id": str(legal_case.id), "case_title": legal_case.title}
    )
    
    session.add(history)
    session.commit()
    
    return {
        "message": "Legal case created successfully",
        "case_id": legal_case.id,
        "case_title": legal_case.title,
        "document_associated": True
    }
