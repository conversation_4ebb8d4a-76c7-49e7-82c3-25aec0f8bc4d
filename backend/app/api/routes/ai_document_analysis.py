"""
AI Document Analysis API Routes

This module provides API endpoints for AI-powered document analysis,
including document upload, analysis triggering, and results retrieval.
"""

import uuid
from typing import Any, List
from fastapi import APIRouter, Depends, HTTPException, Query, UploadFile, File
from sqlmodel import Session, select

from app.api.deps import LawyerScopedUser
from app.core.db import get_session
from app.models import (
    DocumentAnalysis,
    DocumentAnalysisPublic,
    DocumentAnalysesPublic,
    DocumentAnalysisCreate,
    ExtractedInformation,
    ExtractedInformationPublic,
    ExtractedInformationsPublic,
    UserValidation,
    UserValidationPublic,
    UserValidationCreate,
    UserValidationUpdate,
    UserValidationsPublic,
    AnalysisHistory,
    AnalysisHistoryPublic,
    AnalysisHistoriesPublic,
    CaseDocument,
    AnalysisStatus,
    DocumentProcessingType,
    ValidationStatus,
)
from app.services.ai_document_analysis_service import AIDocumentAnalysisService
from app.services.ai_case_creation_service import AICaseCreationService
from app.models import LegalCasePublic

router = APIRouter()


@router.post("/documents/{document_id}/analyze", response_model=DocumentAnalysisPublic)
async def analyze_document(
    document_id: uuid.UUID,
    current_user: LawyerScopedUser,
    session: Session = Depends(get_session),
    analysis_type: DocumentProcessingType = Query(DocumentProcessingType.AI_ANALYSIS),
) -> Any:
    """
    Analyze a document using AI to extract legal information
    
    Args:
        document_id: ID of the document to analyze
        analysis_type: Type of analysis to perform (OCR only or full AI analysis)
        
    Returns:
        DocumentAnalysisPublic: Analysis results
    """
    # Check if document exists and user has access
    document = session.get(CaseDocument, document_id)
    if not document:
        raise HTTPException(status_code=404, detail="Document not found")
    
    # Check if analysis already exists
    existing_analysis = session.exec(
        select(DocumentAnalysis).where(DocumentAnalysis.document_id == document_id)
    ).first()
    
    if existing_analysis and existing_analysis.status in [
        AnalysisStatus.IN_PROGRESS, 
        AnalysisStatus.COMPLETED,
        AnalysisStatus.REQUIRES_VALIDATION
    ]:
        return DocumentAnalysisPublic.model_validate(existing_analysis)
    
    # Create analysis service and analyze document
    analysis_service = AIDocumentAnalysisService(session)
    
    try:
        analysis = await analysis_service.analyze_document(
            document_id=document_id,
            user_id=current_user.id,
            analysis_type=analysis_type
        )
        
        return DocumentAnalysisPublic.model_validate(analysis)
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Analysis failed: {str(e)}")


@router.get("/documents/{document_id}/analysis", response_model=DocumentAnalysisPublic)
def get_document_analysis(
    document_id: uuid.UUID,
    current_user: LawyerScopedUser,
    session: Session = Depends(get_session),
) -> Any:
    """Get analysis results for a document"""
    
    analysis = session.exec(
        select(DocumentAnalysis).where(DocumentAnalysis.document_id == document_id)
    ).first()
    
    if not analysis:
        raise HTTPException(status_code=404, detail="Analysis not found")
    
    return DocumentAnalysisPublic.model_validate(analysis)


@router.get("/analysis/{analysis_id}/extracted-info", response_model=ExtractedInformationPublic)
def get_extracted_information(
    analysis_id: uuid.UUID,
    current_user: LawyerScopedUser,
    session: Session = Depends(get_session),
) -> Any:
    """Get extracted information from analysis"""
    
    extracted_info = session.exec(
        select(ExtractedInformation).where(ExtractedInformation.analysis_id == analysis_id)
    ).first()
    
    if not extracted_info:
        raise HTTPException(status_code=404, detail="Extracted information not found")
    
    return ExtractedInformationPublic.model_validate(extracted_info)


@router.post("/extracted-info/{extracted_info_id}/validate", response_model=UserValidationPublic)
def create_validation(
    extracted_info_id: uuid.UUID,
    validation_data: UserValidationCreate,
    current_user: LawyerScopedUser,
    session: Session = Depends(get_session),
) -> Any:
    """Create a validation for extracted information"""
    
    # Check if extracted info exists
    extracted_info = session.get(ExtractedInformation, extracted_info_id)
    if not extracted_info:
        raise HTTPException(status_code=404, detail="Extracted information not found")
    
    # Check if validation already exists
    existing_validation = session.exec(
        select(UserValidation).where(
            UserValidation.extracted_info_id == extracted_info_id
        )
    ).first()
    
    if existing_validation:
        raise HTTPException(status_code=400, detail="Validation already exists")
    
    # Create validation
    validation = UserValidation(
        **validation_data.model_dump(),
        validated_by=current_user.id
    )
    
    session.add(validation)
    session.commit()
    session.refresh(validation)
    
    return UserValidationPublic.model_validate(validation)


@router.put("/validations/{validation_id}", response_model=UserValidationPublic)
def update_validation(
    validation_id: uuid.UUID,
    validation_update: UserValidationUpdate,
    current_user: LawyerScopedUser,
    session: Session = Depends(get_session),
) -> Any:
    """Update a validation"""
    
    validation = session.get(UserValidation, validation_id)
    if not validation:
        raise HTTPException(status_code=404, detail="Validation not found")
    
    # Check if user can update this validation
    if validation.validated_by != current_user.id:
        raise HTTPException(status_code=403, detail="Not authorized to update this validation")
    
    # Update validation
    update_data = validation_update.model_dump(exclude_unset=True)
    for field, value in update_data.items():
        setattr(validation, field, value)
    
    session.commit()
    session.refresh(validation)
    
    return UserValidationPublic.model_validate(validation)


@router.get("/validations/{validation_id}", response_model=UserValidationPublic)
def get_validation(
    validation_id: uuid.UUID,
    current_user: LawyerScopedUser,
    session: Session = Depends(get_session),
) -> Any:
    """Get a specific validation"""
    
    validation = session.get(UserValidation, validation_id)
    if not validation:
        raise HTTPException(status_code=404, detail="Validation not found")
    
    return UserValidationPublic.model_validate(validation)


@router.get("/analysis/{analysis_id}/history", response_model=AnalysisHistoriesPublic)
def get_analysis_history(
    analysis_id: uuid.UUID,
    current_user: LawyerScopedUser,
    session: Session = Depends(get_session),
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=100),
) -> Any:
    """Get history for an analysis"""
    
    # Check if analysis exists
    analysis = session.get(DocumentAnalysis, analysis_id)
    if not analysis:
        raise HTTPException(status_code=404, detail="Analysis not found")
    
    # Get history records
    history_query = select(AnalysisHistory).where(
        AnalysisHistory.document_analysis_id == analysis_id
    ).offset(skip).limit(limit)
    
    history_records = session.exec(history_query).all()
    
    # Get total count
    count_query = select(AnalysisHistory).where(
        AnalysisHistory.document_analysis_id == analysis_id
    )
    total_count = len(session.exec(count_query).all())
    
    return AnalysisHistoriesPublic(
        data=[AnalysisHistoryPublic.model_validate(record) for record in history_records],
        count=total_count
    )


@router.get("/analyses", response_model=DocumentAnalysesPublic)
def get_analyses(
    current_user: LawyerScopedUser,
    session: Session = Depends(get_session),
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=100),
    status: AnalysisStatus | None = Query(None),
) -> Any:
    """Get list of document analyses"""
    
    query = select(DocumentAnalysis)
    
    if status:
        query = query.where(DocumentAnalysis.status == status)
    
    # Add pagination
    query = query.offset(skip).limit(limit)
    
    analyses = session.exec(query).all()
    
    # Get total count
    count_query = select(DocumentAnalysis)
    if status:
        count_query = count_query.where(DocumentAnalysis.status == status)
    
    total_count = len(session.exec(count_query).all())
    
    return DocumentAnalysesPublic(
        data=[DocumentAnalysisPublic.model_validate(analysis) for analysis in analyses],
        count=total_count
    )


@router.delete("/analysis/{analysis_id}")
def delete_analysis(
    analysis_id: uuid.UUID,
    current_user: LawyerScopedUser,
    session: Session = Depends(get_session),
) -> Any:
    """Delete an analysis and all related data"""
    
    analysis = session.get(DocumentAnalysis, analysis_id)
    if not analysis:
        raise HTTPException(status_code=404, detail="Analysis not found")
    
    # Delete related records
    # Delete extracted information
    extracted_info = session.exec(
        select(ExtractedInformation).where(ExtractedInformation.analysis_id == analysis_id)
    ).first()
    
    if extracted_info:
        # Delete validations
        validations = session.exec(
            select(UserValidation).where(UserValidation.extracted_info_id == extracted_info.id)
        ).all()
        
        for validation in validations:
            session.delete(validation)
        
        session.delete(extracted_info)
    
    # Delete history records
    history_records = session.exec(
        select(AnalysisHistory).where(AnalysisHistory.document_analysis_id == analysis_id)
    ).all()
    
    for record in history_records:
        session.delete(record)
    
    # Delete analysis
    session.delete(analysis)
    session.commit()
    
    return {"message": "Analysis deleted successfully"}


# Case Creation Endpoints

@router.post("/validations/{validation_id}/create-case", response_model=LegalCasePublic)
def create_case_from_validation(
    validation_id: uuid.UUID,
    current_user: LawyerScopedUser,
    session: Session = Depends(get_session),
) -> Any:
    """Create a legal case from validated AI-extracted information"""

    case_creation_service = AICaseCreationService(session)

    try:
        legal_case = case_creation_service.create_case_from_validation(
            validation_id=validation_id,
            user_id=current_user.id
        )

        return LegalCasePublic.model_validate(legal_case)

    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Case creation failed: {str(e)}")


@router.put("/cases/{case_id}/update-from-validation/{validation_id}", response_model=LegalCasePublic)
def update_case_from_validation(
    case_id: uuid.UUID,
    validation_id: uuid.UUID,
    current_user: LawyerScopedUser,
    session: Session = Depends(get_session),
) -> Any:
    """Update an existing case with new AI-extracted information"""

    case_creation_service = AICaseCreationService(session)

    try:
        legal_case = case_creation_service.update_case_from_new_analysis(
            case_id=case_id,
            validation_id=validation_id,
            user_id=current_user.id
        )

        return LegalCasePublic.model_validate(legal_case)

    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Case update failed: {str(e)}")


@router.get("/validations/pending", response_model=UserValidationsPublic)
def get_pending_validations(
    current_user: LawyerScopedUser,
    session: Session = Depends(get_session),
) -> Any:
    """Get pending validations for the current user"""

    case_creation_service = AICaseCreationService(session)
    pending_validations = case_creation_service.get_pending_validations(current_user.id)

    return UserValidationsPublic(
        data=[UserValidationPublic.model_validate(validation) for validation in pending_validations],
        count=len(pending_validations)
    )


@router.get("/validations/ready-for-case-creation", response_model=UserValidationsPublic)
def get_validations_ready_for_case_creation(
    current_user: LawyerScopedUser,
    session: Session = Depends(get_session),
) -> Any:
    """Get approved validations that are ready for case creation"""

    case_creation_service = AICaseCreationService(session)
    ready_validations = case_creation_service.get_approved_validations_for_case_creation(current_user.id)

    return UserValidationsPublic(
        data=[UserValidationPublic.model_validate(validation) for validation in ready_validations],
        count=len(ready_validations)
    )


@router.get("/cases/{case_id}/analysis/{analysis_id}/suggestions")
def get_case_update_suggestions(
    case_id: uuid.UUID,
    analysis_id: uuid.UUID,
    current_user: LawyerScopedUser,
    session: Session = Depends(get_session),
) -> Any:
    """Get suggestions for updating a case based on new document analysis"""

    case_creation_service = AICaseCreationService(session)

    try:
        suggestions = case_creation_service.suggest_case_updates_from_analysis(
            case_id=case_id,
            analysis_id=analysis_id
        )

        return suggestions

    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to generate suggestions: {str(e)}")
