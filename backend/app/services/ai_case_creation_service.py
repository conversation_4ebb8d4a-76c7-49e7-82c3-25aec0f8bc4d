"""
AI Case Creation Service

This service handles the creation of legal cases from validated AI-extracted information.
It manages the workflow from document analysis to case creation with user validation.
"""

import uuid
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime, date

from sqlmodel import Session, select

from app.models import (
    LegalCase,
    LegalCaseCreate,
    UserValidation,
    ExtractedInformation,
    DocumentAnalysis,
    CaseDocument,
    CaseActivity,
    AnalysisHistory,
    ActivityType,
    ValidationStatus,
    CaseStatus,
    CasePriority,
    User,
)
from app.crud import crud_legal_case

logger = logging.getLogger(__name__)


class AICaseCreationService:
    """Service for creating legal cases from AI-extracted information"""
    
    def __init__(self, session: Session):
        self.session = session
    
    def create_case_from_validation(
        self, 
        validation_id: uuid.UUID, 
        user_id: uuid.UUID
    ) -> LegalCase:
        """
        Create a legal case from validated AI-extracted information
        
        Args:
            validation_id: ID of the user validation
            user_id: ID of the user creating the case
            
        Returns:
            LegalCase: The created legal case
        """
        # Get validation
        validation = self.session.get(UserValidation, validation_id)
        if not validation:
            raise ValueError(f"Validation {validation_id} not found")
        
        if validation.validation_status != ValidationStatus.APPROVED:
            raise ValueError("Validation must be approved before creating case")
        
        if not validation.should_create_case:
            raise ValueError("Validation is not marked for case creation")
        
        # Get extracted information
        extracted_info = self.session.get(ExtractedInformation, validation.extracted_info_id)
        if not extracted_info:
            raise ValueError("Extracted information not found")
        
        # Get document analysis
        analysis = self.session.get(DocumentAnalysis, extracted_info.analysis_id)
        if not analysis:
            raise ValueError("Document analysis not found")
        
        # Get original document
        document = self.session.get(CaseDocument, analysis.document_id)
        if not document:
            raise ValueError("Original document not found")
        
        # Create case data from validation and extracted info
        case_data = self._build_case_data(validation, extracted_info, user_id)
        
        # Create the legal case
        legal_case = crud_legal_case.create(session=self.session, obj_in=case_data)
        
        # Update document to link to the new case
        document.case_id = legal_case.id
        self.session.commit()
        
        # Create case activity
        self._create_case_activity(
            legal_case.id,
            ActivityType.CASE_CREATED,
            f"Case created from AI analysis of document: {document.original_filename}",
            user_id,
            {
                "validation_id": str(validation_id),
                "analysis_id": str(extracted_info.analysis_id),
                "document_id": str(document.id),
                "ai_extracted": True
            }
        )
        
        # Create analysis history
        self._create_analysis_history(
            extracted_info.analysis_id,
            "case_created",
            f"Legal case '{legal_case.title}' created from validation",
            user_id,
            {"case_id": str(legal_case.id)},
            legal_case.id
        )
        
        return legal_case
    
    def update_case_from_new_analysis(
        self,
        case_id: uuid.UUID,
        validation_id: uuid.UUID,
        user_id: uuid.UUID
    ) -> LegalCase:
        """
        Update an existing case with new AI-extracted information
        
        Args:
            case_id: ID of the existing case
            validation_id: ID of the new validation
            user_id: ID of the user performing the update
            
        Returns:
            LegalCase: The updated legal case
        """
        # Get existing case
        legal_case = self.session.get(LegalCase, case_id)
        if not legal_case:
            raise ValueError(f"Legal case {case_id} not found")
        
        # Get validation
        validation = self.session.get(UserValidation, validation_id)
        if not validation:
            raise ValueError(f"Validation {validation_id} not found")
        
        if validation.validation_status != ValidationStatus.APPROVED:
            raise ValueError("Validation must be approved before updating case")
        
        # Get extracted information
        extracted_info = self.session.get(ExtractedInformation, validation.extracted_info_id)
        if not extracted_info:
            raise ValueError("Extracted information not found")
        
        # Track changes
        changes = {}
        
        # Update case with new information
        if validation.validated_data:
            validated_data = validation.validated_data
            
            # Update basic case information
            if "title" in validated_data and validated_data["title"]:
                old_title = legal_case.title
                legal_case.title = validated_data["title"]
                changes["title"] = {"old": old_title, "new": legal_case.title}
            
            if "client_name" in validated_data and validated_data["client_name"]:
                old_client = legal_case.client_name
                legal_case.client_name = validated_data["client_name"]
                changes["client_name"] = {"old": old_client, "new": legal_case.client_name}
            
            if "case_type" in validated_data and validated_data["case_type"]:
                old_type = legal_case.case_type
                legal_case.case_type = validated_data["case_type"]
                changes["case_type"] = {"old": old_type.value, "new": legal_case.case_type.value}
            
            if "description" in validated_data and validated_data["description"]:
                old_description = legal_case.description
                legal_case.description = validated_data["description"]
                changes["description"] = {"old": old_description, "new": legal_case.description}
        
        # Update from extracted information if not in validated data
        if extracted_info.suggested_case_type and not legal_case.case_type:
            old_type = legal_case.case_type
            legal_case.case_type = extracted_info.suggested_case_type
            changes["case_type"] = {"old": old_type.value if old_type else None, "new": legal_case.case_type.value}
        
        if extracted_info.document_summary and not legal_case.description:
            old_description = legal_case.description
            legal_case.description = extracted_info.document_summary
            changes["description"] = {"old": old_description, "new": legal_case.description}
        
        # Save changes
        self.session.commit()
        self.session.refresh(legal_case)
        
        # Create case activity
        self._create_case_activity(
            legal_case.id,
            ActivityType.CASE_UPDATED,
            f"Case updated from AI analysis validation",
            user_id,
            {
                "validation_id": str(validation_id),
                "analysis_id": str(extracted_info.analysis_id),
                "changes": changes,
                "ai_updated": True
            }
        )
        
        # Create analysis history
        self._create_analysis_history(
            extracted_info.analysis_id,
            "case_updated",
            f"Legal case '{legal_case.title}' updated from validation",
            user_id,
            {"case_id": str(legal_case.id), "changes": changes},
            legal_case.id
        )
        
        return legal_case
    
    def _build_case_data(
        self, 
        validation: UserValidation, 
        extracted_info: ExtractedInformation,
        user_id: uuid.UUID
    ) -> LegalCaseCreate:
        """Build case creation data from validation and extracted information"""
        
        # Start with validated data
        validated_data = validation.validated_data or {}
        
        # Use suggested values from validation or extracted info
        title = (
            validation.suggested_case_title or 
            validated_data.get("title") or
            f"Case from document analysis - {datetime.now().strftime('%Y-%m-%d')}"
        )
        
        client_name = (
            validation.suggested_client_name or
            validated_data.get("client_name") or
            extracted_info.plaintiff or
            "Unknown Client"
        )
        
        case_type = (
            validated_data.get("case_type") or
            extracted_info.suggested_case_type or
            "other"
        )
        
        description = (
            validated_data.get("description") or
            extracted_info.document_summary or
            "Case created from AI document analysis"
        )
        
        # Get user to determine lawyer_id
        user = self.session.get(User, user_id)
        if not user:
            raise ValueError(f"User {user_id} not found")
        
        lawyer_id = user_id if user.role.value == "lawyer" else user.assigned_lawyer_id
        if not lawyer_id:
            raise ValueError("No lawyer assigned for case creation")
        
        return LegalCaseCreate(
            title=title,
            client_name=client_name,
            case_type=case_type,
            description=description,
            lawyer_id=lawyer_id,
            status=CaseStatus.OPEN,
            priority=CasePriority.MEDIUM,
            opening_date=date.today()
        )
    
    def _create_case_activity(
        self,
        case_id: uuid.UUID,
        activity_type: ActivityType,
        description: str,
        user_id: uuid.UUID,
        metadata: Optional[Dict[str, Any]] = None
    ):
        """Create a case activity record"""
        activity = CaseActivity(
            case_id=case_id,
            activity_type=activity_type.value,
            description=description,
            user_id=user_id,
            activity_metadata=metadata
        )
        self.session.add(activity)
        self.session.commit()
    
    def _create_analysis_history(
        self,
        analysis_id: uuid.UUID,
        action_type: str,
        description: str,
        user_id: uuid.UUID,
        changes: Optional[Dict[str, Any]] = None,
        case_id: Optional[uuid.UUID] = None
    ):
        """Create an analysis history record"""
        history = AnalysisHistory(
            case_id=case_id,
            document_analysis_id=analysis_id,
            action_type=action_type,
            action_description=description,
            performed_by=user_id,
            changes_made=changes
        )
        self.session.add(history)
        self.session.commit()
    
    def get_pending_validations(self, user_id: uuid.UUID) -> List[UserValidation]:
        """Get pending validations for a user"""
        query = select(UserValidation).where(
            UserValidation.validated_by == user_id,
            UserValidation.validation_status == ValidationStatus.PENDING
        )
        return list(self.session.exec(query).all())
    
    def get_approved_validations_for_case_creation(self, user_id: uuid.UUID) -> List[UserValidation]:
        """Get approved validations that are ready for case creation"""
        query = select(UserValidation).where(
            UserValidation.validated_by == user_id,
            UserValidation.validation_status == ValidationStatus.APPROVED,
            UserValidation.should_create_case == True
        )
        return list(self.session.exec(query).all())

    def suggest_case_updates_from_analysis(
        self,
        case_id: uuid.UUID,
        analysis_id: uuid.UUID
    ) -> Dict[str, Any]:
        """
        Suggest updates to an existing case based on new document analysis

        Args:
            case_id: ID of the existing case
            analysis_id: ID of the new analysis

        Returns:
            Dict with suggested updates
        """
        # Get existing case
        legal_case = self.session.get(LegalCase, case_id)
        if not legal_case:
            raise ValueError(f"Legal case {case_id} not found")

        # Get analysis and extracted info
        analysis = self.session.get(DocumentAnalysis, analysis_id)
        if not analysis:
            raise ValueError(f"Analysis {analysis_id} not found")

        extracted_info = self.session.exec(
            select(ExtractedInformation).where(ExtractedInformation.analysis_id == analysis_id)
        ).first()

        if not extracted_info:
            raise ValueError("No extracted information found for analysis")

        suggestions = {
            "case_id": str(case_id),
            "analysis_id": str(analysis_id),
            "current_case": {
                "title": legal_case.title,
                "client_name": legal_case.client_name,
                "case_type": legal_case.case_type.value,
                "description": legal_case.description,
            },
            "suggested_updates": {},
            "new_information": {},
            "confidence_level": "medium"
        }

        # Compare and suggest updates
        if extracted_info.suggested_case_type and extracted_info.suggested_case_type != legal_case.case_type:
            suggestions["suggested_updates"]["case_type"] = {
                "current": legal_case.case_type.value,
                "suggested": extracted_info.suggested_case_type.value,
                "confidence": extracted_info.case_type_confidence.value if extracted_info.case_type_confidence else "medium"
            }

        # Add new information from document
        if extracted_info.plaintiff and extracted_info.plaintiff not in (legal_case.client_name or ""):
            suggestions["new_information"]["additional_plaintiff"] = extracted_info.plaintiff

        if extracted_info.defendant:
            suggestions["new_information"]["defendant"] = extracted_info.defendant

        if extracted_info.witnesses:
            suggestions["new_information"]["witnesses"] = extracted_info.witnesses

        if extracted_info.key_facts:
            suggestions["new_information"]["key_facts"] = extracted_info.key_facts

        if extracted_info.important_dates:
            suggestions["new_information"]["important_dates"] = extracted_info.important_dates

        if extracted_info.amounts:
            suggestions["new_information"]["amounts"] = extracted_info.amounts

        if extracted_info.legal_references:
            suggestions["new_information"]["legal_references"] = extracted_info.legal_references

        # Suggest description enhancement
        if extracted_info.document_summary:
            current_desc = legal_case.description or ""
            if extracted_info.document_summary not in current_desc:
                suggestions["suggested_updates"]["description"] = {
                    "current": current_desc,
                    "suggested_addition": extracted_info.document_summary,
                    "action": "append"
                }

        # Calculate overall confidence
        has_updates = len(suggestions["suggested_updates"]) > 0
        has_new_info = len(suggestions["new_information"]) > 0

        if has_updates and has_new_info:
            suggestions["confidence_level"] = "high"
        elif has_updates or has_new_info:
            suggestions["confidence_level"] = "medium"
        else:
            suggestions["confidence_level"] = "low"

        return suggestions
