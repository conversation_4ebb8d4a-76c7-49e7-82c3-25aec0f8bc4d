import os
import logging
from typing import Optional
import pytesseract
from pdf2image import convert_from_path
from PIL import Image
import pypdf
from pathlib import Path

logger = logging.getLogger(__name__)

class OCRService:
    def __init__(self):
        # Try to configure Tesseract path (adjust based on your system)
        self.tesseract_available = self._check_tesseract()
        
    def _check_tesseract(self) -> bool:
        """Check if Tesseract is available"""
        try:
            pytesseract.get_tesseract_version()
            return True
        except Exception as e:
            logger.warning(f"Tesseract not available: {e}")
            return False

    def extract_text_from_file(self, file_path: str, filename: str) -> str:
        """
        Extract text from various file formats
        """
        file_ext = Path(filename).suffix.lower()
        
        try:
            if file_ext == '.pdf':
                return self._extract_from_pdf(file_path)
            elif file_ext in ['.jpg', '.jpeg', '.png', '.tiff', '.bmp']:
                return self._extract_from_image(file_path)
            elif file_ext in ['.doc', '.docx']:
                return self._extract_from_doc(file_path)
            else:
                logger.warning(f"Unsupported file type: {file_ext}")
                return self._create_fallback_text(filename)
                
        except Exception as e:
            logger.error(f"OCR extraction failed for {filename}: {str(e)}")
            return self._create_fallback_text(filename)

    def _extract_from_pdf(self, file_path: str) -> str:
        """Extract text from PDF using PyPDF and OCR fallback"""
        text = ""
        
        try:
            # First try to extract text directly from PDF
            with open(file_path, 'rb') as file:
                pdf_reader = pypdf.PdfReader(file)
                for page in pdf_reader.pages:
                    page_text = page.extract_text()
                    if page_text.strip():
                        text += page_text + "\n"
            
            # If we got meaningful text, return it
            if len(text.strip()) > 100:
                logger.info("Successfully extracted text from PDF using PyPDF")
                return text.strip()
            
            # If not enough text, try OCR on PDF images
            if self.tesseract_available:
                logger.info("Falling back to OCR for PDF")
                return self._ocr_pdf_images(file_path)
            else:
                logger.warning("No OCR available, using fallback text")
                return self._create_fallback_text(os.path.basename(file_path))
                
        except Exception as e:
            logger.error(f"PDF text extraction failed: {str(e)}")
            return self._create_fallback_text(os.path.basename(file_path))

    def _ocr_pdf_images(self, file_path: str) -> str:
        """Convert PDF to images and OCR them"""
        try:
            # Convert PDF to images
            images = convert_from_path(file_path, dpi=300, first_page=1, last_page=5)  # Limit to first 5 pages
            
            text = ""
            for i, image in enumerate(images):
                logger.info(f"OCR processing page {i+1}")
                page_text = pytesseract.image_to_string(image, lang='eng+fra')  # English + French
                text += f"\n--- Page {i+1} ---\n{page_text}\n"
            
            return text.strip()
            
        except Exception as e:
            logger.error(f"PDF OCR failed: {str(e)}")
            return self._create_fallback_text(os.path.basename(file_path))

    def _extract_from_image(self, file_path: str) -> str:
        """Extract text from image using OCR"""
        if not self.tesseract_available:
            return self._create_fallback_text(os.path.basename(file_path))
            
        try:
            image = Image.open(file_path)
            text = pytesseract.image_to_string(image, lang='eng+fra')  # English + French
            
            if text.strip():
                logger.info("Successfully extracted text from image using OCR")
                return text.strip()
            else:
                return self._create_fallback_text(os.path.basename(file_path))
                
        except Exception as e:
            logger.error(f"Image OCR failed: {str(e)}")
            return self._create_fallback_text(os.path.basename(file_path))

    def _extract_from_doc(self, file_path: str) -> str:
        """Extract text from DOC/DOCX files"""
        try:
            # For now, return fallback - would need python-docx for full implementation
            logger.warning("DOC/DOCX extraction not implemented, using fallback")
            return self._create_fallback_text(os.path.basename(file_path))
            
        except Exception as e:
            logger.error(f"DOC extraction failed: {str(e)}")
            return self._create_fallback_text(os.path.basename(file_path))

    def _create_fallback_text(self, filename: str) -> str:
        """Create fallback text when OCR fails"""
        return f"""
Document: {filename}

[OCR/Text extraction not available or failed]

This document has been uploaded for legal analysis. The system was unable to extract 
text content automatically. Manual review may be required.

Document type: Legal document
Status: Requires manual text extraction
Uploaded: {filename}

Key areas to review manually:
- Parties involved
- Contract terms and conditions  
- Important dates and deadlines
- Financial amounts and obligations
- Legal clauses and provisions

Please review the original document for complete analysis.
"""

    def get_extraction_confidence(self, extracted_text: str) -> float:
        """
        Estimate confidence of text extraction based on content quality
        """
        if "[OCR/Text extraction not available" in extracted_text:
            return 0.1
        
        # Simple heuristics for confidence
        text_length = len(extracted_text.strip())
        word_count = len(extracted_text.split())
        
        if text_length < 50:
            return 0.3
        elif text_length < 200:
            return 0.6
        elif word_count > 100:
            return 0.9
        else:
            return 0.7

# Global instance
ocr_service = OCRService()
