"""
AI Document Analysis Service

This service handles the extraction of legal information from documents using OpenAI
and OCR technologies. It processes documents, extracts text, and uses AI to identify
relevant legal information for case creation.
"""

import uuid
import json
import logging
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime
from pathlib import Path

import openai
import pytesseract
from PIL import Image
from pdf2image import convert_from_path
import pypdf

from sqlmodel import Session, select

from app.core.config import settings
from app.models import (
    DocumentAnalysis,
    DocumentAnalysisCreate,
    ExtractedInformation,
    ExtractedInformationCreate,
    AnalysisHistory,
    CaseDocument,
    AnalysisStatus,
    ExtractionConfidence,
    DocumentProcessingType,
    CaseType,
)

logger = logging.getLogger(__name__)


class AIDocumentAnalysisService:
    """Service for AI-powered document analysis and information extraction"""
    
    def __init__(self, session: Session):
        self.session = session
        self.openai_client = openai.OpenAI(api_key=settings.OPENAI_API_KEY)
    
    async def analyze_document(
        self, 
        document_id: uuid.UUID, 
        user_id: uuid.UUID,
        analysis_type: DocumentProcessingType = DocumentProcessingType.AI_ANALYSIS
    ) -> DocumentAnalysis:
        """
        Main method to analyze a document and extract legal information
        
        Args:
            document_id: ID of the document to analyze
            user_id: ID of the user requesting the analysis
            analysis_type: Type of analysis to perform
            
        Returns:
            DocumentAnalysis object with results
        """
        # Get document from database
        document = self.session.get(CaseDocument, document_id)
        if not document:
            raise ValueError(f"Document {document_id} not found")
        
        # Create analysis record
        analysis = DocumentAnalysis(
            document_id=document_id,
            analysis_type=analysis_type,
            status=AnalysisStatus.IN_PROGRESS,
            analyzed_by=user_id,
            processing_metadata={"started_at": datetime.utcnow().isoformat()}
        )
        self.session.add(analysis)
        self.session.commit()
        self.session.refresh(analysis)
        
        try:
            # Extract text from document
            extracted_text = await self._extract_text_from_document(document)
            
            # Update analysis with extracted text
            analysis.extracted_text = extracted_text
            analysis.processing_metadata["text_extraction_completed"] = datetime.utcnow().isoformat()
            self.session.commit()
            
            if analysis_type == DocumentProcessingType.OCR_ONLY:
                analysis.status = AnalysisStatus.COMPLETED
                self.session.commit()
                return analysis
            
            # Perform AI analysis
            extracted_info = await self._perform_ai_analysis(analysis, extracted_text)
            
            # Update status
            analysis.status = AnalysisStatus.REQUIRES_VALIDATION
            analysis.confidence_score = self._calculate_overall_confidence(extracted_info)
            analysis.processing_metadata["ai_analysis_completed"] = datetime.utcnow().isoformat()
            self.session.commit()
            
            # Create history record
            self._create_history_record(
                analysis.id,
                "analysis_completed",
                f"AI analysis completed for document {document.original_filename}",
                user_id
            )
            
            return analysis
            
        except Exception as e:
            logger.error(f"Error analyzing document {document_id}: {str(e)}")
            analysis.status = AnalysisStatus.FAILED
            analysis.error_message = str(e)
            self.session.commit()
            raise
    
    async def _extract_text_from_document(self, document: CaseDocument) -> str:
        """Extract text from document using appropriate method based on file type"""
        file_path = Path(document.file_path)
        
        if not file_path.exists():
            raise FileNotFoundError(f"Document file not found: {file_path}")
        
        content_type = document.content_type.lower()
        
        if content_type == "application/pdf":
            return await self._extract_text_from_pdf(file_path)
        elif content_type.startswith("image/"):
            return await self._extract_text_from_image(file_path)
        else:
            raise ValueError(f"Unsupported file type: {content_type}")
    
    async def _extract_text_from_pdf(self, file_path: Path) -> str:
        """Extract text from PDF file"""
        text = ""
        
        try:
            # Try to extract text directly from PDF first
            with open(file_path, 'rb') as file:
                pdf_reader = pypdf.PdfReader(file)
                for page in pdf_reader.pages:
                    page_text = page.extract_text()
                    if page_text.strip():
                        text += page_text + "\n"
            
            # If no text extracted, use OCR
            if not text.strip():
                logger.info(f"No text found in PDF {file_path}, using OCR")
                text = await self._extract_text_from_pdf_with_ocr(file_path)
                
        except Exception as e:
            logger.warning(f"Error extracting text from PDF {file_path}: {e}")
            # Fallback to OCR
            text = await self._extract_text_from_pdf_with_ocr(file_path)
        
        return text.strip()
    
    async def _extract_text_from_pdf_with_ocr(self, file_path: Path) -> str:
        """Extract text from PDF using OCR"""
        text = ""
        
        # Convert PDF to images
        images = convert_from_path(file_path)
        
        for i, image in enumerate(images):
            logger.info(f"Processing page {i+1} of {len(images)} from {file_path}")
            page_text = pytesseract.image_to_string(image, lang='fra+eng')
            text += page_text + "\n"
        
        return text.strip()
    
    async def _extract_text_from_image(self, file_path: Path) -> str:
        """Extract text from image file using OCR"""
        image = Image.open(file_path)
        text = pytesseract.image_to_string(image, lang='fra+eng')
        return text.strip()
    
    async def _perform_ai_analysis(
        self, 
        analysis: DocumentAnalysis, 
        text: str
    ) -> ExtractedInformation:
        """Perform AI analysis of extracted text to identify legal information"""
        
        prompt = self._build_analysis_prompt(text)
        
        try:
            response = self.openai_client.chat.completions.create(
                model="gpt-4",
                messages=[
                    {
                        "role": "system",
                        "content": "You are a legal document analysis expert. Extract relevant legal information from documents and return structured JSON data."
                    },
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                temperature=0.1,
                max_tokens=2000
            )
            
            # Parse AI response
            ai_response = response.choices[0].message.content
            extracted_data = json.loads(ai_response)
            
            # Create ExtractedInformation record
            extracted_info = ExtractedInformation(
                analysis_id=analysis.id,
                **self._parse_ai_response(extracted_data)
            )
            
            self.session.add(extracted_info)
            self.session.commit()
            self.session.refresh(extracted_info)
            
            return extracted_info
            
        except Exception as e:
            logger.error(f"Error in AI analysis: {str(e)}")
            raise
    
    def _build_analysis_prompt(self, text: str) -> str:
        """Build the prompt for AI analysis"""
        return f"""
Analyze the following legal document and extract relevant information. Return a JSON object with the following structure:

{{
    "plaintiff": "Name of the plaintiff/demandeur (if any)",
    "defendant": "Name of the defendant/défendeur (if any)",
    "witnesses": ["List of witness names"],
    "lawyers": ["List of lawyer names mentioned"],
    "suggested_case_type": "One of: civil, criminal, family, corporate, immigration, real_estate, intellectual_property, employment, other",
    "case_type_confidence": "One of: low, medium, high, very_high",
    "document_summary": "Brief summary of the document (max 500 words)",
    "key_facts": ["List of key facts from the document"],
    "important_dates": {{"description": "YYYY-MM-DD format"}},
    "amounts": {{"description": numeric_value}},
    "legal_references": ["List of legal articles, laws, or jurisprudence mentioned"],
    "additional_data": {{"any_other_relevant_info": "value"}}
}}

Document text:
{text[:4000]}  # Limit text to avoid token limits

Respond only with valid JSON, no additional text.
"""
    
    def _parse_ai_response(self, ai_data: Dict[str, Any]) -> Dict[str, Any]:
        """Parse and validate AI response data"""
        # Map case type string to enum
        case_type_str = ai_data.get("suggested_case_type")
        case_type = None
        if case_type_str:
            try:
                case_type = CaseType(case_type_str.lower())
            except ValueError:
                case_type = CaseType.OTHER
        
        # Map confidence string to enum
        confidence_str = ai_data.get("case_type_confidence")
        confidence = None
        if confidence_str:
            try:
                confidence = ExtractionConfidence(confidence_str.lower())
            except ValueError:
                confidence = ExtractionConfidence.MEDIUM
        
        return {
            "plaintiff": ai_data.get("plaintiff"),
            "defendant": ai_data.get("defendant"),
            "witnesses": ai_data.get("witnesses", []),
            "lawyers": ai_data.get("lawyers", []),
            "suggested_case_type": case_type,
            "case_type_confidence": confidence,
            "document_summary": ai_data.get("document_summary"),
            "key_facts": ai_data.get("key_facts", []),
            "important_dates": ai_data.get("important_dates", {}),
            "amounts": ai_data.get("amounts", {}),
            "legal_references": ai_data.get("legal_references", []),
            "additional_data": ai_data.get("additional_data", {})
        }
    
    def _calculate_overall_confidence(self, extracted_info: ExtractedInformation) -> float:
        """Calculate overall confidence score based on extracted information"""
        confidence_scores = {
            ExtractionConfidence.LOW: 0.25,
            ExtractionConfidence.MEDIUM: 0.55,
            ExtractionConfidence.HIGH: 0.8,
            ExtractionConfidence.VERY_HIGH: 0.95
        }
        
        base_confidence = confidence_scores.get(
            extracted_info.case_type_confidence, 
            0.5
        )
        
        # Adjust based on amount of extracted information
        info_count = sum([
            1 if extracted_info.plaintiff else 0,
            1 if extracted_info.defendant else 0,
            len(extracted_info.witnesses or []),
            len(extracted_info.key_facts or []),
            len(extracted_info.important_dates or {}),
            len(extracted_info.legal_references or [])
        ])
        
        # Boost confidence if we have more information
        confidence_boost = min(0.2, info_count * 0.02)
        
        return min(1.0, base_confidence + confidence_boost)
    
    def _create_history_record(
        self,
        analysis_id: uuid.UUID,
        action_type: str,
        description: str,
        user_id: uuid.UUID,
        changes: Optional[Dict[str, Any]] = None
    ):
        """Create a history record for tracking analysis actions"""
        history = AnalysisHistory(
            document_analysis_id=analysis_id,
            action_type=action_type,
            action_description=description,
            performed_by=user_id,
            changes_made=changes
        )
        self.session.add(history)
        self.session.commit()
