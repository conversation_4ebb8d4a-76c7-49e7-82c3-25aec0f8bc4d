import json
import logging
from typing import Dict, List, Optional
from openai import OpenAI
from app.core.config import settings

logger = logging.getLogger(__name__)

class OpenAIService:
    def __init__(self):
        if settings.OPENAI_API_KEY:
            self.client = OpenAI(api_key=settings.OPENAI_API_KEY)
            self.enabled = True
        else:
            self.client = None
            self.enabled = False
            logger.warning("OpenAI API key not configured, using simulation mode")

    def analyze_legal_document(self, extracted_text: str, filename: str, output_language: str = "auto") -> Dict:
        """
        Analyze legal document using OpenAI GPT-4

        Args:
            extracted_text: Text extracted from document
            filename: Name of the document
            output_language: Language for analysis output ("auto", "french", "arabic", "original")
        """
        if not self.enabled:
            return self._simulate_analysis(extracted_text, filename, output_language)

        try:
            logger.info(f"🤖 Starting OpenAI analysis for {filename} (output: {output_language})")
            prompt = self._create_analysis_prompt(extracted_text, filename, output_language)

            response = self.client.chat.completions.create(
                model=settings.OPENAI_MODEL,
                messages=[
                    {
                        "role": "system",
                        "content": "Tu es un assistant juridique expert spécialisé dans l'analyse de documents légaux français. Réponds UNIQUEMENT avec du JSON valide."
                    },
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                max_tokens=settings.OPENAI_MAX_TOKENS,
                temperature=0.1  # Low temperature for consistent results
            )

            # Parse the JSON response
            raw_content = response.choices[0].message.content
            logger.info(f"📄 OpenAI raw response: {raw_content[:200]}...")

            analysis = json.loads(raw_content)
            logger.info(f"✅ OpenAI analysis completed successfully")

            # Validate and structure the response
            return self._structure_analysis_response(analysis, extracted_text)

        except json.JSONDecodeError as e:
            logger.error(f"❌ OpenAI returned invalid JSON: {str(e)}")
            return self._simulate_analysis(extracted_text, filename)
        except Exception as e:
            logger.error(f"❌ OpenAI analysis failed: {str(e)}")
            # Fallback to simulation if OpenAI fails
            return self._simulate_analysis(extracted_text, filename)

    def _create_analysis_prompt(self, extracted_text: str, filename: str, output_language: str = "auto") -> str:
        """
        Create a structured prompt for legal document analysis with language preference
        """
        # Determine language instruction based on user choice
        language_instructions = {
            "auto": "Si le texte contient de l'arabe, traduis les éléments clés en français dans ton analyse. Utilise la langue la plus appropriée pour l'avocat tunisien.",
            "french": "Réponds UNIQUEMENT en français. Traduis tous les éléments arabes en français dans ton analyse.",
            "arabic": "أجب باللغة العربية فقط. ترجم جميع العناصر الفرنسية إلى العربية في تحليلك.",
            "original": "Conserve la langue d'origine du document. N'effectue aucune traduction."
        }

        language_instruction = language_instructions.get(output_language, language_instructions["auto"])

        return f"""
Tu es un assistant juridique expert spécialisé dans l'analyse de documents légaux tunisiens. Tu maîtrises parfaitement le droit tunisien, français et les documents bilingues arabe-français. Analyse le document suivant et extrais les informations clés.

IMPORTANT: Réponds UNIQUEMENT avec un objet JSON valide, sans texte supplémentaire.

Structure JSON requise:
{{
    "document_type": "string (ex: 'contrat_vente', 'contrat_achat', 'bail', 'testament', 'procuration', 'acte_notarie', 'jugement', 'requete')",
    "confidence": "float entre 0 et 1 basé sur la clarté du document",
    "language_detected": "string (ara, fra, ara+fra, ou autre)",
    "key_entities": [
        "Liste des entités importantes: noms de personnes, entreprises, biens, montants, dates, numéros CIN/passeport"
    ],
    "suggested_actions": [
        "Liste d'actions recommandées basées sur le type de document et le droit tunisien"
    ],
    "parties_involved": {{
        "acheteur": "nom de l'acheteur ou null",
        "vendeur": "nom du vendeur ou null",
        "locataire": "nom du locataire ou null",
        "proprietaire": "nom du propriétaire ou null",
        "demandeur": "nom du demandeur ou null",
        "defendeur": "nom du défendeur ou null",
        "notaire": "nom du notaire ou null",
        "autres_parties": ["autres parties impliquées"]
    }},
    "important_dates": {{
        "date_signature": "YYYY-MM-DD ou null",
        "date_effet": "YYYY-MM-DD ou null",
        "date_expiration": "YYYY-MM-DD ou null",
        "echeance": "YYYY-MM-DD ou null",
        "date_audience": "YYYY-MM-DD ou null"
    }},
    "financial_information": {{
        "montants": [
            {{"description": "description", "montant": "nombre", "devise": "TND/EUR/USD"}}
        ],
        "modalites_paiement": "string ou null"
    }},
    "legal_references": [
        "références légales citées (Code des Obligations et Contrats, Code Civil, etc.)"
    ],
    "tunisian_specifics": {{
        "cin_numbers": ["numéros CIN mentionnés"],
        "property_titles": ["titres fonciers mentionnés"],
        "court_references": ["références de tribunaux tunisiens"],
        "notary_references": ["références notariales"]
    }},
    "summary": "résumé concis du document et de son objectif",
    "risk_assessment": "évaluation des risques juridiques selon le droit tunisien",
    "urgency_level": "low, medium, ou high",
    "compliance_notes": "notes sur la conformité avec la législation tunisienne"
}}

Nom du fichier: {filename}

Contenu du document:
{extracted_text[:6000]}

INSTRUCTION DE LANGUE: {language_instruction}

Analyse ce document en tenant compte du contexte juridique tunisien. Fournis une extraction précise des informations. Si certaines informations ne sont pas disponibles, utilise null.
"""

    def _structure_analysis_response(self, analysis: Dict, extracted_text: str) -> Dict:
        """
        Structure and validate the OpenAI response
        """
        return {
            "document_type": analysis.get("document_type", "legal_document"),
            "confidence": min(max(analysis.get("confidence", 0.8), 0.0), 1.0),
            "key_entities": analysis.get("key_entities", []),
            "suggested_actions": analysis.get("suggested_actions", []),
            "extracted_info": {
                "plaintiff": analysis.get("parties_involved", {}).get("plaintiff"),
                "defendant": analysis.get("parties_involved", {}).get("defendant"),
                "buyer": analysis.get("parties_involved", {}).get("buyer"),
                "seller": analysis.get("parties_involved", {}).get("seller"),
                "case_type": self._determine_case_type(analysis.get("document_type", "")),
                "case_title": f"Legal Case - {analysis.get('document_type', 'Document')}",
                "document_summary": analysis.get("summary", extracted_text[:500]),
                "key_facts": analysis.get("suggested_actions", []),
                "important_dates": analysis.get("important_dates", {}),
                "amounts": analysis.get("financial_information", {}).get("amounts", []),
                "legal_references": analysis.get("legal_references", []),
                "risk_assessment": analysis.get("risk_assessment"),
                "urgency_level": analysis.get("urgency_level", "medium")
            }
        }

    def _determine_case_type(self, document_type: str) -> str:
        """
        Map document type to legal case type
        """
        type_mapping = {
            "contract": "contract",
            "purchase_agreement": "real_estate",
            "employment_contract": "employment",
            "lease_agreement": "real_estate",
            "court_filing": "civil",
            "divorce_papers": "family",
            "will": "estate",
            "power_of_attorney": "estate",
            "business_agreement": "corporate",
            "nda": "corporate",
            "settlement": "civil"
        }
        return type_mapping.get(document_type.lower(), "other")

    def _simulate_analysis(self, extracted_text: str, filename: str, output_language: str = "auto") -> Dict:
        """
        Fallback simulation when OpenAI is not available
        """
        # Enhanced simulation based on filename and content patterns (Tunisian context)
        if "contrat" in filename.lower() or "عقد" in extracted_text or "contract" in extracted_text.lower():
            return {
                "document_type": "contrat_vente",
                "confidence": 0.85,
                "language_detected": "fra+ara",
                "key_entities": [
                    "Acheteur: أحمد بن علي (Ahmed Ben Ali)",
                    "Vendeur: فاطمة الزهراء (Fatma Zahra)",
                    "Propriété: شارع الحبيب بورقيبة، تونس (Avenue Habib Bourguiba, Tunis)",
                    "Montant: 150,000 TND",
                    "CIN: 12345678",
                    "Date: 2024-01-15"
                ],
                "suggested_actions": [
                    "Créer dossier de vente immobilière",
                    "Vérifier conformité Code des Obligations",
                    "Programmer rendez-vous notaire",
                    "Contrôler titre foncier"
                ],
                "tunisian_specifics": {
                    "cin_numbers": ["12345678", "87654321"],
                    "property_titles": ["TF 123456 Tunis"],
                    "court_references": ["Tribunal de Première Instance de Tunis"],
                    "notary_references": ["Notaire Maître Mohamed Trabelsi"]
                },
                "extracted_info": {
                    "plaintiff": None,
                    "defendant": None,
                    "buyer": "Ahmed Ben Ali",
                    "seller": "Fatma Zahra",
                    "case_type": "real_estate",
                    "case_title": "Contrat de Vente Immobilière - Tunisie",
                    "document_summary": f"Contrat de vente immobilière tunisien pour {filename}. Conforme au Code des Obligations et Contrats tunisien.",
                    "key_facts": ["Montant: 150,000 TND", "Transfert de propriété", "Intervention notariale obligatoire"],
                    "important_dates": {"signature_date": "2024-01-15"},
                    "amounts": [{"description": "Prix de vente", "amount": 150000, "currency": "TND"}],
                    "legal_references": ["Code des Obligations et Contrats", "Code des Droits Réels"],
                    "risk_assessment": "Transaction immobilière standard avec vérifications réglementaires tunisiennes",
                    "urgency_level": "medium",
                    "compliance_notes": "Conforme à la législation tunisienne en vigueur"
                }
            }
        else:
            return {
                "document_type": "legal_document",
                "confidence": 0.75,
                "key_entities": ["Document Analysis", "Legal Review"],
                "suggested_actions": ["Review document", "Identify parties", "Assess legal implications"],
                "extracted_info": {
                    "plaintiff": None,
                    "defendant": None,
                    "buyer": None,
                    "seller": None,
                    "case_type": "other",
                    "case_title": f"Legal Document Analysis - {filename}",
                    "document_summary": f"Legal document requiring analysis: {filename}",
                    "key_facts": ["Document uploaded", "Analysis required"],
                    "important_dates": {},
                    "amounts": [],
                    "legal_references": [],
                    "risk_assessment": "Requires manual review",
                    "urgency_level": "medium"
                }
            }

# Global instance
openai_service = OpenAIService()
