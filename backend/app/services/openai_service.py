import json
import logging
from typing import Dict, List, Optional
from openai import OpenAI
from app.core.config import settings

logger = logging.getLogger(__name__)

class OpenAIService:
    def __init__(self):
        if settings.OPENAI_API_KEY:
            self.client = OpenAI(api_key=settings.OPENAI_API_KEY)
            self.enabled = True
        else:
            self.client = None
            self.enabled = False
            logger.warning("OpenAI API key not configured, using simulation mode")

    def analyze_legal_document(self, extracted_text: str, filename: str) -> Dict:
        """
        Analyze legal document using OpenAI GPT-4
        """
        if not self.enabled:
            return self._simulate_analysis(extracted_text, filename)

        try:
            logger.info(f"🤖 Starting OpenAI analysis for {filename}")
            prompt = self._create_analysis_prompt(extracted_text, filename)

            response = self.client.chat.completions.create(
                model=settings.OPENAI_MODEL,
                messages=[
                    {
                        "role": "system",
                        "content": "Tu es un assistant juridique expert spécial<PERSON><PERSON> dans l'analyse de documents légaux français. Réponds UNIQUEMENT avec du JSON valide."
                    },
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                max_tokens=settings.OPENAI_MAX_TOKENS,
                temperature=0.1  # Low temperature for consistent results
            )

            # Parse the JSON response
            raw_content = response.choices[0].message.content
            logger.info(f"📄 OpenAI raw response: {raw_content[:200]}...")

            analysis = json.loads(raw_content)
            logger.info(f"✅ OpenAI analysis completed successfully")

            # Validate and structure the response
            return self._structure_analysis_response(analysis, extracted_text)

        except json.JSONDecodeError as e:
            logger.error(f"❌ OpenAI returned invalid JSON: {str(e)}")
            return self._simulate_analysis(extracted_text, filename)
        except Exception as e:
            logger.error(f"❌ OpenAI analysis failed: {str(e)}")
            # Fallback to simulation if OpenAI fails
            return self._simulate_analysis(extracted_text, filename)

    def _create_analysis_prompt(self, extracted_text: str, filename: str) -> str:
        """
        Create a structured prompt for legal document analysis
        """
        return f"""
Tu es un assistant juridique expert spécialisé dans l'analyse de documents légaux français. Analyse le document suivant et extrais les informations clés.

IMPORTANT: Réponds UNIQUEMENT avec un objet JSON valide, sans texte supplémentaire.

Structure JSON requise:
{{
    "document_type": "string (ex: 'contrat_vente', 'contrat_achat', 'bail', 'testament', 'procuration')",
    "confidence": "float entre 0 et 1 basé sur la clarté du document",
    "key_entities": [
        "Liste des entités importantes: noms de personnes, entreprises, biens, montants, dates"
    ],
    "suggested_actions": [
        "Liste d'actions recommandées basées sur le type de document"
    ],
    "parties_involved": {{
        "acheteur": "nom de l'acheteur ou null",
        "vendeur": "nom du vendeur ou null",
        "locataire": "nom du locataire ou null",
        "proprietaire": "nom du propriétaire ou null",
        "autres_parties": ["autres parties impliquées"]
    }},
    "important_dates": {{
        "date_signature": "YYYY-MM-DD ou null",
        "date_effet": "YYYY-MM-DD ou null",
        "date_expiration": "YYYY-MM-DD ou null",
        "echeance": "YYYY-MM-DD ou null"
    }},
    "financial_information": {{
        "montants": [
            {{"description": "description", "montant": "nombre", "devise": "EUR"}}
        ],
        "modalites_paiement": "string ou null"
    }},
    "legal_references": [
        "références légales citées (articles de loi, codes, etc.)"
    ],
    "summary": "résumé concis du document et de son objectif",
    "risk_assessment": "évaluation des risques juridiques potentiels",
    "urgency_level": "low, medium, ou high"
}}

Nom du fichier: {filename}

Contenu du document:
{extracted_text[:6000]}

Analyse ce document en français et fournis une extraction précise des informations. Si certaines informations ne sont pas disponibles, utilise null.
"""

    def _structure_analysis_response(self, analysis: Dict, extracted_text: str) -> Dict:
        """
        Structure and validate the OpenAI response
        """
        return {
            "document_type": analysis.get("document_type", "legal_document"),
            "confidence": min(max(analysis.get("confidence", 0.8), 0.0), 1.0),
            "key_entities": analysis.get("key_entities", []),
            "suggested_actions": analysis.get("suggested_actions", []),
            "extracted_info": {
                "plaintiff": analysis.get("parties_involved", {}).get("plaintiff"),
                "defendant": analysis.get("parties_involved", {}).get("defendant"),
                "buyer": analysis.get("parties_involved", {}).get("buyer"),
                "seller": analysis.get("parties_involved", {}).get("seller"),
                "case_type": self._determine_case_type(analysis.get("document_type", "")),
                "case_title": f"Legal Case - {analysis.get('document_type', 'Document')}",
                "document_summary": analysis.get("summary", extracted_text[:500]),
                "key_facts": analysis.get("suggested_actions", []),
                "important_dates": analysis.get("important_dates", {}),
                "amounts": analysis.get("financial_information", {}).get("amounts", []),
                "legal_references": analysis.get("legal_references", []),
                "risk_assessment": analysis.get("risk_assessment"),
                "urgency_level": analysis.get("urgency_level", "medium")
            }
        }

    def _determine_case_type(self, document_type: str) -> str:
        """
        Map document type to legal case type
        """
        type_mapping = {
            "contract": "contract",
            "purchase_agreement": "real_estate",
            "employment_contract": "employment",
            "lease_agreement": "real_estate",
            "court_filing": "civil",
            "divorce_papers": "family",
            "will": "estate",
            "power_of_attorney": "estate",
            "business_agreement": "corporate",
            "nda": "corporate",
            "settlement": "civil"
        }
        return type_mapping.get(document_type.lower(), "other")

    def _simulate_analysis(self, extracted_text: str, filename: str) -> Dict:
        """
        Fallback simulation when OpenAI is not available
        """
        # Enhanced simulation based on filename and content patterns
        if "contrat" in filename.lower() or "contract" in extracted_text.lower():
            return {
                "document_type": "contract",
                "confidence": 0.85,
                "key_entities": [
                    "Buyer: Jean Martin",
                    "Seller: Marie Dupont", 
                    "Property: 123 Rue de la Paix",
                    "Amount: €250,000",
                    "Date: 2024-01-15"
                ],
                "suggested_actions": [
                    "Create contract case",
                    "Schedule signature appointment",
                    "Verify property documents",
                    "Contact notary for closing"
                ],
                "extracted_info": {
                    "plaintiff": "Jean Martin",
                    "defendant": None,
                    "buyer": "Jean Martin",
                    "seller": "Marie Dupont",
                    "case_type": "real_estate",
                    "case_title": "Real Estate Purchase Contract",
                    "document_summary": f"Purchase contract analysis for {filename}. Contains standard real estate transaction terms and conditions.",
                    "key_facts": ["Purchase amount: €250,000", "Property transfer", "Notary required"],
                    "important_dates": {"signature_date": "2024-01-15"},
                    "amounts": [{"description": "Purchase price", "amount": 250000, "currency": "EUR"}],
                    "legal_references": [],
                    "risk_assessment": "Standard real estate transaction with typical risks",
                    "urgency_level": "medium"
                }
            }
        else:
            return {
                "document_type": "legal_document",
                "confidence": 0.75,
                "key_entities": ["Document Analysis", "Legal Review"],
                "suggested_actions": ["Review document", "Identify parties", "Assess legal implications"],
                "extracted_info": {
                    "plaintiff": None,
                    "defendant": None,
                    "buyer": None,
                    "seller": None,
                    "case_type": "other",
                    "case_title": f"Legal Document Analysis - {filename}",
                    "document_summary": f"Legal document requiring analysis: {filename}",
                    "key_facts": ["Document uploaded", "Analysis required"],
                    "important_dates": {},
                    "amounts": [],
                    "legal_references": [],
                    "risk_assessment": "Requires manual review",
                    "urgency_level": "medium"
                }
            }

# Global instance
openai_service = OpenAIService()
