import json
import logging
from typing import Dict, List, Optional
from openai import OpenAI
from app.core.config import settings

logger = logging.getLogger(__name__)

class OpenAIService:
    def __init__(self):
        if settings.OPENAI_API_KEY:
            self.client = OpenAI(api_key=settings.OPENAI_API_KEY)
            self.enabled = True
        else:
            self.client = None
            self.enabled = False
            logger.warning("OpenAI API key not configured, using simulation mode")

    def analyze_legal_document(self, extracted_text: str, filename: str) -> Dict:
        """
        Analyze legal document using OpenAI GPT-4
        """
        if not self.enabled:
            return self._simulate_analysis(extracted_text, filename)

        try:
            prompt = self._create_analysis_prompt(extracted_text, filename)
            
            response = self.client.chat.completions.create(
                model=settings.OPENAI_MODEL,
                messages=[
                    {
                        "role": "system",
                        "content": "You are a legal AI assistant specialized in analyzing legal documents. Extract key information and provide structured analysis."
                    },
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                max_tokens=settings.OPENAI_MAX_TOKENS,
                temperature=0.1,  # Low temperature for consistent results
                response_format={"type": "json_object"}
            )

            # Parse the JSON response
            analysis = json.loads(response.choices[0].message.content)
            
            # Validate and structure the response
            return self._structure_analysis_response(analysis, extracted_text)

        except Exception as e:
            logger.error(f"OpenAI analysis failed: {str(e)}")
            # Fallback to simulation if OpenAI fails
            return self._simulate_analysis(extracted_text, filename)

    def _create_analysis_prompt(self, extracted_text: str, filename: str) -> str:
        """
        Create a structured prompt for legal document analysis
        """
        return f"""
Analyze the following legal document and extract key information. Return a JSON object with the following structure:

{{
    "document_type": "string (e.g., 'contract', 'agreement', 'legal_notice', 'court_filing')",
    "confidence": "float between 0 and 1",
    "key_entities": [
        "list of important entities (people, companies, properties, amounts, dates)"
    ],
    "suggested_actions": [
        "list of recommended next steps or actions"
    ],
    "parties_involved": {{
        "plaintiff": "string or null",
        "defendant": "string or null",
        "buyer": "string or null", 
        "seller": "string or null",
        "other_parties": ["list of other involved parties"]
    }},
    "important_dates": {{
        "signature_date": "YYYY-MM-DD or null",
        "effective_date": "YYYY-MM-DD or null",
        "expiration_date": "YYYY-MM-DD or null",
        "deadline": "YYYY-MM-DD or null"
    }},
    "financial_information": {{
        "amounts": [
            {{"description": "string", "amount": "number", "currency": "string"}}
        ],
        "payment_terms": "string or null"
    }},
    "legal_references": [
        "list of cited laws, articles, or legal precedents"
    ],
    "summary": "brief summary of the document content and purpose",
    "risk_assessment": "string describing potential legal risks or concerns",
    "urgency_level": "low, medium, or high"
}}

Document filename: {filename}

Document content:
{extracted_text[:4000]}  # Limit to avoid token limits

Provide a thorough analysis focusing on legal implications and actionable insights.
"""

    def _structure_analysis_response(self, analysis: Dict, extracted_text: str) -> Dict:
        """
        Structure and validate the OpenAI response
        """
        return {
            "document_type": analysis.get("document_type", "legal_document"),
            "confidence": min(max(analysis.get("confidence", 0.8), 0.0), 1.0),
            "key_entities": analysis.get("key_entities", []),
            "suggested_actions": analysis.get("suggested_actions", []),
            "extracted_info": {
                "plaintiff": analysis.get("parties_involved", {}).get("plaintiff"),
                "defendant": analysis.get("parties_involved", {}).get("defendant"),
                "buyer": analysis.get("parties_involved", {}).get("buyer"),
                "seller": analysis.get("parties_involved", {}).get("seller"),
                "case_type": self._determine_case_type(analysis.get("document_type", "")),
                "case_title": f"Legal Case - {analysis.get('document_type', 'Document')}",
                "document_summary": analysis.get("summary", extracted_text[:500]),
                "key_facts": analysis.get("suggested_actions", []),
                "important_dates": analysis.get("important_dates", {}),
                "amounts": analysis.get("financial_information", {}).get("amounts", []),
                "legal_references": analysis.get("legal_references", []),
                "risk_assessment": analysis.get("risk_assessment"),
                "urgency_level": analysis.get("urgency_level", "medium")
            }
        }

    def _determine_case_type(self, document_type: str) -> str:
        """
        Map document type to legal case type
        """
        type_mapping = {
            "contract": "contract",
            "purchase_agreement": "real_estate",
            "employment_contract": "employment",
            "lease_agreement": "real_estate",
            "court_filing": "civil",
            "divorce_papers": "family",
            "will": "estate",
            "power_of_attorney": "estate",
            "business_agreement": "corporate",
            "nda": "corporate",
            "settlement": "civil"
        }
        return type_mapping.get(document_type.lower(), "other")

    def _simulate_analysis(self, extracted_text: str, filename: str) -> Dict:
        """
        Fallback simulation when OpenAI is not available
        """
        # Enhanced simulation based on filename and content patterns
        if "contrat" in filename.lower() or "contract" in extracted_text.lower():
            return {
                "document_type": "contract",
                "confidence": 0.85,
                "key_entities": [
                    "Buyer: Jean Martin",
                    "Seller: Marie Dupont", 
                    "Property: 123 Rue de la Paix",
                    "Amount: €250,000",
                    "Date: 2024-01-15"
                ],
                "suggested_actions": [
                    "Create contract case",
                    "Schedule signature appointment",
                    "Verify property documents",
                    "Contact notary for closing"
                ],
                "extracted_info": {
                    "plaintiff": "Jean Martin",
                    "defendant": None,
                    "buyer": "Jean Martin",
                    "seller": "Marie Dupont",
                    "case_type": "real_estate",
                    "case_title": "Real Estate Purchase Contract",
                    "document_summary": f"Purchase contract analysis for {filename}. Contains standard real estate transaction terms and conditions.",
                    "key_facts": ["Purchase amount: €250,000", "Property transfer", "Notary required"],
                    "important_dates": {"signature_date": "2024-01-15"},
                    "amounts": [{"description": "Purchase price", "amount": 250000, "currency": "EUR"}],
                    "legal_references": [],
                    "risk_assessment": "Standard real estate transaction with typical risks",
                    "urgency_level": "medium"
                }
            }
        else:
            return {
                "document_type": "legal_document",
                "confidence": 0.75,
                "key_entities": ["Document Analysis", "Legal Review"],
                "suggested_actions": ["Review document", "Identify parties", "Assess legal implications"],
                "extracted_info": {
                    "plaintiff": None,
                    "defendant": None,
                    "buyer": None,
                    "seller": None,
                    "case_type": "other",
                    "case_title": f"Legal Document Analysis - {filename}",
                    "document_summary": f"Legal document requiring analysis: {filename}",
                    "key_facts": ["Document uploaded", "Analysis required"],
                    "important_dates": {},
                    "amounts": [],
                    "legal_references": [],
                    "risk_assessment": "Requires manual review",
                    "urgency_level": "medium"
                }
            }

# Global instance
openai_service = OpenAIService()
