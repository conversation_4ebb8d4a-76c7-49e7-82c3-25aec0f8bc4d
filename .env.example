# EquiNova App - Environment Variables Example
# Copy this file to .env.local and fill in your actual values

# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_MODEL=gpt-4
OPENAI_MAX_TOKENS=2000

# Database Configuration
DATABASE_URL=postgresql://user:password@localhost:5432/equinova_db

# JWT Configuration
JWT_SECRET_KEY=your_jwt_secret_key_here
JWT_ALGORITHM=HS256
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=30

# Application Configuration
APP_NAME=EquiNova
APP_VERSION=1.0.0
DEBUG=false

# CORS Configuration
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:8080

# File Upload Configuration
MAX_FILE_SIZE_MB=10
ALLOWED_FILE_TYPES=pdf,jpg,jpeg,png,txt,doc,docx

# OCR Configuration
TESSERACT_LANGUAGES=ara+fra+eng
OCR_CONFIDENCE_THRESHOLD=0.5

# Security
SECRET_KEY=your_secret_key_here
BCRYPT_ROUNDS=12

# Email Configuration (if needed)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your_app_password

# Redis Configuration (if needed)
REDIS_URL=redis://localhost:6379

# Logging
LOG_LEVEL=INFO
LOG_FILE=logs/app.log
