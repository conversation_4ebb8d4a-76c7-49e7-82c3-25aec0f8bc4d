<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Static</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .success {
            color: green;
            font-weight: bold;
        }
        .info {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Test de Connectivité</h1>
        <p class="success">✅ Le serveur web fonctionne !</p>
        <div class="info">
            <p><strong>Si vous voyez cette page, cela signifie que :</strong></p>
            <ul>
                <li>✅ Docker fonctionne</li>
                <li>✅ Le conteneur frontend est actif</li>
                <li>✅ Vite sert les fichiers statiques</li>
                <li>✅ Le réseau fonctionne</li>
            </ul>
        </div>
        <p>Le problème est donc dans React ou dans les dépendances JavaScript.</p>
        <button onclick="testJS()">Tester JavaScript</button>
        <div id="js-result"></div>
    </div>

    <script>
        function testJS() {
            document.getElementById('js-result').innerHTML = 
                '<p style="color: green;">✅ JavaScript fonctionne aussi !</p>';
        }
        
        // Test automatique
        console.log('Test console: JavaScript fonctionne');
    </script>
</body>
</html>
