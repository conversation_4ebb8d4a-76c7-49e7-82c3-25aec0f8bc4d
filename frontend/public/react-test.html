<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test React CDN</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .success {
            color: green;
            font-weight: bold;
        }
        .error {
            color: red;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Test React via CDN</h1>
        <div id="react-root">Chargement de React...</div>
        <div id="status"></div>
    </div>

    <!-- React CDN -->
    <script crossorigin src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script crossorigin src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>

    <script type="text/babel">
        const { useState } = React;

        function App() {
            const [count, setCount] = useState(0);
            
            return React.createElement('div', null,
                React.createElement('h2', { style: { color: 'green' } }, '✅ React fonctionne via CDN !'),
                React.createElement('p', null, 'Compteur: ', count),
                React.createElement('button', { 
                    onClick: () => setCount(count + 1),
                    style: { 
                        padding: '10px 20px', 
                        backgroundColor: '#007bff', 
                        color: 'white', 
                        border: 'none', 
                        borderRadius: '4px',
                        cursor: 'pointer'
                    }
                }, 'Incrémenter'),
                React.createElement('div', { style: { marginTop: '20px', padding: '15px', backgroundColor: '#e8f5e8', borderRadius: '4px' } },
                    React.createElement('p', null, 'Si vous voyez ce message et que le bouton fonctionne, React marche parfaitement.'),
                    React.createElement('p', null, 'Le problème est donc dans notre configuration Vite/build.')
                )
            );
        }

        // Rendu de l'application
        const root = ReactDOM.createRoot(document.getElementById('react-root'));
        root.render(React.createElement(App));

        // Mise à jour du statut
        document.getElementById('status').innerHTML = 
            '<p class="success">✅ React chargé et rendu avec succès !</p>';
    </script>
</body>
</html>
