import { Outlet, createFileRoute } from "@tanstack/react-router"
import {
  Container,
  Card,
  CardContent,
  Typography,
  Box,
  Button,
  AppBar,
  Toolbar
} from "@mui/material"
import {
  Home as HomeIcon,
  Description as DescriptionIcon,
  Assessment as AssessmentIcon
} from "@mui/icons-material"

export const Route = createFileRoute("/_layout")({
  component: Layout,
  // Suppression temporaire de la vérification d'authentification
})

function Layout() {
  return (
    <Box sx={{ flexGrow: 1 }}>
      {/* AppBar Material-UI */}
      <AppBar position="static">
        <Toolbar>
          <Typography variant="h6" component="div" sx={{ flexGrow: 1 }}>
            TunLeg - Material-UI Layout
          </Typography>
        </Toolbar>
      </AppBar>

      {/* Navigation */}
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Typography variant="h5" gutterBottom>
              Navigation
            </Typography>
            <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
              <Button
                variant="outlined"
                startIcon={<HomeIcon />}
                href="/"
                size="small"
              >
                Accueil
              </Button>
              <Button
                variant="outlined"
                startIcon={<DescriptionIcon />}
                href="/ai-document-analysis"
                size="small"
              >
                AI Document Analysis
              </Button>
              <Button
                variant="outlined"
                startIcon={<AssessmentIcon />}
                href="/diagnostic"
                size="small"
              >
                Diagnostic
              </Button>
            </Box>
          </CardContent>
        </Card>

        {/* Contenu des pages */}
        <Outlet />
      </Container>
    </Box>
  )
}

export default Layout
