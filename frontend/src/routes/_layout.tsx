import { Box, Text } from "@chakra-ui/react"
import { Outlet, createFileRoute, redirect } from "@tanstack/react-router"
import { isLoggedIn } from "@/hooks/useAuth"

export const Route = createFileRoute("/_layout")({
  component: Layout,
  beforeLoad: async () => {
    if (!isLoggedIn()) {
      throw redirect({
        to: "/login",
      })
    }
  },
})

function Layout() {
  return (
    <Box p={4}>
      <Text mb={4} fontSize="lg" fontWeight="bold">TunLeg - Simplified Layout</Text>
      <Box bg="white" p={4} borderRadius="md" shadow="sm">
        <Outlet />
      </Box>
    </Box>
  )
}

export default Layout
