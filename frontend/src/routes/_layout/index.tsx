import { createFileRoute } from '@tanstack/react-router'
import {
  Card,
  CardContent,
  Typography,
  Box,
  Grid,
  Alert,
  Container
} from '@mui/material'
import { Home as HomeIcon } from '@mui/icons-material'

export const Route = createFileRoute('/_layout/')({
  component: Index,
})

function Index() {
  return (
    <Container maxWidth="xl">
      <Grid container spacing={3}>
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <HomeIcon sx={{ mr: 1, color: 'primary.main' }} />
                <Typography variant="h4" component="h1">
                  🏛️ Welcome to TunLeg
                </Typography>
              </Box>
              <Typography variant="body1" color="text.secondary" paragraph>
                Your professional legal management platform powered by Material-UI + TanStack Router
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{ bgcolor: 'primary.50' }}>
            <CardContent>
              <Typography variant="h4" color="primary.main">24</Typography>
              <Typography variant="body2" color="primary.dark">Active Cases</Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{ bgcolor: 'success.50' }}>
            <CardContent>
              <Typography variant="h4" color="success.main">156</Typography>
              <Typography variant="body2" color="success.dark">Documents</Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{ bgcolor: 'warning.50' }}>
            <CardContent>
              <Typography variant="h4" color="warning.main">8</Typography>
              <Typography variant="body2" color="warning.dark">Pending Reviews</Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{ bgcolor: 'error.50' }}>
            <CardContent>
              <Typography variant="h4" color="error.main">3</Typography>
              <Typography variant="body2" color="error.dark">Urgent Tasks</Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12}>
          <Alert severity="success">
            🎉 Material-UI + TanStack Router fonctionne parfaitement !
          </Alert>
        </Grid>
      </Grid>
    </Container>
  )
}
