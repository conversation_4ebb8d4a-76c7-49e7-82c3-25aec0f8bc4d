import { createFileRoute } from '@tanstack/react-router'
import {
  Card,
  CardContent,
  Typography,
  Box,
  Grid,
  Alert
} from '@mui/material'
import { Home as HomeIcon } from '@mui/icons-material'

export const Route = createFileRoute('/_layout/')({
  component: Index,
})

function Index() {
  return (
    <Grid container spacing={3}>
      <Grid item xs={12}>
        <Card>
          <CardContent>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
              <HomeIcon sx={{ mr: 1, color: 'primary.main' }} />
              <Typography variant="h4" component="h1">
                Accueil TunLeg
              </Typography>
            </Box>
            <Typography variant="body1" color="text.secondary" paragraph>
              Bienvenue dans l'application de gestion juridique TunLeg.
            </Typography>
            <Typography variant="body1" color="text.secondary">
              Utilisez la navigation ci-dessus pour explorer les fonctionnalités.
            </Typography>
          </CardContent>
        </Card>
      </Grid>

      <Grid item xs={12}>
        <Alert severity="success">
          🎉 Material-UI + TanStack Router fonctionne parfaitement !
        </Alert>
      </Grid>
    </Grid>
  )
}
