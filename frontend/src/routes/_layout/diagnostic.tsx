import { createFileRoute } from '@tanstack/react-router'
import { 
  Card, 
  CardContent, 
  Typography, 
  Box, 
  Grid,
  Alert
} from '@mui/material'
import { Assessment as AssessmentIcon } from '@mui/icons-material'

export const Route = createFileRoute('/_layout/diagnostic')({
  component: DiagnosticPage,
})

function DiagnosticPage() {
  return (
    <Grid container spacing={3}>
      <Grid item xs={12}>
        <Card>
          <CardContent>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
              <AssessmentIcon sx={{ mr: 1, color: 'primary.main' }} />
              <Typography variant="h4" component="h2">
                Diagnostic Système
              </Typography>
            </Box>
            <Typography variant="body1" color="text.secondary" paragraph>
              État des composants et bibliothèques de l'application.
            </Typography>
          </CardContent>
        </Card>
      </Grid>
      
      <Grid item xs={12} md={6}>
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom color="success.main">
              ✅ Composants Fonctionnels
            </Typography>
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
              <Alert severity="success">React 18 - Parfait</Alert>
              <Alert severity="success">Vite - Rapide et stable</Alert>
              <Alert severity="success">Material-UI - Interface moderne</Alert>
              <Alert severity="success">TanStack Router - Fonctionne !</Alert>
              <Alert severity="success">TypeScript - Support complet</Alert>
            </Box>
          </CardContent>
        </Card>
      </Grid>
      
      <Grid item xs={12} md={6}>
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom color="error.main">
              ❌ Composants Problématiques
            </Typography>
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
              <Alert severity="error">Chakra UI - Pages blanches</Alert>
              <Alert severity="warning">API Client - Non configuré</Alert>
              <Alert severity="info">Authentification - Temporairement désactivée</Alert>
            </Box>
          </CardContent>
        </Card>
      </Grid>
      
      <Grid item xs={12}>
        <Alert severity="success">
          🎉 Material-UI + TanStack Router + Layout complet fonctionne !
        </Alert>
      </Grid>
    </Grid>
  )
}
