import { createFileRoute } from '@tanstack/react-router'
import {
  Card,
  CardContent,
  Typography,
  Box,
  Grid,
  Alert,
  Paper,
  Button,
  Chip,
  LinearProgress
} from '@mui/material'
import {
  Description as DescriptionIcon,
  Upload as UploadIcon
} from '@mui/icons-material'

export const Route = createFileRoute('/_layout/ai-document-analysis')({
  component: AIDocumentAnalysisPage,
})

function AIDocumentAnalysisPage() {
  return (
    <Grid container spacing={3}>
      <Grid item xs={12}>
        <Card>
          <CardContent>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
              <DescriptionIcon sx={{ mr: 1, color: 'primary.main' }} />
              <Typography variant="h4" component="h2">
                AI Document Analysis
              </Typography>
            </Box>
            <Typography variant="body1" color="text.secondary" paragraph>
              Upload legal documents and let AI extract relevant information automatically.
              Review, validate, and create legal cases from the extracted data.
            </Typography>
          </CardContent>
        </Card>
      </Grid>

      <Grid item xs={12} md={6}>
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              📄 Upload Document
            </Typography>
            <Paper
              sx={{
                p: 3,
                textAlign: 'center',
                border: '2px dashed #ccc',
                backgroundColor: '#fafafa',
                cursor: 'pointer',
                '&:hover': { backgroundColor: '#f0f0f0' }
              }}
            >
              <UploadIcon sx={{ fontSize: 48, color: 'text.secondary', mb: 1 }} />
              <Typography variant="body1" color="text.secondary">
                Drag & drop files here or click to browse
              </Typography>
              <Button variant="contained" sx={{ mt: 2 }} disabled>
                Select Files
              </Button>
            </Paper>
          </CardContent>
        </Card>
      </Grid>

      <Grid item xs={12} md={6}>
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              🤖 Analysis Status
            </Typography>
            <Box sx={{ mb: 2 }}>
              <Typography variant="body2" color="text.secondary">
                Ready to analyze documents
              </Typography>
              <LinearProgress variant="determinate" value={0} sx={{ mt: 1 }} />
            </Box>
            <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
              <Chip label="PDF Support" color="primary" size="small" />
              <Chip label="Word Documents" color="primary" size="small" />
              <Chip label="AI Extraction" color="secondary" size="small" />
            </Box>
          </CardContent>
        </Card>
      </Grid>

      <Grid item xs={12}>
        <Alert severity="success">
          🎉 Page AI Document Analysis avec Material-UI + TanStack Router !
        </Alert>
      </Grid>
    </Grid>
  )
}
