import { createFileRoute } from '@tanstack/react-router'
import {
  Card,
  CardContent,
  Typography,
  Box,
  Grid,
  Alert,
  Button,
  Container
} from '@mui/material'
import {
  Psychology as PsychologyIcon
} from '@mui/icons-material'

export const Route = createFileRoute('/_layout/ai-document-analysis')({
  component: AIDocumentAnalysisPage,
})

function AIDocumentAnalysisPage() {
  return (
    <Container maxWidth="xl">
      <Grid container spacing={3}>
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <PsychologyIcon sx={{ mr: 1, color: 'primary.main' }} />
                <Typography variant="h4" component="h1">
                  🤖 AI Document Analysis
                </Typography>
              </Box>
              <Typography variant="body1" color="text.secondary" paragraph>
                Upload legal documents and let AI extract relevant information automatically.
                Review, validate, and create legal cases from the extracted data.
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={6}>
          <Card sx={{ height: '300px', display: 'flex', alignItems: 'center', justifyContent: 'center', border: '2px dashed', borderColor: 'grey.300' }}>
            <Box textAlign="center">
              <Typography variant="h1" sx={{ fontSize: '4rem', mb: 2 }}>📄</Typography>
              <Typography variant="h6" gutterBottom>Upload Document</Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                Drag & drop files here or click to browse
              </Typography>
              <Button variant="contained">Select Files</Button>
            </Box>
          </Card>
        </Grid>

        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>Analysis Status</Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                Ready to analyze documents
              </Typography>
              <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                <Button variant="outlined" size="small">PDF Support</Button>
                <Button variant="outlined" size="small">Word Documents</Button>
                <Button variant="outlined" size="small" color="secondary">AI Extraction</Button>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12}>
          <Alert severity="success">
            🎉 Material-UI + TanStack Router + AI Document Analysis fonctionne !
          </Alert>
        </Grid>
      </Grid>
    </Container>
  )
}
