import { createFileRoute } from '@tanstack/react-router'
import { Box, Container, Heading, Text, VStack } from '@chakra-ui/react'
import { DocumentAnalysisUpload } from '../../components/AIDocumentAnalysis/DocumentAnalysisUpload'

export const Route = createFileRoute('/_layout/ai-document-analysis')({
  component: AIDocumentAnalysisPage,
})

function AIDocumentAnalysisPage() {
  const handleAnalysisComplete = (analysisId: string) => {
    console.log('Analysis complete:', analysisId)
  }

  return (
    <Container maxW="8xl" py={8}>
      <VStack spacing={8} align="stretch">
        <Box textAlign="center">
          <Heading size="xl" mb={4}>
            AI Document Analysis
          </Heading>
          <Text fontSize="lg" color="gray.600" maxW="2xl" mx="auto">
            Upload legal documents and let AI extract relevant information automatically.
            Review, validate, and create legal cases from the extracted data.
          </Text>
        </Box>

        <DocumentAnalysisUpload
          onAnalysisComplete={handleAnalysisComplete}
        />
      </VStack>
    </Container>
  )
}
