import { createFileRoute } from '@tanstack/react-router'
import { Box, Container, Heading, Text, VStack, Button } from '@chakra-ui/react'

export const Route = createFileRoute('/_layout/ai-document-analysis')({
  component: AIDocumentAnalysisPage,
})

function AIDocumentAnalysisPage() {
  return (
    <Container maxW="8xl" py={8}>
      <VStack spacing={8} align="stretch">
        <Box textAlign="center">
          <Heading size="xl" mb={4}>
            AI Document Analysis
          </Heading>
          <Text fontSize="lg" color="gray.600" maxW="2xl" mx="auto">
            Upload legal documents and let AI extract relevant information automatically.
            Review, validate, and create legal cases from the extracted data.
          </Text>
        </Box>

        <Box p={8} bg="gray.50" borderRadius="md">
          <Text mb={4}>
            La fonctionnalité d'upload sera ajoutée une fois que l'API sera configurée.
          </Text>
          <Button colorScheme="blue" disabled>
            Upload Document (Coming Soon)
          </Button>
        </Box>
      </VStack>
    </Container>
  )
}
