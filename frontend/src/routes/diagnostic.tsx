import { createFileRoute } from '@tanstack/react-router'
import { Box, Text, VStack, Heading, Button, Alert, AlertIcon } from '@chakra-ui/react'

export const Route = createFileRoute('/diagnostic')({
  component: DiagnosticPage,
})

function DiagnosticPage() {
  const tests = [
    { name: 'React', status: '✅', description: 'React fonctionne' },
    { name: 'Chakra UI', status: '✅', description: 'Chakra UI fonctionne' },
    { name: 'TanStack Router', status: '✅', description: 'Router fonctionne' },
    { name: 'Layout', status: '🔍', description: 'Test en cours...' },
  ]

  return (
    <Box p={8} maxW="800px" mx="auto">
      <Heading mb={6}>Diagnostic de l'Application</Heading>
      
      <Alert status="info" mb={6}>
        <AlertIcon />
        Cette page teste tous les composants de base de l'application.
      </Alert>

      <VStack spacing={4} align="stretch">
        {tests.map((test, index) => (
          <Box key={index} p={4} bg="gray.50" borderRadius="md">
            <Text fontWeight="bold">
              {test.status} {test.name}
            </Text>
            <Text fontSize="sm" color="gray.600">
              {test.description}
            </Text>
          </Box>
        ))}
      </VStack>

      <Box mt={8} p={4} bg="blue.50" borderRadius="md">
        <Text fontWeight="bold" mb={2}>Test des composants Chakra UI :</Text>
        <VStack spacing={2} align="stretch">
          <Button colorScheme="blue" size="sm">Bouton Bleu</Button>
          <Button colorScheme="green" size="sm">Bouton Vert</Button>
          <Button variant="outline" size="sm">Bouton Outline</Button>
        </VStack>
      </Box>

      <Box mt={4} p={4} bg="green.50" borderRadius="md">
        <Text fontWeight="bold" color="green.800">
          🎉 Si vous voyez cette page, tous les composants de base fonctionnent !
        </Text>
      </Box>
    </Box>
  )
}
