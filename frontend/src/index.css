@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom base styles */
@layer base {
  html {
    font-family: 'Inter', system-ui, sans-serif;
  }
  
  body {
    @apply bg-gray-50 text-gray-900;
  }
}

/* Custom component styles */
@layer components {
  .btn-primary {
    @apply bg-primary-600 hover:bg-primary-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200;
  }
  
  .btn-secondary {
    @apply bg-gray-200 hover:bg-gray-300 text-gray-900 font-medium py-2 px-4 rounded-lg transition-colors duration-200;
  }
  
  .card {
    @apply bg-white rounded-lg shadow-sm border border-gray-200 p-6;
  }
  
  .card-header {
    @apply border-b border-gray-200 pb-4 mb-4;
  }
  
  .sidebar-item {
    @apply flex items-center px-4 py-2 text-gray-700 hover:bg-gray-100 hover:text-gray-900 rounded-lg transition-colors duration-200;
  }
  
  .sidebar-item.active {
    @apply bg-primary-50 text-primary-700 border-r-2 border-primary-600;
  }
  
  .alert {
    @apply p-4 rounded-lg border;
  }
  
  .alert-success {
    @apply bg-success-50 border-success-200 text-success-800;
  }
  
  .alert-error {
    @apply bg-error-50 border-error-200 text-error-800;
  }
  
  .alert-warning {
    @apply bg-warning-50 border-warning-200 text-warning-800;
  }
  
  .alert-info {
    @apply bg-blue-50 border-blue-200 text-blue-800;
  }
}

/* Custom utility styles */
@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
  
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  
  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }
}
