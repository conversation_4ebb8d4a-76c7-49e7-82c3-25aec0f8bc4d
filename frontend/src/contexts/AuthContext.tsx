import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { apiClient, User, LoginRequest } from '../services/api';

interface AuthContextType {
  user: User | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  login: (credentials: LoginRequest) => Promise<void>;
  logout: () => Promise<void>;
  refreshUser: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  const isAuthenticated = !!user;

  // Initialize auth state on app load
  useEffect(() => {
    initializeAuth();
  }, []);

  const initializeAuth = async () => {
    try {
      const token = localStorage.getItem('access_token');
      if (token) {
        const currentUser = await apiClient.getCurrentUser();
        setUser(currentUser);
      }
    } catch (error) {
      console.error('Failed to initialize auth:', error);
      // Clear invalid token
      localStorage.removeItem('access_token');
    } finally {
      setIsLoading(false);
    }
  };

  const login = async (credentials: LoginRequest) => {
    try {
      setIsLoading(true);

      // Mode démo : simuler l'authentification pour tester l'interface
      const demoUsers = {
        '<EMAIL>': {
          id: 1,
          email: '<EMAIL>',
          first_name: 'Admin',
          last_name: 'TunLeg',
          role: 'admin' as const,
          is_active: true,
        },
        '<EMAIL>': {
          id: 2,
          email: '<EMAIL>',
          first_name: 'Marie',
          last_name: 'Dupont',
          role: 'lawyer' as const,
          is_active: true,
        },
        '<EMAIL>': {
          id: 3,
          email: '<EMAIL>',
          first_name: 'Jean',
          last_name: 'Martin',
          role: 'client' as const,
          is_active: true,
        },
      };

      // Simuler un délai d'authentification
      await new Promise(resolve => setTimeout(resolve, 1000));

      const demoUser = demoUsers[credentials.email as keyof typeof demoUsers];

      if (demoUser && (
        credentials.password === 'admin123' ||
        credentials.password === 'lawyer123' ||
        credentials.password === 'client123'
      )) {
        // Simuler un token
        const mockToken = `demo-token-${Date.now()}`;
        localStorage.setItem('access_token', mockToken);
        setUser(demoUser);
      } else {
        // Essayer l'API réelle en fallback
        try {
          const response = await apiClient.login(credentials);
          setUser(response.user);
        } catch (apiError) {
          throw new Error('Invalid credentials. Use demo accounts: <EMAIL>, <EMAIL>, or <EMAIL> with passwords admin123, lawyer123, client123');
        }
      }
    } catch (error) {
      console.error('Login failed:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const logout = async () => {
    try {
      await apiClient.logout();
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      setUser(null);
    }
  };

  const refreshUser = async () => {
    try {
      const currentUser = await apiClient.getCurrentUser();
      setUser(currentUser);
    } catch (error) {
      console.error('Failed to refresh user:', error);
      setUser(null);
    }
  };

  const value: AuthContextType = {
    user,
    isLoading,
    isAuthenticated,
    login,
    logout,
    refreshUser,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth(): AuthContextType {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

// Higher-order component for protected routes
export function withAuth<P extends object>(Component: React.ComponentType<P>) {
  return function AuthenticatedComponent(props: P) {
    const { isAuthenticated, isLoading } = useAuth();

    if (isLoading) {
      return <div>Loading...</div>; // You can replace with a proper loading component
    }

    if (!isAuthenticated) {
      return <div>Please log in to access this page.</div>; // You can replace with a login form
    }

    return <Component {...props} />;
  };
}
