import React, { useState } from 'react';
import {
  Dialog,
  DialogT<PERSON>le,
  DialogContent,
  DialogActions,
  TextField,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Box,
  Typography,
  Alert,
  Chip,
  Divider,
  CircularProgress,
} from '@mui/material';
import {
  Gavel as GavelIcon,
  Close as CloseIcon,
} from '@mui/icons-material';
import { apiClient, AnalysisResult, LegalCase } from '../../services/api';

interface CreateCaseDialogProps {
  open: boolean;
  onClose: () => void;
  analysisResult?: AnalysisResult;
  onCaseCreated?: (legalCase: LegalCase) => void;
}

export default function CreateCaseDialog({
  open,
  onClose,
  analysisResult,
  onCaseCreated,
}: CreateCaseDialogProps) {
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [priority, setPriority] = useState<'low' | 'medium' | 'high' | 'urgent'>('medium');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');

  // Auto-populate fields when analysis result changes
  React.useEffect(() => {
    if (analysisResult) {
      setTitle(`Legal Case - ${analysisResult.document_type}`);
      setDescription(
        `Case created from document analysis.\n\n` +
        `Document Type: ${analysisResult.document_type}\n` +
        `Confidence: ${(analysisResult.confidence * 100).toFixed(1)}%\n\n` +
        `Key Entities: ${analysisResult.key_entities.join(', ')}\n\n` +
        `Suggested Actions: ${analysisResult.suggested_actions.join(', ')}\n\n` +
        `Extracted Text Preview:\n${analysisResult.extracted_text.substring(0, 500)}...`
      );
    }
  }, [analysisResult]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setIsLoading(true);

    try {
      let newCase: LegalCase;

      if (analysisResult) {
        // Create case from analysis
        newCase = await apiClient.createCaseFromAnalysis(analysisResult.id, {
          title,
          description,
          priority,
          status: 'open',
        });
      } else {
        // Create regular case
        newCase = await apiClient.createLegalCase({
          title,
          description,
          priority,
          status: 'open',
        });
      }

      onCaseCreated?.(newCase);
      handleClose();
    } catch (error) {
      console.error('Failed to create case:', error);
      setError(error instanceof Error ? error.message : 'Failed to create case');
    } finally {
      setIsLoading(false);
    }
  };

  const handleClose = () => {
    setTitle('');
    setDescription('');
    setPriority('medium');
    setError('');
    onClose();
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent': return 'error';
      case 'high': return 'warning';
      case 'medium': return 'info';
      case 'low': return 'success';
      default: return 'default';
    }
  };

  return (
    <Dialog 
      open={open} 
      onClose={handleClose} 
      maxWidth="md" 
      fullWidth
      PaperProps={{
        sx: { minHeight: '60vh' }
      }}
    >
      <DialogTitle>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <GavelIcon color="primary" />
          <Typography variant="h6">
            Create Legal Case
            {analysisResult && ' from Analysis'}
          </Typography>
        </Box>
      </DialogTitle>

      <DialogContent>
        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}

        {analysisResult && (
          <Box sx={{ mb: 3, p: 2, bgcolor: 'primary.50', borderRadius: 1 }}>
            <Typography variant="subtitle2" gutterBottom>
              📄 Source Document Analysis
            </Typography>
            <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap', mb: 1 }}>
              <Chip 
                label={analysisResult.document_type} 
                color="primary" 
                size="small" 
              />
              <Chip 
                label={`${(analysisResult.confidence * 100).toFixed(1)}% confidence`} 
                color="success" 
                size="small" 
              />
            </Box>
            <Typography variant="body2" color="text.secondary">
              Key entities: {analysisResult.key_entities.join(', ')}
            </Typography>
          </Box>
        )}

        <Box component="form" onSubmit={handleSubmit}>
          <TextField
            autoFocus
            margin="dense"
            label="Case Title"
            fullWidth
            variant="outlined"
            value={title}
            onChange={(e) => setTitle(e.target.value)}
            required
            disabled={isLoading}
            sx={{ mb: 2 }}
          />

          <FormControl fullWidth sx={{ mb: 2 }}>
            <InputLabel>Priority</InputLabel>
            <Select
              value={priority}
              label="Priority"
              onChange={(e) => setPriority(e.target.value as any)}
              disabled={isLoading}
            >
              <MenuItem value="low">
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <Chip label="Low" color="success" size="small" />
                  <Typography>Low Priority</Typography>
                </Box>
              </MenuItem>
              <MenuItem value="medium">
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <Chip label="Medium" color="info" size="small" />
                  <Typography>Medium Priority</Typography>
                </Box>
              </MenuItem>
              <MenuItem value="high">
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <Chip label="High" color="warning" size="small" />
                  <Typography>High Priority</Typography>
                </Box>
              </MenuItem>
              <MenuItem value="urgent">
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <Chip label="Urgent" color="error" size="small" />
                  <Typography>Urgent Priority</Typography>
                </Box>
              </MenuItem>
            </Select>
          </FormControl>

          <TextField
            margin="dense"
            label="Case Description"
            fullWidth
            multiline
            rows={8}
            variant="outlined"
            value={description}
            onChange={(e) => setDescription(e.target.value)}
            required
            disabled={isLoading}
            placeholder="Describe the legal case, including relevant details, parties involved, and initial assessment..."
          />
        </Box>
      </DialogContent>

      <DialogActions sx={{ p: 3 }}>
        <Button 
          onClick={handleClose} 
          disabled={isLoading}
          startIcon={<CloseIcon />}
        >
          Cancel
        </Button>
        <Button 
          onClick={handleSubmit}
          variant="contained"
          disabled={isLoading || !title.trim() || !description.trim()}
          startIcon={isLoading ? <CircularProgress size={16} /> : <GavelIcon />}
        >
          {isLoading ? 'Creating...' : 'Create Case'}
        </Button>
      </DialogActions>
    </Dialog>
  );
}
