import React, { useState } from 'react';
import {
  <PERSON>,
  Drawer,
  App<PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  Typo<PERSON>,
  Divider,
  IconButton,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Avatar,
  Chip,
  useTheme,
  useMediaQuery,
  Badge,
  Collapse,
} from '@mui/material';
import {
  Menu as MenuIcon,
  Home as HomeIcon,
  Folder as FolderIcon,
  Upload as UploadIcon,
  People as PeopleIcon,
  Settings as SettingsIcon,
  Logout as LogoutIcon,
  Gavel as GavelIcon,
  Description as DescriptionIcon,
  BarChart as BarChartIcon,
  Calendar as CalendarIcon,
  Search as SearchIcon,
  Notifications as NotificationsIcon,
  Mail as MailIcon,
  ExpandLess,
  ExpandMore,
  Cpu as CpuIcon,
} from '@mui/icons-material';
import { useQueryClient } from '@tanstack/react-query';
import type { UserPublic } from '@/client';
import useAuth from '@/hooks/useAuth';

const drawerWidth = 280;

interface MenuItem {
  icon: React.ElementType;
  title: string;
  path: string;
  badge?: string;
  roles?: string[];
  category?: string;
  children?: MenuItem[];
}

const menuItems: MenuItem[] = [
  // Main Navigation
  { icon: HomeIcon, title: "Dashboard", path: "/", category: "main" },

  // Case Management
  { 
    icon: FolderIcon, 
    title: "Legal Cases", 
    path: "/legal-cases", 
    roles: ['admin', 'lawyer', 'assistant'], 
    category: "cases",
    children: [
      { icon: GavelIcon, title: "All Cases", path: "/legal-cases" },
      { icon: CalendarIcon, title: "Calendar", path: "/calendar" },
      { icon: BarChartIcon, title: "Reports", path: "/reports", roles: ['admin', 'lawyer'] },
    ]
  },

  // Document Management
  { 
    icon: DescriptionIcon, 
    title: "Documents", 
    path: "/documents", 
    category: "documents",
    children: [
      { icon: UploadIcon, title: "Upload", path: "/documents" },
      { icon: CpuIcon, title: "AI Analysis", path: "/ai-document-analysis", roles: ['admin', 'lawyer'] },
      { icon: SearchIcon, title: "Search", path: "/search" },
    ]
  },

  // Administration
  { 
    icon: PeopleIcon, 
    title: "Administration", 
    path: "/admin", 
    roles: ['admin'], 
    category: "admin",
    children: [
      { icon: PeopleIcon, title: "Users", path: "/admin" },
      { icon: SettingsIcon, title: "Settings", path: "/settings" },
    ]
  },
];

interface MaterialLayoutProps {
  children: React.ReactNode;
}

export default function MaterialLayout({ children }: MaterialLayoutProps) {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const [mobileOpen, setMobileOpen] = useState(false);
  const [expandedItems, setExpandedItems] = useState<string[]>([]);
  
  const queryClient = useQueryClient();
  const currentUser = queryClient.getQueryData<UserPublic>(["currentUser"]);
  const { logout } = useAuth();

  const handleDrawerToggle = () => {
    setMobileOpen(!mobileOpen);
  };

  const handleExpandClick = (title: string) => {
    setExpandedItems(prev => 
      prev.includes(title) 
        ? prev.filter(item => item !== title)
        : [...prev, title]
    );
  };

  const getRoleColor = (role?: string) => {
    switch (role) {
      case 'admin': return 'error';
      case 'lawyer': return 'primary';
      case 'assistant': return 'success';
      case 'client': return 'secondary';
      default: return 'default';
    }
  };

  const hasPermission = (item: MenuItem) => {
    if (!item.roles) return true;
    return item.roles.includes(currentUser?.role || '');
  };

  const renderMenuItem = (item: MenuItem, depth = 0) => {
    if (!hasPermission(item)) return null;

    const isExpanded = expandedItems.includes(item.title);
    const hasChildren = item.children && item.children.length > 0;

    return (
      <React.Fragment key={item.title}>
        <ListItem disablePadding sx={{ pl: depth * 2 }}>
          <ListItemButton
            onClick={() => {
              if (hasChildren) {
                handleExpandClick(item.title);
              } else {
                // Navigation logic here
                if (isMobile) setMobileOpen(false);
              }
            }}
          >
            <ListItemIcon>
              <item.icon />
            </ListItemIcon>
            <ListItemText primary={item.title} />
            {item.badge && (
              <Badge badgeContent={item.badge} color="error" />
            )}
            {hasChildren && (isExpanded ? <ExpandLess /> : <ExpandMore />)}
          </ListItemButton>
        </ListItem>
        
        {hasChildren && (
          <Collapse in={isExpanded} timeout="auto" unmountOnExit>
            <List component="div" disablePadding>
              {item.children!.map(child => renderMenuItem(child, depth + 1))}
            </List>
          </Collapse>
        )}
      </React.Fragment>
    );
  };

  const drawer = (
    <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* Logo Section */}
      <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider' }}>
        <Typography variant="h6" sx={{ fontWeight: 'bold', color: 'primary.main' }}>
          TunLeg
        </Typography>
        <Typography variant="caption" color="text.secondary">
          Legal Management System
        </Typography>
      </Box>

      {/* User Profile Section */}
      {currentUser && (
        <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider' }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <Avatar
              sx={{ 
                bgcolor: `${getRoleColor(currentUser.role)}.main`,
                width: 40,
                height: 40
              }}
            >
              {(currentUser.full_name || currentUser.email).charAt(0).toUpperCase()}
            </Avatar>
            <Box sx={{ flex: 1, minWidth: 0 }}>
              <Typography variant="subtitle2" noWrap>
                {currentUser.full_name || currentUser.email}
              </Typography>
              <Chip 
                label={currentUser.role || 'User'} 
                size="small" 
                color={getRoleColor(currentUser.role)}
                sx={{ textTransform: 'capitalize' }}
              />
            </Box>
          </Box>
        </Box>
      )}

      {/* Navigation Menu */}
      <Box sx={{ flex: 1, overflow: 'auto' }}>
        <List>
          {menuItems.map(item => renderMenuItem(item))}
        </List>
      </Box>

      {/* Logout Section */}
      <Box sx={{ borderTop: 1, borderColor: 'divider', p: 1 }}>
        <ListItem disablePadding>
          <ListItemButton onClick={logout}>
            <ListItemIcon>
              <LogoutIcon />
            </ListItemIcon>
            <ListItemText primary="Logout" />
          </ListItemButton>
        </ListItem>
      </Box>
    </Box>
  );

  return (
    <Box sx={{ display: 'flex' }}>
      {/* AppBar */}
      <AppBar
        position="fixed"
        sx={{
          width: { md: `calc(100% - ${drawerWidth}px)` },
          ml: { md: `${drawerWidth}px` },
        }}
      >
        <Toolbar>
          <IconButton
            color="inherit"
            aria-label="open drawer"
            edge="start"
            onClick={handleDrawerToggle}
            sx={{ mr: 2, display: { md: 'none' } }}
          >
            <MenuIcon />
          </IconButton>
          <Typography variant="h6" noWrap component="div" sx={{ flexGrow: 1 }}>
            TunLeg - Legal Management
          </Typography>
          <IconButton color="inherit">
            <Badge badgeContent={4} color="error">
              <NotificationsIcon />
            </Badge>
          </IconButton>
          <IconButton color="inherit">
            <MailIcon />
          </IconButton>
        </Toolbar>
      </AppBar>

      {/* Drawer */}
      <Box
        component="nav"
        sx={{ width: { md: drawerWidth }, flexShrink: { md: 0 } }}
      >
        {/* Mobile drawer */}
        <Drawer
          variant="temporary"
          open={mobileOpen}
          onClose={handleDrawerToggle}
          ModalProps={{ keepMounted: true }}
          sx={{
            display: { xs: 'block', md: 'none' },
            '& .MuiDrawer-paper': { boxSizing: 'border-box', width: drawerWidth },
          }}
        >
          {drawer}
        </Drawer>
        
        {/* Desktop drawer */}
        <Drawer
          variant="permanent"
          sx={{
            display: { xs: 'none', md: 'block' },
            '& .MuiDrawer-paper': { boxSizing: 'border-box', width: drawerWidth },
          }}
          open
        >
          {drawer}
        </Drawer>
      </Box>

      {/* Main content */}
      <Box
        component="main"
        sx={{
          flexGrow: 1,
          p: 3,
          width: { md: `calc(100% - ${drawerWidth}px)` },
          mt: '64px', // AppBar height
        }}
      >
        {children}
      </Box>
    </Box>
  );
}
