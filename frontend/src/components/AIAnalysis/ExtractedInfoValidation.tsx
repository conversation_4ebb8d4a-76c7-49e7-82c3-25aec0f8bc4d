import React, { useState } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  TextField,
  Button,
  Grid,
  Chip,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Switch,
  FormControlLabel,
  Divider,
} from '@mui/material';
import {
  ExpandMore as ExpandMoreIcon,
  Edit as EditIcon,
  Save as SaveIcon,
  CheckCircle as ApproveIcon,
  Cancel as RejectIcon,
} from '@mui/icons-material';
import { AnalysisResult } from '../../services/api';

interface ExtractedInfoValidationProps {
  analysisResults: AnalysisResult[];
  onValidationComplete: (validatedResults: any[]) => void;
}

interface ValidationData {
  id: string;
  status: 'pending' | 'approved' | 'modified' | 'rejected';
  plaintiff: string;
  defendant: string;
  witnesses: string[];
  lawyers: string[];
  caseType: string;
  caseTitle: string;
  clientName: string;
  documentSummary: string;
  keyFacts: string[];
  importantDates: Record<string, string>;
  amounts: Record<string, number>;
  legalReferences: string[];
  createCase: boolean;
  validationNotes: string;
  confidence: number;
}

export default function ExtractedInfoValidation({ 
  analysisResults, 
  onValidationComplete 
}: ExtractedInfoValidationProps) {
  console.log('🔍 ExtractedInfoValidation received:', analysisResults);
  console.log('📊 Number of analysis results:', analysisResults.length);

  const [validations, setValidations] = useState<ValidationData[]>(() => {
    console.log('🏗️ Creating validations from results...');
    return analysisResults.map((result, index) => {
      console.log(`📋 Processing result ${index + 1}:`, result);
      console.log(`🔍 Key entities:`, result.key_entities);
      console.log(`🎯 Suggested actions:`, result.suggested_actions);
      console.log(`📊 Confidence:`, result.confidence);
      console.log(`📄 Extracted text:`, result.extracted_text);

      const validation = {
      id: result.id || `validation-${index}`,
      status: 'pending' as const,
      plaintiff: result.key_entities?.find(e => e.toLowerCase().includes('plaintiff')) || '',
      defendant: result.key_entities?.find(e => e.toLowerCase().includes('defendant')) || '',
      witnesses: result.key_entities?.filter(e => e.toLowerCase().includes('witness')) || [],
      lawyers: result.key_entities?.filter(e => e.toLowerCase().includes('lawyer')) || [],
      caseType: result.document_type,
      caseTitle: `Legal Case - ${result.document_type}`,
      clientName: result.key_entities?.[0] || 'Unknown Client',
      documentSummary: result.extracted_text ?
        (result.extracted_text.length > 500 ? result.extracted_text.substring(0, 500) + '...' : result.extracted_text) :
        'No summary available',
      keyFacts: result.suggested_actions || [],
      importantDates: { 'Document Date': new Date().toISOString().split('T')[0] },
      amounts: {},
      legalReferences: [],
      createCase: true,
      validationNotes: '',
      confidence: result.confidence || 0,
      originalAnalysis: result,
    };

    console.log(`✅ Created validation ${index + 1}:`, validation);
    return validation;
  });
  });

  const [editingIndex, setEditingIndex] = useState<number | null>(null);

  const handleValidationChange = (index: number, field: string, value: any) => {
    setValidations(prev => prev.map((validation, i) => 
      i === index 
        ? { ...validation, [field]: value, status: 'modified' as const }
        : validation
    ));
  };

  const handleApprove = (index: number) => {
    setValidations(prev => prev.map((validation, i) => 
      i === index 
        ? { ...validation, status: 'approved' as const }
        : validation
    ));
  };

  const handleReject = (index: number) => {
    setValidations(prev => prev.map((validation, i) => 
      i === index 
        ? { ...validation, status: 'rejected' as const, createCase: false }
        : validation
    ));
  };

  const handleSaveAll = () => {
    const approvedValidations = validations.filter(v =>
      (v.status === 'approved' || v.status === 'modified') && v.createCase
    );
    console.log('💾 Saving validations:', approvedValidations);
    onValidationComplete(approvedValidations);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved': return 'success';
      case 'modified': return 'warning';
      case 'rejected': return 'error';
      default: return 'default';
    }
  };

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 0.8) return 'success';
    if (confidence >= 0.6) return 'warning';
    return 'error';
  };

  console.log('🎨 Rendering validation component with', validations.length, 'validations');

  return (
    <Box>
      <Typography variant="h6" gutterBottom>
        📋 Validate Extracted Information ({validations.length} document{validations.length > 1 ? 's' : ''})
      </Typography>
      
      <Alert severity="info" sx={{ mb: 3 }}>
        Review the information extracted by AI. Make corrections as needed and approve for case creation.
      </Alert>



      {validations.map((validation, index) => (
        <Accordion key={validation.id} sx={{ mb: 2 }}>
          <AccordionSummary expandIcon={<ExpandMoreIcon />}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, width: '100%' }}>
              <Typography variant="h6" sx={{ flexGrow: 1 }}>
                Document {index + 1}: {validation.caseType}
              </Typography>
              <Chip 
                label={validation.status.toUpperCase()}
                color={getStatusColor(validation.status)}
                size="small"
              />
              <Chip 
                label={`${(validation.confidence * 100).toFixed(0)}% confidence`}
                color={getConfidenceColor(validation.confidence)}
                size="small"
              />
            </Box>
          </AccordionSummary>
          
          <AccordionDetails>
            <Grid container spacing={3}>
              {/* Case Information */}
              <Grid item xs={12} md={6}>
                <Card variant="outlined">
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      📋 Case Information
                    </Typography>
                    
                    <TextField
                      fullWidth
                      label="Case Title"
                      value={validation.caseTitle}
                      onChange={(e) => handleValidationChange(index, 'caseTitle', e.target.value)}
                      margin="normal"
                      disabled={editingIndex !== null && editingIndex !== index}
                    />
                    
                    <TextField
                      fullWidth
                      label="Client Name"
                      value={validation.clientName}
                      onChange={(e) => handleValidationChange(index, 'clientName', e.target.value)}
                      margin="normal"
                      disabled={editingIndex !== null && editingIndex !== index}
                    />
                    
                    <FormControl fullWidth margin="normal">
                      <InputLabel>Case Type</InputLabel>
                      <Select
                        value={validation.caseType}
                        label="Case Type"
                        onChange={(e) => handleValidationChange(index, 'caseType', e.target.value)}
                        disabled={editingIndex !== null && editingIndex !== index}
                      >
                        <MenuItem value="civil">Civil</MenuItem>
                        <MenuItem value="criminal">Criminal</MenuItem>
                        <MenuItem value="family">Family</MenuItem>
                        <MenuItem value="corporate">Corporate</MenuItem>
                        <MenuItem value="immigration">Immigration</MenuItem>
                        <MenuItem value="real_estate">Real Estate</MenuItem>
                        <MenuItem value="intellectual_property">Intellectual Property</MenuItem>
                        <MenuItem value="employment">Employment</MenuItem>
                        <MenuItem value="other">Other</MenuItem>
                      </Select>
                    </FormControl>
                  </CardContent>
                </Card>
              </Grid>

              {/* Parties Information */}
              <Grid item xs={12} md={6}>
                <Card variant="outlined">
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      👥 Parties Involved
                    </Typography>
                    
                    <TextField
                      fullWidth
                      label="Plaintiff"
                      value={validation.plaintiff}
                      onChange={(e) => handleValidationChange(index, 'plaintiff', e.target.value)}
                      margin="normal"
                      disabled={editingIndex !== null && editingIndex !== index}
                    />
                    
                    <TextField
                      fullWidth
                      label="Defendant"
                      value={validation.defendant}
                      onChange={(e) => handleValidationChange(index, 'defendant', e.target.value)}
                      margin="normal"
                      disabled={editingIndex !== null && editingIndex !== index}
                    />
                    
                    <TextField
                      fullWidth
                      label="Witnesses (comma-separated)"
                      value={validation.witnesses.join(', ')}
                      onChange={(e) => handleValidationChange(index, 'witnesses', e.target.value.split(', ').filter(w => w.trim()))}
                      margin="normal"
                      disabled={editingIndex !== null && editingIndex !== index}
                    />
                    
                    <TextField
                      fullWidth
                      label="Lawyers (comma-separated)"
                      value={validation.lawyers.join(', ')}
                      onChange={(e) => handleValidationChange(index, 'lawyers', e.target.value.split(', ').filter(l => l.trim()))}
                      margin="normal"
                      disabled={editingIndex !== null && editingIndex !== index}
                    />
                  </CardContent>
                </Card>
              </Grid>

              {/* Extracted Information Display */}
              <Grid item xs={12}>
                <Card variant="outlined" sx={{ bgcolor: 'grey.50' }}>
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      🔍 AI Extracted Information
                    </Typography>

                    <Grid container spacing={2}>
                      <Grid item xs={12} md={4}>
                        <Typography variant="body2" paragraph>
                          <strong>Key Entities:</strong><br />
                          <Box component="span" sx={{ color: 'text.secondary' }}>
                            {analysisResults[index]?.key_entities?.length > 0
                              ? analysisResults[index].key_entities.join(', ')
                              : 'None detected'
                            }
                          </Box>
                        </Typography>
                      </Grid>
                      <Grid item xs={12} md={4}>
                        <Typography variant="body2" paragraph>
                          <strong>Suggested Actions:</strong><br />
                          <Box component="span" sx={{ color: 'text.secondary' }}>
                            {analysisResults[index]?.suggested_actions?.length > 0
                              ? analysisResults[index].suggested_actions.join(', ')
                              : 'None suggested'
                            }
                          </Box>
                        </Typography>
                      </Grid>
                      <Grid item xs={12} md={4}>
                        <Typography variant="body2" paragraph>
                          <strong>Confidence Score:</strong><br />
                          <Box component="span" sx={{
                            color: validation.confidence >= 0.8 ? 'success.main' :
                                   validation.confidence >= 0.6 ? 'warning.main' : 'error.main',
                            fontWeight: 'bold'
                          }}>
                            {(validation.confidence * 100).toFixed(1)}%
                          </Box>
                        </Typography>
                      </Grid>
                    </Grid>
                  </CardContent>
                </Card>
              </Grid>

              {/* Document Summary */}
              <Grid item xs={12}>
                <Card variant="outlined">
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      📄 Document Summary
                    </Typography>
                    
                    <TextField
                      fullWidth
                      multiline
                      rows={4}
                      label="Document Summary"
                      value={validation.documentSummary}
                      onChange={(e) => handleValidationChange(index, 'documentSummary', e.target.value)}
                      margin="normal"
                      disabled={editingIndex !== null && editingIndex !== index}
                    />
                    
                    <TextField
                      fullWidth
                      label="Key Facts (comma-separated)"
                      value={validation.keyFacts.join(', ')}
                      onChange={(e) => handleValidationChange(index, 'keyFacts', e.target.value.split(', ').filter(f => f.trim()))}
                      margin="normal"
                      disabled={editingIndex !== null && editingIndex !== index}
                    />
                  </CardContent>
                </Card>
              </Grid>

              {/* Validation Controls */}
              <Grid item xs={12}>
                <Card variant="outlined">
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      ✅ Validation Controls
                    </Typography>
                    
                    <FormControlLabel
                      control={
                        <Switch
                          checked={validation.createCase}
                          onChange={(e) => handleValidationChange(index, 'createCase', e.target.checked)}
                        />
                      }
                      label="Create legal case from this validation"
                    />
                    
                    <TextField
                      fullWidth
                      multiline
                      rows={3}
                      label="Validation Notes"
                      value={validation.validationNotes}
                      onChange={(e) => handleValidationChange(index, 'validationNotes', e.target.value)}
                      margin="normal"
                      placeholder="Add any notes about the validation or corrections made..."
                    />
                    
                    <Divider sx={{ my: 2 }} />
                    
                    <Box sx={{ display: 'flex', gap: 1 }}>
                      <Button
                        variant="contained"
                        color="success"
                        startIcon={<ApproveIcon />}
                        onClick={() => handleApprove(index)}
                        disabled={validation.status === 'approved'}
                      >
                        Approve
                      </Button>
                      <Button
                        variant="outlined"
                        color="error"
                        startIcon={<RejectIcon />}
                        onClick={() => handleReject(index)}
                        disabled={validation.status === 'rejected'}
                      >
                        Reject
                      </Button>
                      <Button
                        variant="outlined"
                        startIcon={<EditIcon />}
                        onClick={() => setEditingIndex(editingIndex === index ? null : index)}
                      >
                        {editingIndex === index ? 'Stop Editing' : 'Edit'}
                      </Button>
                    </Box>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          </AccordionDetails>
        </Accordion>
      ))}

      <Box sx={{ mt: 3, textAlign: 'center' }}>
        <Button
          variant="contained"
          size="large"
          startIcon={<SaveIcon />}
          onClick={handleSaveAll}
          disabled={validations.filter(v => v.status === 'approved' || v.status === 'modified').length === 0}
        >
          Save All Validations & Continue
        </Button>
        <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
          {validations.filter(v => (v.status === 'approved' || v.status === 'modified') && v.createCase).length} validation{validations.filter(v => (v.status === 'approved' || v.status === 'modified') && v.createCase).length !== 1 ? 's' : ''} ready for case creation
        </Typography>
      </Box>
    </Box>
  );
}
