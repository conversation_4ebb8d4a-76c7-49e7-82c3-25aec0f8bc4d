import React, { useState } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>nt,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Card,
  Card<PERSON>ontent,
  <PERSON><PERSON>,
  Chip,
  Container,
} from '@mui/material';
import {
  CloudUpload as UploadIcon,
  Psychology as AnalysisIcon,
  CheckCircle as ValidateIcon,
  Gavel as CaseIcon,
} from '@mui/icons-material';
import DocumentUpload from '../Upload/DocumentUpload';
import { AnalysisResult } from '../../services/api';

// Composants simplifiés pour le moment
const ExtractedInfoValidation = ({ analysisResults, onValidationComplete }: any) => (
  <Box>
    <Alert severity="success" sx={{ mb: 2 }}>
      ✅ {analysisResults.length} document{analysisResults.length > 1 ? 's' : ''} ready for validation
    </Alert>
    <Button
      variant="contained"
      onClick={() => onValidationComplete(analysisResults.map((r: any, i: number) => ({ ...r, validated: true, id: `val-${i}` })))}
    >
      Approve All & Continue
    </Button>
  </Box>
);

const CaseCreationSummary = ({ validatedResults, onCaseCreationComplete }: any) => (
  <Box>
    <Alert severity="info" sx={{ mb: 2 }}>
      🎯 Ready to create {validatedResults.length} legal case{validatedResults.length > 1 ? 's' : ''}
    </Alert>
    <Button
      variant="contained"
      onClick={() => {
        const cases = validatedResults.map((r: any, i: number) => ({
          id: `case-${i}`,
          title: `Legal Case ${i + 1}`,
          created_at: new Date().toISOString()
        }));
        onCaseCreationComplete(cases);
      }}
    >
      Create All Cases
    </Button>
  </Box>
);

interface AIDocumentAnalysisWorkflowProps {
  onComplete?: (createdCase: any) => void;
}

export default function AIDocumentAnalysisWorkflow({ onComplete }: AIDocumentAnalysisWorkflowProps) {
  const [activeStep, setActiveStep] = useState(0);
  const [analysisResults, setAnalysisResults] = useState<AnalysisResult[]>([]);
  const [validatedResults, setValidatedResults] = useState<any[]>([]);
  const [createdCases, setCreatedCases] = useState<any[]>([]);

  const steps = [
    {
      label: 'Document Upload & Analysis',
      description: 'Upload legal documents and perform AI analysis',
      icon: <UploadIcon />,
    },
    {
      label: 'Information Validation',
      description: 'Review and validate extracted information',
      icon: <ValidateIcon />,
    },
    {
      label: 'Case Creation',
      description: 'Create legal cases from validated information',
      icon: <CaseIcon />,
    },
  ];

  const handleNext = () => {
    setActiveStep((prevActiveStep) => prevActiveStep + 1);
  };

  const handleBack = () => {
    setActiveStep((prevActiveStep) => prevActiveStep - 1);
  };

  const handleReset = () => {
    setActiveStep(0);
    setAnalysisResults([]);
    setValidatedResults([]);
    setCreatedCases([]);
  };

  const handleAnalysisComplete = (results: AnalysisResult[]) => {
    setAnalysisResults(results);
    if (results.length > 0) {
      // Auto-advance to validation step
      setTimeout(() => handleNext(), 1000);
    }
  };

  const handleValidationComplete = (validated: any[]) => {
    setValidatedResults(validated);
    handleNext();
  };

  const handleCaseCreationComplete = (cases: any[]) => {
    setCreatedCases(cases);
    onComplete?.(cases);
  };

  const getStepContent = (step: number) => {
    switch (step) {
      case 0:
        return (
          <Box>
            <Typography variant="h6" gutterBottom>
              📄 Upload Legal Documents
            </Typography>
            <Typography variant="body2" color="text.secondary" paragraph>
              Upload PDF files, scanned images, or other legal documents for AI analysis.
              The system will extract text and identify key legal information.
            </Typography>
            
            <DocumentUpload
              onAnalysisComplete={handleAnalysisComplete}
              maxFileSize={50}
              acceptedTypes={['.pdf', '.doc', '.docx', '.jpg', '.jpeg', '.png', '.tiff']}
            />

            {analysisResults.length > 0 && (
              <Alert severity="success" sx={{ mt: 2 }}>
                ✅ {analysisResults.length} document{analysisResults.length > 1 ? 's' : ''} analyzed successfully!
                Proceeding to validation step...
              </Alert>
            )}
          </Box>
        );

      case 1:
        return (
          <Box>
            <Typography variant="h6" gutterBottom>
              ✅ Validate Extracted Information
            </Typography>
            <Typography variant="body2" color="text.secondary" paragraph>
              Review the information extracted by AI and make any necessary corrections.
              Ensure accuracy before proceeding to case creation.
            </Typography>

            {analysisResults.length > 0 ? (
              <ExtractedInfoValidation
                analysisResults={analysisResults}
                onValidationComplete={handleValidationComplete}
              />
            ) : (
              <Alert severity="warning">
                No analysis results available. Please complete the upload step first.
              </Alert>
            )}
          </Box>
        );

      case 2:
        return (
          <Box>
            <Typography variant="h6" gutterBottom>
              ⚖️ Create Legal Cases
            </Typography>
            <Typography variant="body2" color="text.secondary" paragraph>
              Create legal cases from the validated information. Review case details
              and confirm creation.
            </Typography>

            {validatedResults.length > 0 ? (
              <CaseCreationSummary
                validatedResults={validatedResults}
                onCaseCreationComplete={handleCaseCreationComplete}
              />
            ) : (
              <Alert severity="warning">
                No validated results available. Please complete the validation step first.
              </Alert>
            )}
          </Box>
        );

      default:
        return 'Unknown step';
    }
  };

  return (
    <Card>
      <CardContent>
        <Typography variant="h4" component="h1" gutterBottom>
          🤖 AI Document Analysis Workflow
        </Typography>
        <Typography variant="body1" color="text.secondary" paragraph>
          Complete workflow for analyzing legal documents and creating cases automatically.
        </Typography>

        <Stepper activeStep={activeStep} orientation="vertical">
          {steps.map((step, index) => (
            <Step key={step.label}>
              <StepLabel
                optional={
                  index === 2 ? (
                    <Typography variant="caption">Last step</Typography>
                  ) : null
                }
                icon={step.icon}
              >
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  {step.label}
                  {index < activeStep && (
                    <Chip label="Completed" color="success" size="small" />
                  )}
                  {index === activeStep && (
                    <Chip label="In Progress" color="primary" size="small" />
                  )}
                </Box>
              </StepLabel>
              <StepContent>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                  {step.description}
                </Typography>
                
                {getStepContent(index)}

                <Box sx={{ mb: 2, mt: 3 }}>
                  <div>
                    <Button
                      variant="contained"
                      onClick={handleNext}
                      sx={{ mt: 1, mr: 1 }}
                      disabled={
                        (index === 0 && analysisResults.length === 0) ||
                        (index === 1 && validatedResults.length === 0)
                      }
                    >
                      {index === steps.length - 1 ? 'Finish' : 'Continue'}
                    </Button>
                    <Button
                      disabled={index === 0}
                      onClick={handleBack}
                      sx={{ mt: 1, mr: 1 }}
                    >
                      Back
                    </Button>
                  </div>
                </Box>
              </StepContent>
            </Step>
          ))}
        </Stepper>

        {activeStep === steps.length && (
          <Card variant="outlined" sx={{ mt: 3, p: 3, textAlign: 'center' }}>
            <Typography variant="h5" gutterBottom color="success.main">
              🎉 Workflow Completed Successfully!
            </Typography>
            <Typography variant="body1" paragraph>
              {createdCases.length} legal case{createdCases.length > 1 ? 's' : ''} created from your documents.
            </Typography>
            <Box sx={{ mt: 2 }}>
              <Button onClick={handleReset} sx={{ mr: 1 }}>
                Start New Analysis
              </Button>
              <Button variant="contained" href="/legal-cases">
                View Created Cases
              </Button>
            </Box>
          </Card>
        )}
      </CardContent>
    </Card>
  );
}
