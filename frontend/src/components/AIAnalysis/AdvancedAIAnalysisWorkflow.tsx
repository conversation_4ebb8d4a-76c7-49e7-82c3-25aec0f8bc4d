import React, { useState } from 'react';
import {
  <PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Step<PERSON>ontent,
  Button,
  Alert,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  LinearProgress,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Paper,
  Divider
} from '@mui/material';
import {
  Upload as UploadIcon,
  Psychology as PsychologyIcon,
  Visibility as VisibilityIcon,
  CheckCircle as CheckCircleIcon,
  ExpandMore as ExpandMoreIcon,
  Save as SaveIcon
} from '@mui/icons-material';

interface AnalysisResult {
  success: boolean;
  filename: string;
  file_size: number;
  file_type: string;
  output_language: string;
  ocr_extraction: {
    extracted_text_length: number;
    extracted_text_preview: string;
    ocr_confidence: number;
    is_fallback: boolean;
  };
  ai_analysis: {
    document_type: string;
    confidence: number;
    key_entities: string[];
    suggested_actions: string[];
    extracted_info: any;
    language_detected?: string;
    tunisian_specifics?: {
      cin_numbers: string[];
      property_titles: string[];
      court_references: string[];
      notary_references: string[];
    };
  };
  openai_enabled: boolean;
  error?: string;
}

interface AdvancedAIAnalysisWorkflowProps {
  onComplete?: (result: AnalysisResult) => void;
}

export default function AdvancedAIAnalysisWorkflow({ onComplete }: AdvancedAIAnalysisWorkflowProps) {
  const [activeStep, setActiveStep] = useState(0);
  const [file, setFile] = useState<File | null>(null);
  const [outputLanguage, setOutputLanguage] = useState<string>('auto');
  const [loading, setLoading] = useState(false);
  const [analysisResult, setAnalysisResult] = useState<AnalysisResult | null>(null);
  const [validationData, setValidationData] = useState<any>(null);

  const steps = [
    {
      label: 'Upload Document',
      description: 'Choisissez votre document et la langue d\'analyse'
    },
    {
      label: 'Extraction OCR',
      description: 'Extraction automatique du texte (Arabe, Français, Anglais)'
    },
    {
      label: 'Analyse IA',
      description: 'Analyse intelligente avec OpenAI GPT-4'
    },
    {
      label: 'Validation',
      description: 'Vérification et correction des informations extraites'
    },
    {
      label: 'Création du Dossier',
      description: 'Création automatique du dossier juridique'
    }
  ];

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = event.target.files?.[0];
    if (selectedFile) {
      setFile(selectedFile);
      setAnalysisResult(null);
    }
  };

  const handleAnalysis = async () => {
    if (!file) return;

    setLoading(true);
    try {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('output_language', outputLanguage);

      const response = await fetch('http://localhost:8000/api/v1/ai-analysis/complete-analysis', {
        method: 'POST',
        body: formData,
      });

      const data = await response.json();
      setAnalysisResult(data);
      
      if (data.success) {
        setActiveStep(3); // Aller à l'étape de validation
        // Préparer les données de validation
        setValidationData({
          case_title: data.ai_analysis.extracted_info?.case_title || `Analyse - ${data.filename}`,
          client_name: data.ai_analysis.extracted_info?.buyer || data.ai_analysis.extracted_info?.plaintiff || 'Client',
          case_type: data.ai_analysis.document_type || 'other',
          parties: data.ai_analysis.key_entities || [],
          key_facts: data.ai_analysis.suggested_actions || [],
          summary: data.ai_analysis.extracted_info?.document_summary || 'Résumé automatique'
        });
      }
    } catch (error) {
      console.error('Analysis failed:', error);
      setAnalysisResult({
        success: false,
        filename: file.name,
        file_size: file.size,
        file_type: '',
        output_language: outputLanguage,
        ocr_extraction: {
          extracted_text_length: 0,
          extracted_text_preview: '',
          ocr_confidence: 0,
          is_fallback: true
        },
        ai_analysis: {
          document_type: '',
          confidence: 0,
          key_entities: [],
          suggested_actions: [],
          extracted_info: {}
        },
        openai_enabled: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    } finally {
      setLoading(false);
    }
  };

  const handleNext = () => {
    setActiveStep((prevActiveStep) => prevActiveStep + 1);
  };

  const handleBack = () => {
    setActiveStep((prevActiveStep) => prevActiveStep - 1);
  };

  const handleCreateCase = async () => {
    if (!analysisResult) return;

    setLoading(true);
    try {
      const response = await fetch('http://localhost:8000/api/v1/ai-analysis/create-case-from-analysis', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}` // Assuming token auth
        },
        body: JSON.stringify(analysisResult),
      });

      const result = await response.json();

      if (result.success) {
        if (onComplete) {
          onComplete(analysisResult);
        }
        setActiveStep(4); // Aller à l'étape finale
      } else {
        console.error('Failed to create case:', result.error);
        // Pour l'instant, on continue quand même vers l'étape finale
        setActiveStep(4);
      }
    } catch (error) {
      console.error('Error creating case:', error);
      // Pour l'instant, on continue quand même vers l'étape finale
      setActiveStep(4);
    } finally {
      setLoading(false);
    }
  };

  const renderStepContent = (step: number) => {
    switch (step) {
      case 0:
        return (
          <Box>
            <Typography variant="h6" gutterBottom>
              📤 Upload de Document
            </Typography>
            
            <Box sx={{ mb: 3 }}>
              <input
                type="file"
                accept=".pdf,.jpg,.jpeg,.png,.txt,.doc,.docx"
                onChange={handleFileSelect}
                style={{ display: 'none' }}
                id="file-input"
              />
              <label htmlFor="file-input">
                <Button
                  variant="outlined"
                  component="span"
                  startIcon={<UploadIcon />}
                  size="large"
                  sx={{ mr: 2 }}
                >
                  Choisir un document
                </Button>
              </label>
              
              {file && (
                <Chip 
                  label={`${file.name} (${(file.size / 1024).toFixed(1)} KB)`}
                  color="primary"
                  variant="outlined"
                />
              )}
            </Box>

            <FormControl fullWidth sx={{ mb: 3 }}>
              <InputLabel>🌐 Langue de l'analyse IA</InputLabel>
              <Select
                value={outputLanguage}
                label="🌐 Langue de l'analyse IA"
                onChange={(e) => setOutputLanguage(e.target.value)}
              >
                <MenuItem value="auto">🤖 Automatique (Intelligent)</MenuItem>
                <MenuItem value="french">🇫🇷 Français uniquement</MenuItem>
                <MenuItem value="arabic">🇹🇳 العربية فقط (Arabe uniquement)</MenuItem>
                <MenuItem value="original">📄 Langue d'origine</MenuItem>
              </Select>
            </FormControl>

            <Typography variant="caption" color="text.secondary" display="block" sx={{ mb: 2 }}>
              {outputLanguage === 'auto' && '🤖 L\'IA choisira automatiquement la meilleure langue pour l\'avocat tunisien'}
              {outputLanguage === 'french' && '🇫🇷 Toute l\'analyse sera en français, même si le document est en arabe'}
              {outputLanguage === 'arabic' && '🇹🇳 كل التحليل سيكون باللغة العربية، حتى لو كانت الوثيقة بالفرنسية'}
              {outputLanguage === 'original' && '📄 L\'analyse conservera la langue du document original'}
            </Typography>

            <Button
              variant="contained"
              onClick={() => {
                setActiveStep(1);
                setTimeout(() => {
                  setActiveStep(2);
                  handleAnalysis();
                }, 1000);
              }}
              disabled={!file}
              startIcon={<PsychologyIcon />}
              size="large"
            >
              Démarrer l'analyse IA
            </Button>
          </Box>
        );

      case 1:
        return (
          <Box>
            <Typography variant="h6" gutterBottom>
              🔍 Extraction OCR en cours...
            </Typography>
            <LinearProgress sx={{ mb: 2 }} />
            <Typography variant="body2" color="text.secondary">
              Extraction du texte avec support Arabe + Français + Anglais...
            </Typography>
          </Box>
        );

      case 2:
        return (
          <Box>
            <Typography variant="h6" gutterBottom>
              🤖 Analyse IA en cours...
            </Typography>
            {loading && <LinearProgress sx={{ mb: 2 }} />}
            <Typography variant="body2" color="text.secondary">
              Analyse intelligente avec OpenAI GPT-4 spécialisé pour le droit tunisien...
            </Typography>
            
            {analysisResult && (
              <Alert severity={analysisResult.success ? "success" : "error"} sx={{ mt: 2 }}>
                {analysisResult.success ? "✅ Analyse terminée avec succès !" : `❌ Erreur: ${analysisResult.error}`}
              </Alert>
            )}
          </Box>
        );

      case 3:
        return analysisResult ? (
          <Box>
            <Typography variant="h6" gutterBottom>
              📋 Validation des Informations Extraites
            </Typography>
            
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <Card variant="outlined">
                  <CardContent>
                    <Typography variant="subtitle1" gutterBottom>
                      📊 Résumé de l'analyse
                    </Typography>
                    <Box sx={{ mb: 2 }}>
                      <Chip 
                        label={`OCR: ${(analysisResult.ocr_extraction.ocr_confidence * 100).toFixed(1)}%`}
                        color={analysisResult.ocr_extraction.ocr_confidence > 0.7 ? 'success' : 'warning'}
                        sx={{ mr: 1, mb: 1 }}
                      />
                      <Chip 
                        label={`IA: ${(analysisResult.ai_analysis.confidence * 100).toFixed(1)}%`}
                        color={analysisResult.ai_analysis.confidence > 0.7 ? 'success' : 'warning'}
                        sx={{ mr: 1, mb: 1 }}
                      />
                      <Chip 
                        label={`🌐 ${outputLanguage === 'auto' ? 'Auto' : 
                               outputLanguage === 'french' ? 'Français' :
                               outputLanguage === 'arabic' ? 'العربية' : 'Original'}`}
                        color="info"
                        variant="outlined"
                        sx={{ mb: 1 }}
                      />
                    </Box>
                  </CardContent>
                </Card>
              </Grid>

              <Grid item xs={12} md={6}>
                <Card variant="outlined">
                  <CardContent>
                    <Typography variant="subtitle1" gutterBottom>
                      🤖 Informations IA
                    </Typography>
                    <Typography variant="body2" gutterBottom>
                      <strong>Type:</strong> {analysisResult.ai_analysis.document_type}
                    </Typography>
                    <Typography variant="body2" gutterBottom>
                      <strong>Langue détectée:</strong> {analysisResult.ai_analysis.language_detected || 'Non spécifiée'}
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>

              <Grid item xs={12}>
                <Accordion>
                  <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                    <Typography>🔍 Entités extraites ({analysisResult.ai_analysis.key_entities.length})</Typography>
                  </AccordionSummary>
                  <AccordionDetails>
                    <Box>
                      {analysisResult.ai_analysis.key_entities.map((entity, index) => (
                        <Chip key={index} label={entity} sx={{ mr: 1, mb: 1 }} />
                      ))}
                    </Box>
                  </AccordionDetails>
                </Accordion>
              </Grid>

              <Grid item xs={12}>
                <Accordion>
                  <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                    <Typography>📄 Texte extrait ({analysisResult.ocr_extraction.extracted_text_length} caractères)</Typography>
                  </AccordionSummary>
                  <AccordionDetails>
                    <Paper sx={{ p: 2, bgcolor: 'grey.50', maxHeight: 300, overflow: 'auto' }}>
                      <Typography 
                        variant="body2" 
                        component="pre" 
                        sx={{ 
                          whiteSpace: 'pre-wrap',
                          fontFamily: 'monospace',
                          fontSize: '0.75rem'
                        }}
                      >
                        {analysisResult.ocr_extraction.extracted_text_preview}
                      </Typography>
                    </Paper>
                  </AccordionDetails>
                </Accordion>
              </Grid>
            </Grid>

            <Box sx={{ mt: 3 }}>
              <Button
                variant="contained"
                onClick={handleCreateCase}
                startIcon={loading ? undefined : <SaveIcon />}
                size="large"
                disabled={loading}
              >
                {loading ? 'Création en cours...' : 'Créer le dossier juridique'}
              </Button>
              {loading && <LinearProgress sx={{ mt: 1 }} />}
            </Box>
          </Box>
        ) : null;

      case 4:
        return (
          <Box textAlign="center">
            <CheckCircleIcon color="success" sx={{ fontSize: 64, mb: 2 }} />
            <Typography variant="h5" gutterBottom>
              ✅ Dossier créé avec succès !
            </Typography>
            <Typography variant="body1" color="text.secondary" gutterBottom>
              Le dossier juridique a été créé automatiquement avec toutes les informations extraites.
            </Typography>
            <Button
              variant="outlined"
              onClick={() => {
                setActiveStep(0);
                setFile(null);
                setAnalysisResult(null);
                setValidationData(null);
              }}
              sx={{ mt: 2 }}
            >
              Analyser un nouveau document
            </Button>
          </Box>
        );

      default:
        return null;
    }
  };

  return (
    <Box sx={{ maxWidth: 1200, mx: 'auto', p: 3 }}>
      <Typography variant="h4" gutterBottom>
        🤖 Analyse IA Avancée - Workflow Complet
      </Typography>
      <Typography variant="subtitle1" color="text.secondary" gutterBottom sx={{ mb: 4 }}>
        Analyse intelligente de documents juridiques avec OCR trilingue et IA spécialisée pour le droit tunisien
      </Typography>

      <Card>
        <CardContent>
          <Stepper activeStep={activeStep} orientation="vertical">
            {steps.map((step, index) => (
              <Step key={step.label}>
                <StepLabel>
                  <Typography variant="h6">{step.label}</Typography>
                  <Typography variant="body2" color="text.secondary">
                    {step.description}
                  </Typography>
                </StepLabel>
                <StepContent>
                  {renderStepContent(index)}
                  
                  {index < 3 && index !== 1 && index !== 2 && (
                    <Box sx={{ mb: 2, mt: 2 }}>
                      <Button
                        disabled={index === 0}
                        onClick={handleBack}
                        sx={{ mr: 1 }}
                      >
                        Retour
                      </Button>
                      <Button
                        variant="contained"
                        onClick={handleNext}
                      >
                        Suivant
                      </Button>
                    </Box>
                  )}
                </StepContent>
              </Step>
            ))}
          </Stepper>
        </CardContent>
      </Card>
    </Box>
  );
}
