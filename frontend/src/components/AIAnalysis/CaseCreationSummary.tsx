import React, { useState } from 'react';
import {
  Box,
  Card,
  Card<PERSON>ontent,
  <PERSON><PERSON><PERSON>,
  <PERSON>ton,
  Grid,
  Alert,
  Chip,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Divider,
  CircularProgress,
  Accordion,
  AccordionSummary,
  AccordionDetails,
} from '@mui/material';
import {
  Gavel as GavelIcon,
  Person as PersonIcon,
  Description as DescriptionIcon,
  Event as EventIcon,
  CheckCircle as CheckIcon,
  Error as ErrorIcon,
  ExpandMore as ExpandMoreIcon,
} from '@mui/icons-material';
import { apiClient } from '../../services/api';

interface CaseCreationSummaryProps {
  validatedResults: any[];
  onCaseCreationComplete: (createdCases: any[]) => void;
}

interface CaseCreationStatus {
  id: string;
  status: 'pending' | 'creating' | 'success' | 'error';
  caseData: any;
  createdCase?: any;
  error?: string;
}

export default function CaseCreationSummary({ 
  validatedResults, 
  onCaseCreationComplete 
}: CaseCreationSummaryProps) {
  const [creationStatuses, setCreationStatuses] = useState<CaseCreationStatus[]>(
    validatedResults.map(result => ({
      id: result.id,
      status: 'pending' as const,
      caseData: result,
    }))
  );
  const [isCreating, setIsCreating] = useState(false);

  const handleCreateAllCases = async () => {
    setIsCreating(true);
    const createdCases: any[] = [];

    for (let i = 0; i < creationStatuses.length; i++) {
      const status = creationStatuses[i];
      
      // Update status to creating
      setCreationStatuses(prev => prev.map(s => 
        s.id === status.id 
          ? { ...s, status: 'creating' as const }
          : s
      ));

      try {
        // Simulate case creation delay
        await new Promise(resolve => setTimeout(resolve, 2000));

        // Try real API first, fallback to demo
        let createdCase;
        try {
          createdCase = await apiClient.createLegalCase({
            title: status.caseData.caseTitle,
            description: status.caseData.documentSummary,
            status: 'open',
            priority: 'medium',
            // Add more fields as needed
          });
        } catch (apiError) {
          // Demo mode: create mock case
          createdCase = {
            id: Date.now() + i,
            title: status.caseData.caseTitle,
            description: status.caseData.documentSummary,
            status: 'open',
            priority: 'medium',
            created_at: new Date().toISOString(),
            client_name: status.caseData.clientName,
            case_type: status.caseData.caseType,
            plaintiff: status.caseData.plaintiff,
            defendant: status.caseData.defendant,
            witnesses: status.caseData.witnesses,
            lawyers: status.caseData.lawyers,
            key_facts: status.caseData.keyFacts,
            validation_notes: status.caseData.validationNotes,
            source_documents: [status.caseData.originalAnalysis],
          };
        }

        // Update status to success
        setCreationStatuses(prev => prev.map(s => 
          s.id === status.id 
            ? { ...s, status: 'success' as const, createdCase }
            : s
        ));

        createdCases.push(createdCase);

      } catch (error) {
        console.error('Case creation failed:', error);
        
        // Update status to error
        setCreationStatuses(prev => prev.map(s => 
          s.id === status.id 
            ? { 
                ...s, 
                status: 'error' as const, 
                error: error instanceof Error ? error.message : 'Case creation failed' 
              }
            : s
        ));
      }
    }

    setIsCreating(false);
    onCaseCreationComplete(createdCases);
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'creating':
        return <CircularProgress size={20} />;
      case 'success':
        return <CheckIcon color="success" />;
      case 'error':
        return <ErrorIcon color="error" />;
      default:
        return <GavelIcon />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'creating': return 'primary';
      case 'success': return 'success';
      case 'error': return 'error';
      default: return 'default';
    }
  };

  const allCompleted = creationStatuses.every(s => s.status === 'success' || s.status === 'error');
  const successCount = creationStatuses.filter(s => s.status === 'success').length;

  return (
    <Box>
      <Typography variant="h6" gutterBottom>
        ⚖️ Create Legal Cases ({validatedResults.length} case{validatedResults.length > 1 ? 's' : ''})
      </Typography>
      
      <Alert severity="info" sx={{ mb: 3 }}>
        Review the cases to be created and confirm. Source documents will be automatically associated with each case.
      </Alert>

      {/* Summary */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            📊 Creation Summary
          </Typography>
          <Grid container spacing={2}>
            <Grid item xs={6} sm={3}>
              <Box textAlign="center">
                <Typography variant="h4" color="primary.main">
                  {validatedResults.length}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Cases to Create
                </Typography>
              </Box>
            </Grid>
            <Grid item xs={6} sm={3}>
              <Box textAlign="center">
                <Typography variant="h4" color="success.main">
                  {successCount}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Successfully Created
                </Typography>
              </Box>
            </Grid>
            <Grid item xs={6} sm={3}>
              <Box textAlign="center">
                <Typography variant="h4" color="warning.main">
                  {creationStatuses.filter(s => s.status === 'creating').length}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  In Progress
                </Typography>
              </Box>
            </Grid>
            <Grid item xs={6} sm={3}>
              <Box textAlign="center">
                <Typography variant="h4" color="error.main">
                  {creationStatuses.filter(s => s.status === 'error').length}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Failed
                </Typography>
              </Box>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Case Details */}
      {creationStatuses.map((status, index) => (
        <Accordion key={status.id} sx={{ mb: 2 }}>
          <AccordionSummary expandIcon={<ExpandMoreIcon />}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, width: '100%' }}>
              {getStatusIcon(status.status)}
              <Typography variant="h6" sx={{ flexGrow: 1 }}>
                Case {index + 1}: {status.caseData.caseTitle}
              </Typography>
              <Chip 
                label={status.status.toUpperCase()}
                color={getStatusColor(status.status)}
                size="small"
              />
            </Box>
          </AccordionSummary>
          
          <AccordionDetails>
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <Typography variant="h6" gutterBottom>
                  📋 Case Details
                </Typography>
                <List dense>
                  <ListItem>
                    <ListItemIcon><PersonIcon /></ListItemIcon>
                    <ListItemText 
                      primary="Client" 
                      secondary={status.caseData.clientName || 'Not specified'} 
                    />
                  </ListItem>
                  <ListItem>
                    <ListItemIcon><GavelIcon /></ListItemIcon>
                    <ListItemText 
                      primary="Case Type" 
                      secondary={status.caseData.caseType} 
                    />
                  </ListItem>
                  <ListItem>
                    <ListItemIcon><DescriptionIcon /></ListItemIcon>
                    <ListItemText 
                      primary="Plaintiff" 
                      secondary={status.caseData.plaintiff || 'Not specified'} 
                    />
                  </ListItem>
                  <ListItem>
                    <ListItemIcon><DescriptionIcon /></ListItemIcon>
                    <ListItemText 
                      primary="Defendant" 
                      secondary={status.caseData.defendant || 'Not specified'} 
                    />
                  </ListItem>
                </List>
              </Grid>
              
              <Grid item xs={12} md={6}>
                <Typography variant="h6" gutterBottom>
                  📄 Document Information
                </Typography>
                <Typography variant="body2" paragraph>
                  <strong>Summary:</strong> {status.caseData.documentSummary.substring(0, 200)}...
                </Typography>
                <Typography variant="body2" paragraph>
                  <strong>Key Facts:</strong> {status.caseData.keyFacts.join(', ')}
                </Typography>
                {status.caseData.validationNotes && (
                  <Typography variant="body2" paragraph>
                    <strong>Validation Notes:</strong> {status.caseData.validationNotes}
                  </Typography>
                )}
              </Grid>

              {status.status === 'error' && status.error && (
                <Grid item xs={12}>
                  <Alert severity="error">
                    <strong>Creation Failed:</strong> {status.error}
                  </Alert>
                </Grid>
              )}

              {status.status === 'success' && status.createdCase && (
                <Grid item xs={12}>
                  <Alert severity="success">
                    <strong>Case Created Successfully!</strong> Case ID: {status.createdCase.id}
                  </Alert>
                </Grid>
              )}
            </Grid>
          </AccordionDetails>
        </Accordion>
      ))}

      {/* Action Buttons */}
      <Box sx={{ mt: 3, textAlign: 'center' }}>
        {!allCompleted ? (
          <Button
            variant="contained"
            size="large"
            startIcon={isCreating ? <CircularProgress size={20} /> : <GavelIcon />}
            onClick={handleCreateAllCases}
            disabled={isCreating}
          >
            {isCreating ? 'Creating Cases...' : 'Create All Legal Cases'}
          </Button>
        ) : (
          <Box>
            <Alert severity="success" sx={{ mb: 2 }}>
              🎉 Case creation completed! {successCount} case{successCount !== 1 ? 's' : ''} created successfully.
            </Alert>
            <Button
              variant="contained"
              size="large"
              onClick={() => window.location.href = '/legal-cases'}
            >
              View Created Cases
            </Button>
          </Box>
        )}
        
        <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
          All source documents will be automatically associated with their respective cases.
        </Typography>
      </Box>
    </Box>
  );
}
