import React, { useState, useCallback } from 'react';
import {
  Box,
  Button,
  Typography,
  LinearProgress,
  Alert,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Chip,
  Card,
  CardContent,
} from '@mui/material';
import {
  CloudUpload as CloudUploadIcon,
  Description as DescriptionIcon,
  Delete as DeleteIcon,
  CheckCircle as CheckCircleIcon,
  <PERSON>rror as ErrorIcon,
  Refresh as RefreshIcon,
} from '@mui/icons-material';
import { apiClient, AnalysisResult } from '../../services/api';
import { useAuth } from '../../contexts/AuthContext';

interface UploadedFile {
  id: string;
  file: File;
  status: 'uploading' | 'success' | 'error' | 'analyzing';
  progress: number;
  error?: string;
  analysisResult?: AnalysisResult;
  documentId?: string;
}

interface DocumentUploadProps {
  onAnalysisComplete?: (results: AnalysisResult[]) => void;
  maxFileSize?: number; // in MB
  acceptedTypes?: string[];
}

export default function DocumentUpload({
  onAnalysisComplete,
  maxFileSize = 10,
  acceptedTypes = ['.pdf', '.doc', '.docx']
}: DocumentUploadProps) {
  const [files, setFiles] = useState<UploadedFile[]>([]);
  const [isDragOver, setIsDragOver] = useState(false);
  const { isAuthenticated } = useAuth();

  // Drag & Drop handlers
  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
    
    const droppedFiles = Array.from(e.dataTransfer.files);
    handleFiles(droppedFiles);
  }, []);

  // File validation
  const validateFile = (file: File): string | null => {
    // Check file size
    if (file.size > maxFileSize * 1024 * 1024) {
      return `File size exceeds ${maxFileSize}MB limit`;
    }

    // Check file type
    const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase();
    if (!acceptedTypes.includes(fileExtension)) {
      return `File type not supported. Accepted types: ${acceptedTypes.join(', ')}`;
    }

    return null;
  };

  // Handle file selection
  const handleFiles = (selectedFiles: File[]) => {
    const newFiles: UploadedFile[] = [];

    selectedFiles.forEach(file => {
      const error = validateFile(file);
      const uploadedFile: UploadedFile = {
        id: Date.now() + Math.random().toString(),
        file,
        status: error ? 'error' : 'uploading',
        progress: 0,
        error,
      };
      newFiles.push(uploadedFile);
    });

    setFiles(prev => [...prev, ...newFiles]);

    // Start upload for valid files
    newFiles.forEach(uploadedFile => {
      if (!uploadedFile.error) {
        uploadFile(uploadedFile);
      }
    });
  };

  // File input handler
  const handleFileInput = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      handleFiles(Array.from(e.target.files));
    }
  };

  // Upload with fallback to demo mode
  const uploadFile = async (uploadedFile: UploadedFile) => {
    if (!isAuthenticated) {
      setFiles(prev => prev.map(f =>
        f.id === uploadedFile.id
          ? { ...f, status: 'error', error: 'Authentication required' }
          : f
      ));
      return;
    }

    try {
      // Try real API first, fallback to demo mode
      try {
        // Upload file with progress tracking
        const uploadResponse = await apiClient.uploadFile(
          uploadedFile.file,
          (progress) => {
            setFiles(prev => prev.map(f =>
              f.id === uploadedFile.id
                ? { ...f, progress }
                : f
            ));
          }
        );

        // Mark as uploaded successfully
        setFiles(prev => prev.map(f =>
          f.id === uploadedFile.id
            ? { ...f, status: 'success', progress: 100, documentId: uploadResponse.id }
            : f
        ));

        // Start analysis
        analyzeFile(uploadedFile, uploadResponse.id);

      } catch (apiError) {
        console.log('API not available, using demo mode');

        // Demo mode: simulate upload progress
        for (let progress = 0; progress <= 100; progress += 20) {
          await new Promise(resolve => setTimeout(resolve, 300));
          setFiles(prev => prev.map(f =>
            f.id === uploadedFile.id
              ? { ...f, progress }
              : f
          ));
        }

        // Generate mock document ID
        const mockDocumentId = `demo-doc-${Date.now()}`;

        // Mark as uploaded successfully
        setFiles(prev => prev.map(f =>
          f.id === uploadedFile.id
            ? { ...f, status: 'success', progress: 100, documentId: mockDocumentId }
            : f
        ));

        // Start demo analysis
        analyzeDemoFile(uploadedFile, mockDocumentId);
      }

    } catch (error) {
      console.error('Upload failed:', error);
      setFiles(prev => prev.map(f =>
        f.id === uploadedFile.id
          ? {
              ...f,
              status: 'error',
              error: error instanceof Error ? error.message : 'Upload failed'
            }
          : f
      ));
    }
  };

  // Real AI analysis with API
  const analyzeFile = async (uploadedFile: UploadedFile, documentId: string) => {
    setFiles(prev => prev.map(f =>
      f.id === uploadedFile.id
        ? { ...f, status: 'analyzing' }
        : f
    ));

    try {
      // Call real analysis API
      const analysisResult = await apiClient.analyzeDocument(documentId);

      setFiles(prev => prev.map(f =>
        f.id === uploadedFile.id
          ? { ...f, status: 'success', analysisResult }
          : f
      ));

      // Notify parent component with all completed results
      if (onAnalysisComplete) {
        const allResults = files
          .filter(f => f.analysisResult)
          .map(f => f.analysisResult!)
          .concat([analysisResult]);
        onAnalysisComplete(allResults);
      }

    } catch (error) {
      console.error('Analysis failed:', error);
      setFiles(prev => prev.map(f =>
        f.id === uploadedFile.id
          ? {
              ...f,
              status: 'error',
              error: error instanceof Error ? error.message : 'Analysis failed'
            }
          : f
      ));
    }
  };

  // Demo AI analysis
  const analyzeDemoFile = async (uploadedFile: UploadedFile, documentId: string) => {
    setFiles(prev => prev.map(f =>
      f.id === uploadedFile.id
        ? { ...f, status: 'analyzing' }
        : f
    ));

    try {
      // Simulate AI analysis delay
      await new Promise(resolve => setTimeout(resolve, 3000));

      // Generate realistic mock results based on filename
      const filename = uploadedFile.file.name.toLowerCase();
      let documentType = 'Legal Document';
      let entities = ['TunLeg Corp', 'John Doe', '2024-01-15'];
      let actions = ['Create new case', 'Schedule review', 'Notify client'];

      if (filename.includes('contrat') || filename.includes('contract')) {
        documentType = 'Legal Contract';
        entities = ['Buyer: Jean Martin', 'Seller: Marie Dupont', 'Property: 123 Rue de la Paix', 'Date: 2024-01-15', 'Amount: €250,000'];
        actions = ['Create contract case', 'Schedule signature', 'Verify property documents', 'Contact notary'];
      } else if (filename.includes('achat') || filename.includes('purchase')) {
        documentType = 'Purchase Agreement';
        entities = ['Purchaser', 'Vendor', 'Purchase Price', 'Closing Date'];
        actions = ['Create purchase case', 'Review terms', 'Schedule closing'];
      } else if (filename.includes('liste') || filename.includes('list')) {
        documentType = 'Legal Inventory';
        entities = ['Item 1', 'Item 2', 'Item 3', 'Total Value'];
        actions = ['Verify inventory', 'Update records', 'Generate report'];
      }

      const mockResults: AnalysisResult = {
        id: `analysis-${documentId}`,
        document_id: documentId,
        document_type: documentType,
        extracted_text: `Extracted text from ${uploadedFile.file.name}:\n\nThis is a ${documentType} containing important legal information. The document includes various clauses and provisions that require careful review.\n\nKey sections identified:\n- Parties involved\n- Terms and conditions\n- Legal obligations\n- Important dates and deadlines\n\n[Full text would appear here in a real analysis...]`,
        key_entities: entities,
        suggested_actions: actions,
        confidence: 0.85 + Math.random() * 0.1, // Random confidence between 85-95%
        created_at: new Date().toISOString(),
      };

      setFiles(prev => prev.map(f =>
        f.id === uploadedFile.id
          ? { ...f, status: 'success', analysisResult: mockResults }
          : f
      ));

      // Notify parent component with all completed results
      if (onAnalysisComplete) {
        const allResults = files
          .filter(f => f.analysisResult)
          .map(f => f.analysisResult!)
          .concat([mockResults]);
        onAnalysisComplete(allResults);
      }

    } catch (error) {
      console.error('Demo analysis failed:', error);
      setFiles(prev => prev.map(f =>
        f.id === uploadedFile.id
          ? {
              ...f,
              status: 'error',
              error: 'Demo analysis failed'
            }
          : f
      ));
    }
  };

  // Remove file
  const removeFile = (fileId: string) => {
    setFiles(prev => prev.filter(f => f.id !== fileId));
  };

  // Retry upload/analysis
  const retryFile = (fileId: string) => {
    const file = files.find(f => f.id === fileId);
    if (file) {
      setFiles(prev => prev.map(f =>
        f.id === fileId
          ? { ...f, status: 'uploading', progress: 0, error: undefined, analysisResult: undefined, documentId: undefined }
          : f
      ));
      uploadFile(file);
    }
  };

  // Get status icon
  const getStatusIcon = (file: UploadedFile) => {
    switch (file.status) {
      case 'success':
        return <CheckCircleIcon color="success" />;
      case 'error':
        return <ErrorIcon color="error" />;
      case 'analyzing':
        return <RefreshIcon className="animate-spin" color="primary" />;
      default:
        return <DescriptionIcon />;
    }
  };

  // Get status chip
  const getStatusChip = (file: UploadedFile) => {
    switch (file.status) {
      case 'uploading':
        return <Chip label="Uploading..." color="primary" size="small" />;
      case 'success':
        return file.analysisResult 
          ? <Chip label="Analyzed" color="success" size="small" />
          : <Chip label="Uploaded" color="success" size="small" />;
      case 'analyzing':
        return <Chip label="Analyzing..." color="secondary" size="small" />;
      case 'error':
        return <Chip label="Error" color="error" size="small" />;
      default:
        return null;
    }
  };

  return (
    <Box>
      {/* Drag & Drop Zone */}
      <Box
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
        sx={{
          border: '2px dashed',
          borderColor: isDragOver ? 'primary.dark' : 'primary.main',
          borderRadius: 2,
          p: 4,
          textAlign: 'center',
          bgcolor: isDragOver ? 'primary.100' : 'primary.50',
          cursor: 'pointer',
          transition: 'all 0.3s',
          '&:hover': {
            bgcolor: 'primary.100',
            borderColor: 'primary.dark'
          }
        }}
      >
        <CloudUploadIcon sx={{ fontSize: '4rem', color: 'primary.main', mb: 2 }} />
        <Typography variant="h6" gutterBottom>
          {isDragOver ? 'Drop files here' : 'Drag & Drop Documents Here'}
        </Typography>
        <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
          Or click to browse files ({acceptedTypes.join(', ')} supported)
        </Typography>
        
        <input
          type="file"
          multiple
          accept={acceptedTypes.join(',')}
          onChange={handleFileInput}
          style={{ display: 'none' }}
          id="file-upload"
        />
        <label htmlFor="file-upload">
          <Button variant="contained" component="span" size="large">
            Select Files
          </Button>
        </label>
        
        <Typography variant="caption" display="block" color="text.secondary" sx={{ mt: 1 }}>
          Maximum file size: {maxFileSize}MB per file
        </Typography>
      </Box>

      {/* File List */}
      {files.length > 0 && (
        <Card sx={{ mt: 3 }}>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Uploaded Files ({files.length})
            </Typography>
            
            <List>
              {files.map((file) => (
                <ListItem key={file.id} divider>
                  <ListItemIcon>
                    {getStatusIcon(file)}
                  </ListItemIcon>
                  
                  <ListItemText
                    primary={
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Typography variant="body1">{file.file.name}</Typography>
                        {getStatusChip(file)}
                      </Box>
                    }
                    secondary={
                      <Box>
                        <Typography variant="caption" color="text.secondary">
                          {(file.file.size / 1024 / 1024).toFixed(2)} MB
                        </Typography>
                        
                        {file.status === 'uploading' && (
                          <Box sx={{ mt: 1 }}>
                            <LinearProgress 
                              variant="determinate" 
                              value={file.progress} 
                              sx={{ height: 6, borderRadius: 3 }}
                            />
                            <Typography variant="caption" color="text.secondary">
                              {file.progress}% uploaded
                            </Typography>
                          </Box>
                        )}
                        
                        {file.error && (
                          <Alert severity="error" sx={{ mt: 1 }}>
                            {file.error}
                          </Alert>
                        )}
                        
                        {file.analysisResult && (
                          <Box sx={{ mt: 1 }}>
                            <Typography variant="caption" color="success.main">
                              ✅ Analysis complete - {file.analysisResult.documentType} 
                              (Confidence: {(file.analysisResult.confidence * 100).toFixed(0)}%)
                            </Typography>
                          </Box>
                        )}
                      </Box>
                    }
                  />
                  
                  <ListItemSecondaryAction>
                    {file.status === 'error' && (
                      <IconButton onClick={() => retryFile(file.id)} color="primary">
                        <RefreshIcon />
                      </IconButton>
                    )}
                    <IconButton onClick={() => removeFile(file.id)} color="error">
                      <DeleteIcon />
                    </IconButton>
                  </ListItemSecondaryAction>
                </ListItem>
              ))}
            </List>
          </CardContent>
        </Card>
      )}
    </Box>
  );
}
