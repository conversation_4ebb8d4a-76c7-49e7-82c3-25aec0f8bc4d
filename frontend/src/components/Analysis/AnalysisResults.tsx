import React from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  <PERSON><PERSON><PERSON>,
  Chip,
  <PERSON>ton,
  Grid,
  Alert,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Divider,
} from '@mui/material';
import {
  ExpandMore as ExpandMoreIcon,
  Person as PersonIcon,
  Business as BusinessIcon,
  Event as EventIcon,
  Assignment as AssignmentIcon,
  Visibility as VisibilityIcon,
  GetApp as GetAppIcon,
  Gavel as GavelIcon,
} from '@mui/icons-material';

interface AnalysisResult {
  documentType: string;
  extractedText: string;
  keyEntities: string[];
  suggestedActions: string[];
  confidence: number;
}

interface AnalysisResultsProps {
  results: AnalysisResult[];
  onCreateCase?: (result: AnalysisResult) => void;
  onExportResults?: (results: AnalysisResult[]) => void;
}

export default function AnalysisResults({ 
  results, 
  onCreateCase,
  onExportResults 
}: AnalysisResultsProps) {
  if (results.length === 0) {
    return (
      <Alert severity="info" sx={{ mt: 2 }}>
        No analysis results yet. Upload and analyze documents to see extracted information here.
      </Alert>
    );
  }

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 0.8) return 'success';
    if (confidence >= 0.6) return 'warning';
    return 'error';
  };

  const getEntityIcon = (entity: string) => {
    // Simple heuristic to determine entity type
    if (entity.includes('@') || entity.toLowerCase().includes('email')) {
      return <PersonIcon />;
    }
    if (entity.includes('Corp') || entity.includes('LLC') || entity.includes('Inc')) {
      return <BusinessIcon />;
    }
    if (entity.match(/\d{4}-\d{2}-\d{2}/) || entity.match(/\d{1,2}\/\d{1,2}\/\d{4}/)) {
      return <EventIcon />;
    }
    return <AssignmentIcon />;
  };

  return (
    <Box>
      {/* Summary */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
            <Typography variant="h6">
              📊 Analysis Summary ({results.length} document{results.length > 1 ? 's' : ''})
            </Typography>
            <Button 
              variant="outlined" 
              startIcon={<GetAppIcon />}
              onClick={() => onExportResults?.(results)}
            >
              Export Results
            </Button>
          </Box>
          
          <Grid container spacing={2}>
            <Grid item xs={12} sm={4}>
              <Box textAlign="center">
                <Typography variant="h4" color="primary.main">
                  {results.length}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Documents Analyzed
                </Typography>
              </Box>
            </Grid>
            <Grid item xs={12} sm={4}>
              <Box textAlign="center">
                <Typography variant="h4" color="success.main">
                  {results.reduce((acc, r) => acc + r.keyEntities.length, 0)}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Entities Extracted
                </Typography>
              </Box>
            </Grid>
            <Grid item xs={12} sm={4}>
              <Box textAlign="center">
                <Typography variant="h4" color="secondary.main">
                  {Math.round(results.reduce((acc, r) => acc + r.confidence, 0) / results.length * 100)}%
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Avg. Confidence
                </Typography>
              </Box>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Individual Results */}
      {results.map((result, index) => (
        <Accordion key={index} sx={{ mb: 2 }}>
          <AccordionSummary expandIcon={<ExpandMoreIcon />}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, width: '100%' }}>
              <Typography variant="h6" sx={{ flexGrow: 1 }}>
                Document {index + 1}: {result.documentType}
              </Typography>
              <Chip 
                label={`${(result.confidence * 100).toFixed(0)}% confidence`}
                color={getConfidenceColor(result.confidence)}
                size="small"
              />
            </Box>
          </AccordionSummary>
          
          <AccordionDetails>
            <Grid container spacing={3}>
              {/* Document Info */}
              <Grid item xs={12} md={6}>
                <Card variant="outlined">
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      📄 Document Information
                    </Typography>
                    <Box sx={{ mb: 2 }}>
                      <Typography variant="body2" color="text.secondary">
                        Document Type:
                      </Typography>
                      <Typography variant="body1" sx={{ fontWeight: 'medium' }}>
                        {result.documentType}
                      </Typography>
                    </Box>
                    <Box sx={{ mb: 2 }}>
                      <Typography variant="body2" color="text.secondary">
                        Confidence Score:
                      </Typography>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Typography variant="body1" sx={{ fontWeight: 'medium' }}>
                          {(result.confidence * 100).toFixed(1)}%
                        </Typography>
                        <Chip 
                          label={result.confidence >= 0.8 ? 'High' : result.confidence >= 0.6 ? 'Medium' : 'Low'}
                          color={getConfidenceColor(result.confidence)}
                          size="small"
                        />
                      </Box>
                    </Box>
                  </CardContent>
                </Card>
              </Grid>

              {/* Key Entities */}
              <Grid item xs={12} md={6}>
                <Card variant="outlined">
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      🏷️ Key Entities ({result.keyEntities.length})
                    </Typography>
                    <List dense>
                      {result.keyEntities.map((entity, entityIndex) => (
                        <ListItem key={entityIndex}>
                          <ListItemIcon>
                            {getEntityIcon(entity)}
                          </ListItemIcon>
                          <ListItemText primary={entity} />
                        </ListItem>
                      ))}
                    </List>
                  </CardContent>
                </Card>
              </Grid>

              {/* Extracted Text Preview */}
              <Grid item xs={12}>
                <Card variant="outlined">
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      📝 Extracted Text Preview
                    </Typography>
                    <Box 
                      sx={{ 
                        bgcolor: 'grey.50', 
                        p: 2, 
                        borderRadius: 1, 
                        maxHeight: 200, 
                        overflow: 'auto',
                        fontFamily: 'monospace',
                        fontSize: '0.875rem'
                      }}
                    >
                      {result.extractedText}
                    </Box>
                    <Button 
                      variant="text" 
                      startIcon={<VisibilityIcon />} 
                      sx={{ mt: 1 }}
                    >
                      View Full Text
                    </Button>
                  </CardContent>
                </Card>
              </Grid>

              {/* Suggested Actions */}
              <Grid item xs={12}>
                <Card variant="outlined">
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      💡 Suggested Actions
                    </Typography>
                    <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap', mb: 2 }}>
                      {result.suggestedActions.map((action, actionIndex) => (
                        <Chip 
                          key={actionIndex}
                          label={action}
                          variant="outlined"
                          color="primary"
                          size="small"
                        />
                      ))}
                    </Box>
                    <Divider sx={{ my: 2 }} />
                    <Box sx={{ display: 'flex', gap: 2 }}>
                      <Button 
                        variant="contained" 
                        startIcon={<GavelIcon />}
                        onClick={() => onCreateCase?.(result)}
                      >
                        Create Legal Case
                      </Button>
                      <Button variant="outlined">
                        Schedule Review
                      </Button>
                      <Button variant="outlined">
                        Export Data
                      </Button>
                    </Box>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          </AccordionDetails>
        </Accordion>
      ))}
    </Box>
  );
}
