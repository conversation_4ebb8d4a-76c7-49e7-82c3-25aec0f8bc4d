import React, { useState } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>ton,
  <PERSON>rid,
  <PERSON>ert,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Divider,
  Menu,
  MenuItem,
  CircularProgress,
} from '@mui/material';
import {
  ExpandMore as ExpandMoreIcon,
  Person as PersonIcon,
  Business as BusinessIcon,
  Event as EventIcon,
  Assignment as AssignmentIcon,
  Visibility as VisibilityIcon,
  GetApp as GetAppIcon,
  Gavel as GavelIcon,
  FileDownload as FileDownloadIcon,
} from '@mui/icons-material';
import { apiClient, downloadBlob, AnalysisResult as ApiAnalysisResult } from '../../services/api';
import CreateCaseDialog from '../LegalCase/CreateCaseDialog';

interface AnalysisResultsProps {
  results: ApiAnalysisResult[];
  onCreateCase?: (result: ApiAnalysisResult) => void;
  onExportResults?: (results: ApiAnalysisResult[]) => void;
}

export default function AnalysisResults({
  results,
  onCreateCase,
  onExportResults
}: AnalysisResultsProps) {
  const [selectedResult, setSelectedResult] = useState<ApiAnalysisResult | null>(null);
  const [createCaseDialogOpen, setCreateCaseDialogOpen] = useState(false);
  const [exportMenuAnchor, setExportMenuAnchor] = useState<null | HTMLElement>(null);
  const [isExporting, setIsExporting] = useState(false);
  if (results.length === 0) {
    return (
      <Alert severity="info" sx={{ mt: 2 }}>
        No analysis results yet. Upload and analyze documents to see extracted information here.
      </Alert>
    );
  }

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 0.8) return 'success';
    if (confidence >= 0.6) return 'warning';
    return 'error';
  };

  const getEntityIcon = (entity: string) => {
    // Simple heuristic to determine entity type
    if (entity.includes('@') || entity.toLowerCase().includes('email')) {
      return <PersonIcon />;
    }
    if (entity.includes('Corp') || entity.includes('LLC') || entity.includes('Inc')) {
      return <BusinessIcon />;
    }
    if (entity.match(/\d{4}-\d{2}-\d{2}/) || entity.match(/\d{1,2}\/\d{1,2}\/\d{4}/)) {
      return <EventIcon />;
    }
    return <AssignmentIcon />;
  };

  const handleCreateCase = (result: ApiAnalysisResult) => {
    setSelectedResult(result);
    setCreateCaseDialogOpen(true);
  };

  const handleExportClick = (event: React.MouseEvent<HTMLElement>) => {
    setExportMenuAnchor(event.currentTarget);
  };

  const handleExportClose = () => {
    setExportMenuAnchor(null);
  };

  const handleExport = async (format: 'json' | 'csv' | 'pdf') => {
    setIsExporting(true);
    setExportMenuAnchor(null);

    try {
      const analysisIds = results.map(r => r.id);
      const blob = await apiClient.exportAnalysisResults(analysisIds, format);

      const timestamp = new Date().toISOString().split('T')[0];
      const filename = `analysis-results-${timestamp}.${format}`;

      downloadBlob(blob, filename);
    } catch (error) {
      console.error('Export failed:', error);
      // You could show a toast notification here
    } finally {
      setIsExporting(false);
    }
  };

  return (
    <Box>
      {/* Summary */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
            <Typography variant="h6">
              📊 Analysis Summary ({results.length} document{results.length > 1 ? 's' : ''})
            </Typography>
            <Button
              variant="outlined"
              startIcon={isExporting ? <CircularProgress size={16} /> : <GetAppIcon />}
              onClick={handleExportClick}
              disabled={isExporting}
            >
              {isExporting ? 'Exporting...' : 'Export Results'}
            </Button>
          </Box>
          
          <Grid container spacing={2}>
            <Grid item xs={12} sm={4}>
              <Box textAlign="center">
                <Typography variant="h4" color="primary.main">
                  {results.length}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Documents Analyzed
                </Typography>
              </Box>
            </Grid>
            <Grid item xs={12} sm={4}>
              <Box textAlign="center">
                <Typography variant="h4" color="success.main">
                  {results.reduce((acc, r) => acc + r.keyEntities.length, 0)}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Entities Extracted
                </Typography>
              </Box>
            </Grid>
            <Grid item xs={12} sm={4}>
              <Box textAlign="center">
                <Typography variant="h4" color="secondary.main">
                  {Math.round(results.reduce((acc, r) => acc + r.confidence, 0) / results.length * 100)}%
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Avg. Confidence
                </Typography>
              </Box>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Individual Results */}
      {results.map((result, index) => (
        <Accordion key={index} sx={{ mb: 2 }}>
          <AccordionSummary expandIcon={<ExpandMoreIcon />}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, width: '100%' }}>
              <Typography variant="h6" sx={{ flexGrow: 1 }}>
                Document {index + 1}: {result.document_type}
              </Typography>
              <Chip
                label={`${(result.confidence * 100).toFixed(0)}% confidence`}
                color={getConfidenceColor(result.confidence)}
                size="small"
              />
            </Box>
          </AccordionSummary>
          
          <AccordionDetails>
            <Grid container spacing={3}>
              {/* Document Info */}
              <Grid item xs={12} md={6}>
                <Card variant="outlined">
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      📄 Document Information
                    </Typography>
                    <Box sx={{ mb: 2 }}>
                      <Typography variant="body2" color="text.secondary">
                        Document Type:
                      </Typography>
                      <Typography variant="body1" sx={{ fontWeight: 'medium' }}>
                        {result.document_type}
                      </Typography>
                    </Box>
                    <Box sx={{ mb: 2 }}>
                      <Typography variant="body2" color="text.secondary">
                        Confidence Score:
                      </Typography>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Typography variant="body1" sx={{ fontWeight: 'medium' }}>
                          {(result.confidence * 100).toFixed(1)}%
                        </Typography>
                        <Chip 
                          label={result.confidence >= 0.8 ? 'High' : result.confidence >= 0.6 ? 'Medium' : 'Low'}
                          color={getConfidenceColor(result.confidence)}
                          size="small"
                        />
                      </Box>
                    </Box>
                  </CardContent>
                </Card>
              </Grid>

              {/* Key Entities */}
              <Grid item xs={12} md={6}>
                <Card variant="outlined">
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      🏷️ Key Entities ({result.keyEntities.length})
                    </Typography>
                    <List dense>
                      {result.key_entities.map((entity, entityIndex) => (
                        <ListItem key={entityIndex}>
                          <ListItemIcon>
                            {getEntityIcon(entity)}
                          </ListItemIcon>
                          <ListItemText primary={entity} />
                        </ListItem>
                      ))}
                    </List>
                  </CardContent>
                </Card>
              </Grid>

              {/* Extracted Text Preview */}
              <Grid item xs={12}>
                <Card variant="outlined">
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      📝 Extracted Text Preview
                    </Typography>
                    <Box 
                      sx={{ 
                        bgcolor: 'grey.50', 
                        p: 2, 
                        borderRadius: 1, 
                        maxHeight: 200, 
                        overflow: 'auto',
                        fontFamily: 'monospace',
                        fontSize: '0.875rem'
                      }}
                    >
                      {result.extracted_text}
                    </Box>
                    <Button 
                      variant="text" 
                      startIcon={<VisibilityIcon />} 
                      sx={{ mt: 1 }}
                    >
                      View Full Text
                    </Button>
                  </CardContent>
                </Card>
              </Grid>

              {/* Suggested Actions */}
              <Grid item xs={12}>
                <Card variant="outlined">
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      💡 Suggested Actions
                    </Typography>
                    <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap', mb: 2 }}>
                      {result.suggested_actions.map((action, actionIndex) => (
                        <Chip
                          key={actionIndex}
                          label={action}
                          variant="outlined"
                          color="primary"
                          size="small"
                        />
                      ))}
                    </Box>
                    <Divider sx={{ my: 2 }} />
                    <Box sx={{ display: 'flex', gap: 2 }}>
                      <Button
                        variant="contained"
                        startIcon={<GavelIcon />}
                        onClick={() => handleCreateCase(result)}
                      >
                        Create Legal Case
                      </Button>
                      <Button variant="outlined">
                        Schedule Review
                      </Button>
                      <Button variant="outlined">
                        Export Data
                      </Button>
                    </Box>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          </AccordionDetails>
        </Accordion>
      ))}

      {/* Export Menu */}
      <Menu
        anchorEl={exportMenuAnchor}
        open={Boolean(exportMenuAnchor)}
        onClose={handleExportClose}
      >
        <MenuItem onClick={() => handleExport('json')}>
          <FileDownloadIcon sx={{ mr: 1 }} />
          Export as JSON
        </MenuItem>
        <MenuItem onClick={() => handleExport('csv')}>
          <FileDownloadIcon sx={{ mr: 1 }} />
          Export as CSV
        </MenuItem>
        <MenuItem onClick={() => handleExport('pdf')}>
          <FileDownloadIcon sx={{ mr: 1 }} />
          Export as PDF
        </MenuItem>
      </Menu>

      {/* Create Case Dialog */}
      <CreateCaseDialog
        open={createCaseDialogOpen}
        onClose={() => setCreateCaseDialogOpen(false)}
        analysisResult={selectedResult || undefined}
        onCaseCreated={(legalCase) => {
          console.log('Legal case created:', legalCase);
          // You could show a success notification here
          // or redirect to the case details page
        }}
      />
    </Box>
  );
}
