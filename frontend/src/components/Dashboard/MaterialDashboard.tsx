import React from 'react';
import {
  <PERSON>rid,
  Card,
  CardContent,
  Typography,
  Box,
  Alert,
  Container,
} from '@mui/material';
import { Home as HomeIcon } from '@mui/icons-material';
import { useQueryClient } from '@tanstack/react-query';
import type { UserPublic } from '@/client';

// Import des composants Material-UI existants
import MaterialStatisticsCards from './MaterialStatisticsCards';
import MaterialRecentCases from './MaterialRecentCases';
import MaterialQuickActions from './MaterialQuickActions';
import MaterialActivityFeed from './MaterialActivityFeed';

export default function MaterialDashboard() {
  const queryClient = useQueryClient();
  const currentUser = queryClient.getQueryData<UserPublic>(["currentUser"]);

  const getGreeting = () => {
    const hour = new Date().getHours();
    if (hour < 12) return "Good morning";
    if (hour < 18) return "Good afternoon";
    return "Good evening";
  };

  const getUserRole = () => {
    if (currentUser?.is_superuser) return "Administrator";
    if (currentUser?.role) {
      return currentUser.role.charAt(0).toUpperCase() + currentUser.role.slice(1);
    }
    return "User";
  };

  return (
    <Container maxWidth="xl">
      <Grid container spacing={3}>
        {/* Welcome Section */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <HomeIcon sx={{ mr: 1, color: 'primary.main' }} />
                <Typography variant="h4" component="h1">
                  🏛️ {getGreeting()}, {currentUser?.full_name || currentUser?.email || 'User'}
                </Typography>
              </Box>
              <Typography variant="body1" color="text.secondary" paragraph>
                Welcome to <Typography component="span" sx={{ fontWeight: 600, color: 'primary.main' }}>TunLeg</Typography>, your professional legal management platform.
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {getUserRole()} • {new Date().toLocaleDateString('en-US', {
                  weekday: 'long',
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric'
                })}
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        {/* Success Alert */}
        <Grid item xs={12}>
          <Alert severity="success">
            🎉 Material-UI Dashboard with migrated components is working perfectly!
          </Alert>
        </Grid>

        {/* Statistics Cards */}
        <Grid item xs={12}>
          <Typography variant="h5" gutterBottom sx={{ mb: 2 }}>
            Overview
          </Typography>
          <MaterialStatisticsCards />
        </Grid>

        {/* Main Content Grid */}
        <Grid item xs={12} lg={6}>
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <MaterialRecentCases />
            </Grid>
            <Grid item xs={12}>
              <MaterialQuickActions />
            </Grid>
          </Grid>
        </Grid>

        <Grid item xs={12} lg={6}>
          <MaterialActivityFeed />
        </Grid>
      </Grid>
    </Container>
  );
}
