import type { BoxProps } from "@chakra-ui/react"
import { Box, InputGroup as ChakraInputGroup, InputLeftElement, InputRightElement } from "@chakra-ui/react"
import * as React from "react"

export interface InputGroupProps extends BoxProps {
  startElementProps?: any
  endElementProps?: any
  startElement?: React.ReactNode
  endElement?: React.ReactNode
  children: React.ReactElement
  startOffset?: string
  endOffset?: string
}

export const InputGroup = React.forwardRef<HTMLDivElement, InputGroupProps>(
  function InputGroup(props, ref) {
    const {
      startElement,
      startElementProps,
      endElement,
      endElementProps,
      children,
      startOffset = "6px",
      endOffset = "6px",
      ...rest
    } = props

    const child = React.Children.only(children)

    return (
      <ChakraInputGroup ref={ref} {...rest}>
        {startElement && (
          <InputLeftElement pointerEvents="none" {...startElementProps}>
            {startElement}
          </InputLeftElement>
        )}
        {child}
        {endElement && (
          <InputRightElement {...endElementProps}>
            {endElement}
          </InputRightElement>
        )}
      </ChakraInputGroup>
    )
  },
)
