import type { ButtonProps as ChakraButtonProps } from "@chakra-ui/react"
import {
  Button as <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Spinner,
} from "@chakra-ui/react"
import * as React from "react"

interface ButtonLoadingProps {
  loading?: boolean
  loadingText?: React.ReactNode
}

export interface ButtonProps extends ChakraButtonProps, ButtonLoadingProps {}

export const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  function Button(props, ref) {
    const { loading, disabled, loadingText, children, ...rest } = props
    return (
      <ChakraButton
        disabled={loading || disabled}
        ref={ref}
        {...rest}
        leftIcon={loading ? <Spinner size="sm" /> : undefined}
      >
        {loading && loadingText ? loadingText : children}
      </ChakraButton>
    )
  },
)
