import React, { Component, <PERSON><PERSON>rInfo, ReactNode } from "react"
import {
  Box,
  Flex,
  Text,
  VStack,
  HStack,
  Alert,
  AlertIcon,
  AlertTitle,
  AlertDescription,
} from "@chakra-ui/react"
import { <PERSON><PERSON>lertTriangle, FiRefreshCw, FiHome } from "react-icons/fi"
import { Button } from "./button"

interface Props {
  children: ReactNode
  fallback?: ReactNode
  onError?: (error: Error, errorInfo: ErrorInfo) => void
}

interface State {
  hasError: boolean
  error?: Error
}

export class ErrorBoundary extends Component<Props, State> {
  public state: State = {
    hasError: false
  }

  public static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error }
  }

  public componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('ErrorBoundary caught an error:', error, errorInfo)
    this.props.onError?.(error, errorInfo)
  }

  private handleRetry = () => {
    this.setState({ hasError: false, error: undefined })
  }

  private handleGoHome = () => {
    window.location.href = '/'
  }

  public render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback
      }

      return (
        <ErrorFallback
          error={this.state.error}
          onRetry={this.handleRetry}
          onGoHome={this.handleGoHome}
        />
      )
    }

    return this.props.children
  }
}

// Error Fallback Component
interface ErrorFallbackProps {
  error?: Error
  onRetry?: () => void
  onGoHome?: () => void
  title?: string
  description?: string
}

export const ErrorFallback: React.FC<ErrorFallbackProps> = ({
  error,
  onRetry,
  onGoHome,
  title = "Something went wrong",
  description = "An unexpected error occurred. Please try again or contact support if the problem persists."
}) => (
  <Flex
    direction="column"
    align="center"
    justify="center"
    minH="400px"
    p={8}
    textAlign="center"
  >
    <VStack gap={6} maxW="md">
      <Box color="red.500">
        <FiAlertTriangle size={48} />
      </Box>
      
      <VStack gap={3}>
        <Text fontSize="xl" fontWeight="bold" color="gray.800">
          {title}
        </Text>
        <Text color="gray.600" lineHeight="1.6">
          {description}
        </Text>
      </VStack>
      
      {error && process.env.NODE_ENV === 'development' && (
        <Box
          p={4}
          bg="gray.50"
          borderRadius="md"
          borderWidth="1px"
          w="full"
          textAlign="left"
        >
          <Text fontSize="sm" fontFamily="mono" color="red.600">
            {error.message}
          </Text>
        </Box>
      )}

      <HStack gap={3}>
        {onRetry && (
          <Button
            colorScheme="blue"
            onClick={onRetry}
          >
            <FiRefreshCw style={{ marginRight: '8px' }} />
            Try Again
          </Button>
        )}
        {onGoHome && (
          <Button
            variant="outline"
            onClick={onGoHome}
          >
            <FiHome style={{ marginRight: '8px' }} />
            Go Home
          </Button>
        )}
      </HStack>
    </VStack>
  </Flex>
)

// Network Error Component
export interface NetworkErrorProps {
  onRetry?: () => void
  message?: string
}

export const NetworkError: React.FC<NetworkErrorProps> = ({
  onRetry,
  message = "Unable to connect to the server. Please check your internet connection."
}) => (
  <Alert status="error" borderRadius="md">
    <AlertIcon />
    <Box flex="1">
      <AlertTitle>Connection Error</AlertTitle>
      <AlertDescription display="block" mt={1}>
        {message}
      </AlertDescription>
    </Box>
    {onRetry && (
      <Button
        size="sm"
        onClick={onRetry}
        ml={3}
      >
        <FiRefreshCw style={{ marginRight: '4px' }} />
        Retry
      </Button>
    )}
  </Alert>
)

// API Error Component
export interface ApiErrorProps {
  error: {
    status?: number
    message?: string
    detail?: string
  }
  onRetry?: () => void
}

export const ApiError: React.FC<ApiErrorProps> = ({ error, onRetry }) => {
  const getErrorMessage = () => {
    if (error.status === 401) {
      return "You are not authorized to access this resource. Please log in again."
    }
    if (error.status === 403) {
      return "You don't have permission to access this resource."
    }
    if (error.status === 404) {
      return "The requested resource was not found."
    }
    if (error.status === 500) {
      return "Internal server error. Please try again later."
    }
    return error.message || error.detail || "An unexpected error occurred."
  }

  return (
    <Alert status="error" borderRadius="md">
      <AlertIcon />
      <Box flex="1">
        <AlertTitle>
          Error {error.status && `(${error.status})`}
        </AlertTitle>
        <AlertDescription display="block" mt={1}>
          {getErrorMessage()}
        </AlertDescription>
      </Box>
      {onRetry && error.status !== 401 && error.status !== 403 && (
        <Button
          size="sm"
          onClick={onRetry}
          ml={3}
        >
          <FiRefreshCw style={{ marginRight: '4px' }} />
          Retry
        </Button>
      )}
    </Alert>
  )
}

// Empty State Component
export interface EmptyStateProps {
  title?: string
  description?: string
  action?: {
    label: string
    onClick: () => void
  }
  icon?: React.ReactNode
}

export const EmptyState: React.FC<EmptyStateProps> = ({
  title = "No data found",
  description = "There's nothing to display here yet.",
  action,
  icon
}) => (
  <Flex
    direction="column"
    align="center"
    justify="center"
    minH="300px"
    p={8}
    textAlign="center"
  >
    <VStack gap={4} maxW="sm">
      {icon && (
        <Box color="gray.400">
          {icon}
        </Box>
      )}
      
      <VStack gap={2}>
        <Text fontSize="lg" fontWeight="semibold" color="gray.700">
          {title}
        </Text>
        <Text color="gray.500" fontSize="sm">
          {description}
        </Text>
      </VStack>
      
      {action && (
        <Button onClick={action.onClick} colorScheme="blue">
          {action.label}
        </Button>
      )}
    </VStack>
  </Flex>
)

// Hook for error handling
export const useErrorHandler = () => {
  const [error, setError] = React.useState<Error | null>(null)

  const handleError = React.useCallback((error: Error) => {
    console.error('Error caught by useErrorHandler:', error)
    setError(error)
  }, [])

  const clearError = React.useCallback(() => {
    setError(null)
  }, [])

  return { error, handleError, clearError }
}

// HOC for error boundary
export function withErrorBoundary<P extends object>(
  Component: React.ComponentType<P>,
  errorFallback?: ReactNode
) {
  const WrappedComponent = (props: P) => (
    <ErrorBoundary fallback={errorFallback}>
      <Component {...props} />
    </ErrorBoundary>
  )

  WrappedComponent.displayName = `withErrorBoundary(${Component.displayName || Component.name})`
  
  return WrappedComponent
}
