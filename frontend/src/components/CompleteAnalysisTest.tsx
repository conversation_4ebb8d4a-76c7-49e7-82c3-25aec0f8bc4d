import React, { useState } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON>,
  Card,
  CardContent,
  Ty<PERSON><PERSON>,
  Alert,
  LinearProgress,
  Chip,
  Divider,
  Paper,
  Grid,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  FormControl,
  InputLabel,
  Select,
  MenuItem
} from '@mui/material';
import { 
  Upload as UploadIcon, 
  Psychology as PsychologyIcon,
  ExpandMore as ExpandMoreIcon,
  Visibility as VisibilityIcon
} from '@mui/icons-material';

interface CompleteAnalysisResult {
  success: boolean;
  filename: string;
  file_size: number;
  file_type: string;
  output_language: string;
  ocr_extraction: {
    extracted_text_length: number;
    extracted_text_preview: string;
    ocr_confidence: number;
    is_fallback: boolean;
  };
  ai_analysis: {
    document_type: string;
    confidence: number;
    key_entities: string[];
    suggested_actions: string[];
    extracted_info: any;
  };
  openai_enabled: boolean;
  error?: string;
}

export default function CompleteAnalysisTest() {
  const [file, setFile] = useState<File | null>(null);
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<CompleteAnalysisResult | null>(null);
  const [outputLanguage, setOutputLanguage] = useState<string>('auto');

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = event.target.files?.[0];
    if (selectedFile) {
      setFile(selectedFile);
      setResult(null);
    }
  };

  const handleAnalysis = async () => {
    if (!file) return;

    setLoading(true);
    try {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('output_language', outputLanguage);

      const response = await fetch('http://localhost:8000/api/v1/ai-analysis/complete-analysis', {
        method: 'POST',
        body: formData,
      });

      const data = await response.json();
      setResult(data);
    } catch (error) {
      console.error('Complete analysis failed:', error);
      setResult({
        success: false,
        filename: file.name,
        file_size: file.size,
        file_type: '',
        ocr_extraction: {
          extracted_text_length: 0,
          extracted_text_preview: '',
          ocr_confidence: 0,
          is_fallback: true
        },
        ai_analysis: {
          document_type: '',
          confidence: 0,
          key_entities: [],
          suggested_actions: [],
          extracted_info: {}
        },
        openai_enabled: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Box sx={{ maxWidth: 1000, mx: 'auto', p: 3 }}>
      <Typography variant="h4" gutterBottom>
        🤖 Test Analyse Complète (OCR + OpenAI)
      </Typography>
      
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            📤 Upload Document
          </Typography>
          
          <Box sx={{ mb: 2 }}>
            <input
              type="file"
              accept=".pdf,.jpg,.jpeg,.png,.txt"
              onChange={handleFileSelect}
              style={{ display: 'none' }}
              id="file-input"
            />
            <label htmlFor="file-input">
              <Button
                variant="outlined"
                component="span"
                startIcon={<UploadIcon />}
                sx={{ mr: 2 }}
              >
                Choisir un fichier
              </Button>
            </label>

            {file && (
              <Chip
                label={`${file.name} (${(file.size / 1024).toFixed(1)} KB)`}
                color="primary"
                variant="outlined"
              />
            )}
          </Box>

          <Box sx={{ mb: 2 }}>
            <FormControl fullWidth>
              <InputLabel>🌐 Langue de l'analyse IA</InputLabel>
              <Select
                value={outputLanguage}
                label="🌐 Langue de l'analyse IA"
                onChange={(e) => setOutputLanguage(e.target.value)}
              >
                <MenuItem value="auto">🤖 Automatique (Intelligent)</MenuItem>
                <MenuItem value="french">🇫🇷 Français uniquement</MenuItem>
                <MenuItem value="arabic">🇹🇳 العربية فقط (Arabe uniquement)</MenuItem>
                <MenuItem value="original">📄 Langue d'origine (Sans traduction)</MenuItem>
              </Select>
            </FormControl>
            <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
              {outputLanguage === 'auto' && '🤖 L\'IA choisira automatiquement la meilleure langue pour l\'avocat tunisien'}
              {outputLanguage === 'french' && '🇫🇷 Toute l\'analyse sera en français, même si le document est en arabe'}
              {outputLanguage === 'arabic' && '🇹🇳 كل التحليل سيكون باللغة العربية، حتى لو كانت الوثيقة بالفرنسية'}
              {outputLanguage === 'original' && '📄 L\'analyse conservera la langue du document original'}
            </Typography>
          </Box>

          <Button
            variant="contained"
            onClick={handleAnalysis}
            disabled={!file || loading}
            startIcon={<PsychologyIcon />}
            size="large"
          >
            {loading ? 'Analyse en cours...' : 'Analyser avec OCR + OpenAI'}
          </Button>

          {loading && (
            <Box sx={{ mt: 2 }}>
              <LinearProgress />
              <Typography variant="body2" sx={{ mt: 1, textAlign: 'center' }}>
                Extraction OCR puis analyse OpenAI...
              </Typography>
            </Box>
          )}
        </CardContent>
      </Card>

      {result && (
        <Grid container spacing={3}>
          {/* Status Overview */}
          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  📊 Résumé de l'analyse
                </Typography>
                <Box sx={{ mb: 2 }}>
                  <Chip 
                    label={result.success ? '✅ Succès' : '❌ Échec'} 
                    color={result.success ? 'success' : 'error'}
                    sx={{ mr: 1 }}
                  />
                  <Chip 
                    label={`OCR: ${(result.ocr_extraction.ocr_confidence * 100).toFixed(1)}%`}
                    color={result.ocr_extraction.ocr_confidence > 0.7 ? 'success' : 'warning'}
                    sx={{ mr: 1 }}
                  />
                  <Chip 
                    label={`AI: ${(result.ai_analysis.confidence * 100).toFixed(1)}%`}
                    color={result.ai_analysis.confidence > 0.7 ? 'success' : 'warning'}
                    sx={{ mr: 1 }}
                  />
                  <Chip
                    label={result.openai_enabled ? '🤖 OpenAI ON' : '🔄 Simulation'}
                    color={result.openai_enabled ? 'success' : 'warning'}
                    sx={{ mr: 1 }}
                  />
                  <Chip
                    label={`🌐 ${result.output_language === 'auto' ? 'Auto' :
                           result.output_language === 'french' ? 'Français' :
                           result.output_language === 'arabic' ? 'العربية' : 'Original'}`}
                    color="info"
                    variant="outlined"
                  />
                </Box>
              </CardContent>
            </Card>
          </Grid>

          {/* OCR Results */}
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  🔍 Extraction OCR
                </Typography>
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  📄 {result.filename} | 📊 {result.file_size} bytes | 📝 {result.ocr_extraction.extracted_text_length} caractères
                </Typography>
                
                <Accordion>
                  <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                    <Typography>Voir le texte extrait</Typography>
                  </AccordionSummary>
                  <AccordionDetails>
                    <Paper sx={{ p: 2, bgcolor: 'grey.50', maxHeight: 300, overflow: 'auto' }}>
                      <Typography 
                        variant="body2" 
                        component="pre" 
                        sx={{ 
                          whiteSpace: 'pre-wrap',
                          fontFamily: 'monospace',
                          fontSize: '0.75rem'
                        }}
                      >
                        {result.ocr_extraction.extracted_text_preview || 'Aucun texte extrait'}
                      </Typography>
                    </Paper>
                  </AccordionDetails>
                </Accordion>
              </CardContent>
            </Card>
          </Grid>

          {/* AI Analysis Results */}
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  🤖 Analyse OpenAI
                </Typography>
                
                <Box sx={{ mb: 2 }}>
                  <Typography variant="subtitle2">Type de document:</Typography>
                  <Typography variant="body2" sx={{ mb: 1 }}>{result.ai_analysis.document_type}</Typography>
                  
                  <Typography variant="subtitle2">Entités clés:</Typography>
                  <Box sx={{ mb: 1 }}>
                    {result.ai_analysis.key_entities.map((entity, index) => (
                      <Chip key={index} label={entity} size="small" sx={{ mr: 0.5, mb: 0.5 }} />
                    ))}
                  </Box>
                  
                  <Typography variant="subtitle2">Actions suggérées:</Typography>
                  <Box sx={{ mb: 1 }}>
                    {result.ai_analysis.suggested_actions.map((action, index) => (
                      <Chip key={index} label={action} size="small" variant="outlined" sx={{ mr: 0.5, mb: 0.5 }} />
                    ))}
                  </Box>
                </Box>

                <Accordion>
                  <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                    <Typography>Détails de l'analyse</Typography>
                  </AccordionSummary>
                  <AccordionDetails>
                    <Paper sx={{ p: 2, bgcolor: 'grey.50' }}>
                      <Typography 
                        variant="body2" 
                        component="pre" 
                        sx={{ 
                          whiteSpace: 'pre-wrap',
                          fontFamily: 'monospace',
                          fontSize: '0.75rem'
                        }}
                      >
                        {JSON.stringify(result.ai_analysis.extracted_info, null, 2)}
                      </Typography>
                    </Paper>
                  </AccordionDetails>
                </Accordion>
              </CardContent>
            </Card>
          </Grid>

          {result.error && (
            <Grid item xs={12}>
              <Alert severity="error">
                Erreur: {result.error}
              </Alert>
            </Grid>
          )}
        </Grid>
      )}
    </Box>
  );
}
