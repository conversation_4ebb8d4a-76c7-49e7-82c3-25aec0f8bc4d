import React, { useState } from 'react'
import {
  Box,
  <PERSON>ton,
  Card,
  CardBody,
  CardHeader,
  Heading,
  VStack,
  HStack,
  Text,
  Progress,
  Alert,
  Input,
  Select,
  Textarea,
} from '@chakra-ui/react'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { CaseDocumentsService } from '../../client/case-documents'
import useCustomToast from '../../hooks/useCustomToast'
import { Field } from '../ui/field'

interface DocumentAnalysisUploadProps {
  caseId?: string
  onAnalysisComplete?: (analysisId: string) => void
}

interface UploadProgress {
  stage: 'uploading' | 'analyzing' | 'complete' | 'error'
  progress: number
  message: string
}

export const DocumentAnalysisUpload: React.FC<DocumentAnalysisUploadProps> = ({
  caseId,
  onAnalysisComplete,
}) => {
  const [selectedFile, setSelectedFile] = useState<File | null>(null)
  const [uploadProgress, setUploadProgress] = useState<UploadProgress | null>(null)
  const [analysisType, setAnalysisType] = useState<'ai_analysis' | 'ocr_only'>('ai_analysis')
  const [description, setDescription] = useState('')
  const { showSuccessToast, showErrorToast } = useCustomToast()
  const queryClient = useQueryClient()

  // Upload and analyze mutation
  const uploadAndAnalyzeMutation = useMutation({
    mutationFn: async (file: File) => {
      setUploadProgress({
        stage: 'uploading',
        progress: 0,
        message: 'Uploading document...'
      })

      // First upload the document
      const formData = new FormData()
      formData.append('file', file)
      
      if (description) {
        formData.append('description', description)
      }

      // If we have a case ID, upload to that case
      let uploadResponse
      if (caseId) {
        uploadResponse = await CaseDocumentsService.uploadCaseDocument(
          caseId,
          formData
        )
      } else {
        // For now, we'll need a case ID. In the future, we could create a temporary upload
        throw new Error('Case ID is required for document upload')
      }

      setUploadProgress({
        stage: 'uploading',
        progress: 50,
        message: 'Document uploaded successfully'
      })

      setUploadProgress({
        stage: 'analyzing',
        progress: 60,
        message: 'Starting AI analysis...'
      })

      // Then trigger AI analysis
      const analysisResponse = await fetch(
        `/api/v1/ai-analysis/documents/${uploadResponse.id}/analyze?analysis_type=${analysisType}`,
        {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('access_token')}`,
            'Content-Type': 'application/json',
          },
        }
      )

      if (!analysisResponse.ok) {
        throw new Error('Analysis failed')
      }

      const analysis = await analysisResponse.json()

      setUploadProgress({
        stage: 'complete',
        progress: 100,
        message: 'Analysis completed successfully'
      })

      return {
        document: uploadResponse,
        analysis: analysis
      }
    },
    onSuccess: (data) => {
      toast({
        title: 'Success',
        description: 'Document uploaded and analyzed successfully',
        status: 'success',
        duration: 5000,
        isClosable: true,
      })

      if (onAnalysisComplete) {
        onAnalysisComplete(data.analysis.id)
      }

      // Reset form
      setSelectedFile(null)
      setDescription('')
      setUploadProgress(null)

      // Invalidate queries to refresh data
      queryClient.invalidateQueries({ queryKey: ['case-documents'] })
    },
    onError: (error: any) => {
      console.error('Upload/Analysis error:', error)
      
      setUploadProgress({
        stage: 'error',
        progress: 0,
        message: error.message || 'Upload or analysis failed'
      })

      toast({
        title: 'Error',
        description: error.message || 'Failed to upload and analyze document',
        status: 'error',
        duration: 5000,
        isClosable: true,
      })
    },
  })

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      // Validate file type
      const allowedTypes = ['application/pdf', 'image/jpeg', 'image/png', 'image/tiff']
      if (!allowedTypes.includes(file.type)) {
        toast({
          title: 'Invalid file type',
          description: 'Please select a PDF or image file (JPEG, PNG, TIFF)',
          status: 'error',
          duration: 5000,
          isClosable: true,
        })
        return
      }

      // Validate file size (50MB limit)
      const maxSize = 50 * 1024 * 1024 // 50MB
      if (file.size > maxSize) {
        toast({
          title: 'File too large',
          description: 'Please select a file smaller than 50MB',
          status: 'error',
          duration: 5000,
          isClosable: true,
        })
        return
      }

      setSelectedFile(file)
    }
  }

  const handleUpload = () => {
    if (!selectedFile) {
      toast({
        title: 'No file selected',
        description: 'Please select a file to upload',
        status: 'warning',
        duration: 3000,
        isClosable: true,
      })
      return
    }

    if (!caseId) {
      toast({
        title: 'No case selected',
        description: 'Please select a case to upload the document to',
        status: 'warning',
        duration: 3000,
        isClosable: true,
      })
      return
    }

    uploadAndAnalyzeMutation.mutate(selectedFile)
  }

  const resetUpload = () => {
    setSelectedFile(null)
    setUploadProgress(null)
    setDescription('')
  }

  return (
    <Card>
      <CardHeader>
        <Heading size="md">AI Document Analysis</Heading>
        <Text fontSize="sm" color="gray.600">
          Upload a document to extract legal information automatically
        </Text>
      </CardHeader>
      <CardBody>
        <VStack spacing={4} align="stretch">
          {/* File Upload */}
          <Field label="Select Document" helperText="Supported formats: PDF, JPEG, PNG, TIFF (max 50MB)">
            <Input
              type="file"
              accept=".pdf,.jpg,.jpeg,.png,.tiff"
              onChange={handleFileSelect}
              disabled={uploadProgress?.stage === 'uploading' || uploadProgress?.stage === 'analyzing'}
            />
          </Field>

          {/* Analysis Type */}
          <Field label="Analysis Type">
            <Select
              value={analysisType}
              onChange={(e) => setAnalysisType(e.target.value as 'ai_analysis' | 'ocr_only')}
              disabled={uploadProgress?.stage === 'uploading' || uploadProgress?.stage === 'analyzing'}
            >
              <option value="ai_analysis">Full AI Analysis (Extract legal information)</option>
              <option value="ocr_only">OCR Only (Extract text only)</option>
            </Select>
          </Field>

          {/* Description */}
          <Field label="Description (Optional)">
            <Textarea
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              placeholder="Brief description of the document..."
              disabled={uploadProgress?.stage === 'uploading' || uploadProgress?.stage === 'analyzing'}
            />
          </Field>

          {/* Selected File Info */}
          {selectedFile && (
            <Box p={3} bg="gray.50" borderRadius="md">
              <Text fontSize="sm" fontWeight="medium">Selected File:</Text>
              <Text fontSize="sm">{selectedFile.name}</Text>
              <Text fontSize="xs" color="gray.600">
                Size: {(selectedFile.size / 1024 / 1024).toFixed(2)} MB
              </Text>
            </Box>
          )}

          {/* Progress */}
          {uploadProgress && (
            <Box>
              <Text fontSize="sm" mb={2}>{uploadProgress.message}</Text>
              <Progress
                value={uploadProgress.progress}
                colorScheme={uploadProgress.stage === 'error' ? 'red' : 'blue'}
                size="sm"
              />
            </Box>
          )}

          {/* Error Alert */}
          {uploadProgress?.stage === 'error' && (
            <Alert status="error" borderRadius="md">
              <Box>
                <Text fontWeight="bold">Upload Failed!</Text>
                <Text fontSize="sm" mt={1}>{uploadProgress.message}</Text>
              </Box>
            </Alert>
          )}

          {/* Success Alert */}
          {uploadProgress?.stage === 'complete' && (
            <Alert status="success" borderRadius="md">
              <Box>
                <Text fontWeight="bold">Analysis Complete!</Text>
                <Text fontSize="sm" mt={1}>
                  Document has been uploaded and analyzed successfully.
                </Text>
              </Box>
            </Alert>
          )}

          {/* Action Buttons */}
          <HStack spacing={3}>
            <Button
              colorScheme="blue"
              onClick={handleUpload}
              isLoading={uploadProgress?.stage === 'uploading' || uploadProgress?.stage === 'analyzing'}
              loadingText={uploadProgress?.stage === 'uploading' ? 'Uploading...' : 'Analyzing...'}
              disabled={!selectedFile || !caseId}
              flex={1}
            >
              Upload & Analyze
            </Button>
            
            {(uploadProgress?.stage === 'complete' || uploadProgress?.stage === 'error') && (
              <Button
                variant="outline"
                onClick={resetUpload}
                flex={1}
              >
                Upload Another
              </Button>
            )}
          </HStack>
        </VStack>
      </CardBody>
    </Card>
  )
}
