import React, { useState } from 'react'
import {
  <PERSON>,
  <PERSON><PERSON>,
  Card,
  CardBody,
  CardHeader,
  Heading,
  VStack,
  <PERSON><PERSON>tack,
  Text,
  Alert,
  Badge,
  Progress,
} from '@chakra-ui/react'
import { useMutation } from '@tanstack/react-query'
import { DocumentAnalysisUpload } from './DocumentAnalysisUpload'
import { ExtractedInfoValidation } from './ExtractedInfoValidation'
import useCustomToast from '../../hooks/useCustomToast'

interface AIDocumentAnalysisWorkflowProps {
  caseId?: string
  onCaseCreated?: (caseId: string) => void
}

const steps = [
  { title: 'Upload', description: 'Upload document for analysis' },
  { title: 'Review', description: 'Review extracted information' },
  { title: 'Validate', description: 'Validate and correct information' },
  { title: 'Create Case', description: 'Create legal case' },
]

export const AIDocumentAnalysisWorkflow: React.FC<AIDocumentAnalysisWorkflowProps> = ({
  caseId,
  onCaseCreated,
}) => {
  const [analysisId, setAnalysisId] = useState<string | null>(null)
  const [validationId, setValidationId] = useState<string | null>(null)
  const [createdCaseId, setCreatedCaseId] = useState<string | null>(null)
  const [activeStep, setActiveStep] = useState(0)
  const { showSuccessToast, showErrorToast } = useCustomToast()

  // Create case from validation mutation
  const createCaseMutation = useMutation({
    mutationFn: async (validationId: string) => {
      const response = await fetch(
        `/api/v1/ai-analysis/validations/${validationId}/create-case`,
        {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('access_token')}`,
            'Content-Type': 'application/json',
          },
        }
      )
      if (!response.ok) {
        throw new Error('Failed to create case')
      }
      return response.json()
    },
    onSuccess: (data) => {
      setCreatedCaseId(data.id)
      setActiveStep(3)
      
      toast({
        title: 'Case created successfully',
        description: `Legal case "${data.title}" has been created`,
        status: 'success',
        duration: 5000,
        isClosable: true,
      })
      
      if (onCaseCreated) {
        onCaseCreated(data.id)
      }
    },
    onError: (error: any) => {
      toast({
        title: 'Failed to create case',
        description: error.message || 'An error occurred while creating the case',
        status: 'error',
        duration: 5000,
        isClosable: true,
      })
    },
  })

  const handleAnalysisComplete = (newAnalysisId: string) => {
    setAnalysisId(newAnalysisId)
    setActiveStep(1)
  }

  const handleValidationComplete = (newValidationId: string) => {
    setValidationId(newValidationId)
    setActiveStep(2)
  }

  const handleCreateCase = () => {
    if (validationId) {
      createCaseMutation.mutate(validationId)
    }
  }

  const resetWorkflow = () => {
    setAnalysisId(null)
    setValidationId(null)
    setCreatedCaseId(null)
    setActiveStep(0)
  }

  return (
    <Box maxW="6xl" mx="auto" p={6}>
      <Card mb={6}>
        <CardHeader>
          <Heading size="lg" textAlign="center">
            AI Document Analysis Workflow
          </Heading>
          <Text textAlign="center" color="gray.600" mt={2}>
            Upload a document, review extracted information, and create a legal case
          </Text>
        </CardHeader>
        <CardBody>
          {/* Progress Bar */}
          <Box mb={6}>
            <Progress
              value={(activeStep / (steps.length - 1)) * 100}
              colorScheme="blue"
              size="sm"
              borderRadius="md"
            />
            <Text fontSize="sm" color="gray.600" mt={2} textAlign="center">
              Step {activeStep + 1} of {steps.length}: {steps[activeStep]?.title}
            </Text>
          </Box>

          {/* Step Navigation */}
          <HStack spacing={4} justify="center" wrap="wrap">
            {steps.map((step, index) => (
              <VStack key={index} spacing={1} align="center">
                <Badge
                  colorScheme={
                    index < activeStep ? 'green' :
                    index === activeStep ? 'blue' : 'gray'
                  }
                  variant={index <= activeStep ? 'solid' : 'outline'}
                  borderRadius="full"
                  px={3}
                  py={1}
                >
                  {index + 1}
                </Badge>
                <Text fontSize="xs" textAlign="center" maxW="20">
                  {step.title}
                </Text>
              </VStack>
            ))}
          </HStack>
        </CardBody>
      </Card>

      {/* Step Content */}
      <VStack spacing={6} align="stretch">
        {/* Step 1: Upload Document */}
        {activeStep === 0 && (
          <DocumentAnalysisUpload
            caseId={caseId}
            onAnalysisComplete={handleAnalysisComplete}
          />
        )}

        {/* Step 2: Review Extracted Information */}
        {activeStep === 1 && analysisId && (
          <Card>
            <CardHeader>
              <Heading size="md">Review Extracted Information</Heading>
              <Text fontSize="sm" color="gray.600">
                Review the information extracted by AI from your document
              </Text>
            </CardHeader>
            <CardBody>
              <VStack spacing={4}>
                <Alert status="info" borderRadius="md">
                  <Text>Please review the extracted information carefully before proceeding to validation.</Text>
                </Alert>
                
                <HStack spacing={3} w="full">
                  <Button
                    variant="outline"
                    onClick={() => setActiveStep(0)}
                    flex={1}
                  >
                    Back to Upload
                  </Button>
                  <Button
                    colorScheme="blue"
                    onClick={() => setActiveStep(2)}
                    flex={1}
                  >
                    Proceed to Validation
                  </Button>
                </HStack>
              </VStack>
            </CardBody>
          </Card>
        )}

        {/* Step 3: Validate Information */}
        {activeStep === 2 && analysisId && (
          <ExtractedInfoValidation
            analysisId={analysisId}
            onValidationComplete={handleValidationComplete}
          />
        )}

        {/* Step 4: Create Case */}
        {activeStep === 3 && validationId && !createdCaseId && (
          <Card>
            <CardHeader>
              <Heading size="md">Create Legal Case</Heading>
              <Text fontSize="sm" color="gray.600">
                Ready to create a legal case from the validated information
              </Text>
            </CardHeader>
            <CardBody>
              <VStack spacing={4}>
                <Alert status="success" borderRadius="md">
                  <Text>Information has been validated and is ready for case creation.</Text>
                </Alert>
                
                <HStack spacing={3} w="full">
                  <Button
                    variant="outline"
                    onClick={() => setActiveStep(2)}
                    flex={1}
                  >
                    Back to Validation
                  </Button>
                  <Button
                    colorScheme="green"
                    onClick={handleCreateCase}
                    isLoading={createCaseMutation.isPending}
                    loadingText="Creating Case..."
                    flex={1}
                  >
                    Create Legal Case
                  </Button>
                </HStack>
              </VStack>
            </CardBody>
          </Card>
        )}

        {/* Step 4: Case Created */}
        {activeStep === 3 && createdCaseId && (
          <Card>
            <CardHeader>
              <Heading size="md">Case Created Successfully!</Heading>
            </CardHeader>
            <CardBody>
              <VStack spacing={4}>
                <Alert status="success" borderRadius="md">
                  <Text>Your legal case has been created successfully from the AI-analyzed document.</Text>
                </Alert>
                
                <Text>
                  Case ID: <strong>{createdCaseId}</strong>
                </Text>
                
                <HStack spacing={3} w="full">
                  <Button
                    colorScheme="blue"
                    onClick={() => window.location.href = `/legal-cases/${createdCaseId}`}
                    flex={1}
                  >
                    View Case
                  </Button>
                  <Button
                    variant="outline"
                    onClick={resetWorkflow}
                    flex={1}
                  >
                    Analyze Another Document
                  </Button>
                </HStack>
              </VStack>
            </CardBody>
          </Card>
        )}
      </VStack>

      {/* Debug Info (remove in production) */}
      {process.env.NODE_ENV === 'development' && (
        <Card mt={6} bg="gray.50">
          <CardHeader>
            <Heading size="sm">Debug Info</Heading>
          </CardHeader>
          <CardBody>
            <VStack align="start" spacing={2}>
              <Text fontSize="sm">Active Step: {activeStep}</Text>
              <Text fontSize="sm">Analysis ID: {analysisId || 'None'}</Text>
              <Text fontSize="sm">Validation ID: {validationId || 'None'}</Text>
              <Text fontSize="sm">Created Case ID: {createdCaseId || 'None'}</Text>
              <Text fontSize="sm">Case ID (prop): {caseId || 'None'}</Text>
            </VStack>
          </CardBody>
        </Card>
      )}
    </Box>
  )
}
