import React, { useState } from 'react'
import {
  Box,
  Button,
  Card,
  CardBody,
  CardHeader,
  Heading,
  VStack,
  HStack,
  Text,
  Badge,
  Alert,
  IconButton,
  Tag,
  TagLabel,
  Wrap,
  WrapItem,
  Grid,
  GridItem,
  Switch,
} from '@chakra-ui/react'
import { FiChevronDown, FiChevronUp } from 'react-icons/fi'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import useCustomToast from '../../hooks/useCustomToast'

interface CaseUpdateSuggestion {
  case_id: string
  analysis_id: string
  current_case: {
    title: string
    client_name: string
    case_type: string
    description: string
  }
  suggested_updates: Record<string, any>
  new_information: Record<string, any>
  confidence_level: 'low' | 'medium' | 'high'
}

interface CaseUpdateSuggestionsProps {
  caseId: string
  analysisId: string
  onUpdateComplete?: () => void
}

export const CaseUpdateSuggestions: React.FC<CaseUpdateSuggestionsProps> = ({
  caseId,
  analysisId,
  onUpdateComplete,
}) => {
  const [selectedUpdates, setSelectedUpdates] = useState<Record<string, boolean>>({})
  const [selectedNewInfo, setSelectedNewInfo] = useState<Record<string, boolean>>({})
  const [expandedSections, setExpandedSections] = useState<Record<string, boolean>>({})
  const { showSuccessToast, showErrorToast } = useCustomToast()
  const queryClient = useQueryClient()

  // Fetch suggestions
  const { data: suggestions, isLoading, error } = useQuery({
    queryKey: ['case-update-suggestions', caseId, analysisId],
    queryFn: async () => {
      const response = await fetch(
        `/api/v1/ai-analysis/cases/${caseId}/analysis/${analysisId}/suggestions`,
        {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('access_token')}`,
          },
        }
      )
      if (!response.ok) {
        throw new Error('Failed to fetch suggestions')
      }
      return response.json() as CaseUpdateSuggestion
    },
    enabled: !!caseId && !!analysisId,
  })

  // Apply updates mutation
  const applyUpdatesMutation = useMutation({
    mutationFn: async () => {
      // Create validation with selected updates
      const validationData = {
        validation_status: 'approved',
        should_create_case: false, // We're updating, not creating
        validated_data: {
          ...Object.keys(selectedUpdates)
            .filter(key => selectedUpdates[key])
            .reduce((acc, key) => {
              const update = suggestions?.suggested_updates[key]
              if (update) {
                acc[key] = update.suggested || update.suggested_addition
              }
              return acc
            }, {} as Record<string, any>),
          ...Object.keys(selectedNewInfo)
            .filter(key => selectedNewInfo[key])
            .reduce((acc, key) => {
              acc[key] = suggestions?.new_information[key]
              return acc
            }, {} as Record<string, any>),
        },
        validation_notes: 'Auto-generated validation for case update from new document analysis',
      }

      // First create validation
      const validationResponse = await fetch(
        `/api/v1/ai-analysis/extracted-info/${analysisId}/validate`,
        {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('access_token')}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(validationData),
        }
      )

      if (!validationResponse.ok) {
        throw new Error('Failed to create validation')
      }

      const validation = await validationResponse.json()

      // Then apply updates to case
      const updateResponse = await fetch(
        `/api/v1/ai-analysis/cases/${caseId}/update-from-validation/${validation.id}`,
        {
          method: 'PUT',
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('access_token')}`,
            'Content-Type': 'application/json',
          },
        }
      )

      if (!updateResponse.ok) {
        throw new Error('Failed to update case')
      }

      return updateResponse.json()
    },
    onSuccess: () => {
      toast({
        title: 'Case updated successfully',
        description: 'The case has been updated with the selected information',
        status: 'success',
        duration: 5000,
        isClosable: true,
      })

      if (onUpdateComplete) {
        onUpdateComplete()
      }

      queryClient.invalidateQueries({ queryKey: ['legal-cases'] })
    },
    onError: (error: any) => {
      toast({
        title: 'Update failed',
        description: error.message || 'Failed to update case',
        status: 'error',
        duration: 5000,
        isClosable: true,
      })
    },
  })

  const getConfidenceBadgeColor = (confidence: string) => {
    switch (confidence) {
      case 'high': return 'green'
      case 'medium': return 'yellow'
      case 'low': return 'red'
      default: return 'gray'
    }
  }

  const handleUpdateToggle = (key: string, checked: boolean) => {
    setSelectedUpdates(prev => ({ ...prev, [key]: checked }))
  }

  const handleNewInfoToggle = (key: string, checked: boolean) => {
    setSelectedNewInfo(prev => ({ ...prev, [key]: checked }))
  }

  const toggleSection = (key: string) => {
    setExpandedSections(prev => ({
      ...prev,
      [key]: !prev[key]
    }))
  }

  const hasSelectedItems = Object.values(selectedUpdates).some(Boolean) ||
                          Object.values(selectedNewInfo).some(Boolean)

  if (isLoading) {
    return (
      <Card>
        <CardBody>
          <Text>Loading update suggestions...</Text>
        </CardBody>
      </Card>
    )
  }

  if (error || !suggestions) {
    return (
      <Card>
        <CardBody>
          <Alert status="error">
            <AlertIcon />
            Failed to load update suggestions
          </Alert>
        </CardBody>
      </Card>
    )
  }

  const hasUpdates = Object.keys(suggestions.suggested_updates).length > 0
  const hasNewInfo = Object.keys(suggestions.new_information).length > 0

  if (!hasUpdates && !hasNewInfo) {
    return (
      <Card>
        <CardBody>
          <Alert status="info">
            <AlertIcon />
            <Box>
              <AlertTitle>No updates suggested</AlertTitle>
              <AlertDescription>
                The new document analysis doesn't suggest any changes to the existing case.
              </AlertDescription>
            </Box>
          </Alert>
        </CardBody>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <HStack justify="space-between">
          <Heading size="md">Case Update Suggestions</Heading>
          <Badge colorScheme={getConfidenceBadgeColor(suggestions.confidence_level)}>
            {suggestions.confidence_level} confidence
          </Badge>
        </HStack>
        <Text fontSize="sm" color="gray.600">
          Based on the new document analysis, here are suggested updates to the case
        </Text>
      </CardHeader>
      <CardBody>
        <VStack spacing={6} align="stretch">
          {/* Current Case Info */}
          <Box>
            <Heading size="sm" mb={3}>Current Case Information</Heading>
            <Grid templateColumns="repeat(2, 1fr)" gap={4}>
              <GridItem>
                <Text fontSize="sm" fontWeight="medium">Title:</Text>
                <Text fontSize="sm" color="gray.600">{suggestions.current_case.title}</Text>
              </GridItem>
              <GridItem>
                <Text fontSize="sm" fontWeight="medium">Client:</Text>
                <Text fontSize="sm" color="gray.600">{suggestions.current_case.client_name}</Text>
              </GridItem>
              <GridItem>
                <Text fontSize="sm" fontWeight="medium">Type:</Text>
                <Text fontSize="sm" color="gray.600">{suggestions.current_case.case_type}</Text>
              </GridItem>
            </Grid>
          </Box>

          <Divider />

          {/* Suggested Updates */}
          {hasUpdates && (
            <Box>
              <Heading size="sm" mb={3}>Suggested Updates</Heading>
              <VStack spacing={3} align="stretch">
                {Object.entries(suggestions.suggested_updates).map(([key, update]) => (
                  <Box key={key} borderWidth="1px" borderRadius="md" overflow="hidden">
                    <HStack
                      p={3}
                      bg="gray.50"
                      cursor="pointer"
                      onClick={() => toggleSection(key)}
                      _hover={{ bg: "gray.100" }}
                    >
                      <HStack flex="1" spacing={2}>
                        <Switch
                          isChecked={selectedUpdates[key] || false}
                          onChange={(e) => handleUpdateToggle(key, e.target.checked)}
                          onClick={(e) => e.stopPropagation()}
                        />
                        <Text fontSize="sm" fontWeight="medium">
                          Update {key.replace('_', ' ')}
                        </Text>
                      </HStack>
                      {update.confidence && (
                        <Badge size="sm" colorScheme={getConfidenceBadgeColor(update.confidence)}>
                          {update.confidence}
                        </Badge>
                      )}
                      <IconButton
                        aria-label="Toggle details"
                        icon={expandedSections[key] ? <FiChevronUp /> : <FiChevronDown />}
                        size="sm"
                        variant="ghost"
                        onClick={(e) => {
                          e.stopPropagation()
                          toggleSection(key)
                        }}
                      />
                    </HStack>
                    {expandedSections[key] && (
                      <Box p={4} bg="white" borderTop="1px solid" borderColor="gray.200">
                        <VStack align="start" spacing={2}>
                          <Box>
                            <Text fontSize="sm" fontWeight="medium">Current:</Text>
                            <Text fontSize="sm" color="gray.600">{update.current || 'Not set'}</Text>
                          </Box>
                          <Box>
                            <Text fontSize="sm" fontWeight="medium">Suggested:</Text>
                            <Text fontSize="sm" color="blue.600">
                              {update.suggested || update.suggested_addition}
                            </Text>
                          </Box>
                        </VStack>
                      </Box>
                    )}
                  </Box>
                ))}
              </VStack>
            </Box>
          )}

          {/* New Information */}
          {hasNewInfo && (
            <Box>
              <Heading size="sm" mb={3}>New Information to Add</Heading>
              <VStack spacing={4} align="stretch">
                {Object.entries(suggestions.new_information).map(([key, value]) => (
                  <Box key={key} p={3} bg="blue.50" borderRadius="md">
                    <HStack spacing={2} mb={2}>
                      <Switch
                        isChecked={selectedNewInfo[key] || false}
                        onChange={(e) => handleNewInfoToggle(key, e.target.checked)}
                      />
                      <Text fontSize="sm" fontWeight="medium">
                        Add {key.replace('_', ' ')}
                      </Text>
                    </HStack>
                    
                    {Array.isArray(value) ? (
                      <Wrap>
                        {value.map((item, index) => (
                          <WrapItem key={index}>
                            <Tag size="sm" colorScheme="blue">
                              <TagLabel>{item}</TagLabel>
                            </Tag>
                          </WrapItem>
                        ))}
                      </Wrap>
                    ) : typeof value === 'object' ? (
                      <VStack align="start" spacing={1}>
                        {Object.entries(value).map(([subKey, subValue]) => (
                          <Text key={subKey} fontSize="sm">
                            <strong>{subKey}:</strong> {String(subValue)}
                          </Text>
                        ))}
                      </VStack>
                    ) : (
                      <Text fontSize="sm">{String(value)}</Text>
                    )}
                  </Box>
                ))}
              </VStack>
            </Box>
          )}

          {/* Action Buttons */}
          <HStack spacing={3}>
            <Button
              colorScheme="blue"
              onClick={() => applyUpdatesMutation.mutate()}
              isLoading={applyUpdatesMutation.isPending}
              loadingText="Applying Updates..."
              disabled={!hasSelectedItems}
              flex={1}
            >
              Apply Selected Updates
            </Button>
            <Button
              variant="outline"
              onClick={() => {
                setSelectedUpdates({})
                setSelectedNewInfo({})
              }}
              flex={1}
            >
              Clear Selection
            </Button>
          </HStack>

          {!hasSelectedItems && (
            <Alert status="info" size="sm">
              <AlertIcon />
              Select the updates you want to apply to the case
            </Alert>
          )}
        </VStack>
      </CardBody>
    </Card>
  )
}
