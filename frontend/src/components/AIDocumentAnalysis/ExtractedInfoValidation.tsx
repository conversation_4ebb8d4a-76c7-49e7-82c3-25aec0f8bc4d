import React, { useState, useEffect } from 'react'
import {
  <PERSON>,
  <PERSON><PERSON>,
  Card,
  CardBody,
  CardHeader,
  Heading,
  VStack,
  HStack,
  Text,
  Badge,
  FormControl,
  FormLabel,
  Input,
  Textarea,
  Select,
  Switch,
  Divider,
  Alert,
  AlertIcon,
  useToast,
  Grid,
  GridItem,
  Tag,
  TagLabel,
  TagCloseButton,
  Wrap,
  WrapItem,
} from '@chakra-ui/react'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'

interface ExtractedInfo {
  id: string
  analysis_id: string
  plaintiff?: string
  defendant?: string
  witnesses?: string[]
  lawyers?: string[]
  suggested_case_type?: string
  case_type_confidence?: string
  document_summary?: string
  key_facts?: string[]
  important_dates?: Record<string, string>
  amounts?: Record<string, number>
  legal_references?: string[]
  additional_data?: Record<string, any>
  created_at: string
}

interface ValidationData {
  validation_status: 'pending' | 'approved' | 'rejected' | 'modified'
  validated_data?: Record<string, any>
  validation_notes?: string
  should_create_case: boolean
  suggested_case_title?: string
  suggested_client_name?: string
}

interface ExtractedInfoValidationProps {
  analysisId: string
  onValidationComplete?: (validationId: string) => void
}

export const ExtractedInfoValidation: React.FC<ExtractedInfoValidationProps> = ({
  analysisId,
  onValidationComplete,
}) => {
  const [validationData, setValidationData] = useState<ValidationData>({
    validation_status: 'pending',
    should_create_case: false,
    validated_data: {},
  })
  const [isEditing, setIsEditing] = useState(false)
  const toast = useToast()
  const queryClient = useQueryClient()

  // Fetch extracted information
  const { data: extractedInfo, isLoading, error } = useQuery({
    queryKey: ['extracted-info', analysisId],
    queryFn: async () => {
      const response = await fetch(
        `/api/v1/ai-analysis/analysis/${analysisId}/extracted-info`,
        {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('access_token')}`,
          },
        }
      )
      if (!response.ok) {
        throw new Error('Failed to fetch extracted information')
      }
      return response.json() as ExtractedInfo
    },
    enabled: !!analysisId,
  })

  // Initialize validation data when extracted info is loaded
  useEffect(() => {
    if (extractedInfo) {
      setValidationData(prev => ({
        ...prev,
        suggested_case_title: `Case - ${extractedInfo.suggested_case_type || 'Legal Matter'}`,
        suggested_client_name: extractedInfo.plaintiff || '',
        validated_data: {
          title: `Case - ${extractedInfo.suggested_case_type || 'Legal Matter'}`,
          client_name: extractedInfo.plaintiff || '',
          case_type: extractedInfo.suggested_case_type || 'other',
          description: extractedInfo.document_summary || '',
          plaintiff: extractedInfo.plaintiff,
          defendant: extractedInfo.defendant,
          witnesses: extractedInfo.witnesses,
          lawyers: extractedInfo.lawyers,
          key_facts: extractedInfo.key_facts,
          important_dates: extractedInfo.important_dates,
          amounts: extractedInfo.amounts,
          legal_references: extractedInfo.legal_references,
        },
      }))
    }
  }, [extractedInfo])

  // Create validation mutation
  const createValidationMutation = useMutation({
    mutationFn: async (data: ValidationData) => {
      const response = await fetch(
        `/api/v1/ai-analysis/extracted-info/${extractedInfo?.id}/validate`,
        {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('access_token')}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(data),
        }
      )
      if (!response.ok) {
        throw new Error('Failed to create validation')
      }
      return response.json()
    },
    onSuccess: (data) => {
      toast({
        title: 'Validation saved',
        description: 'Information has been validated successfully',
        status: 'success',
        duration: 3000,
        isClosable: true,
      })
      
      if (onValidationComplete) {
        onValidationComplete(data.id)
      }
      
      queryClient.invalidateQueries({ queryKey: ['validations'] })
    },
    onError: (error: any) => {
      toast({
        title: 'Validation failed',
        description: error.message || 'Failed to save validation',
        status: 'error',
        duration: 5000,
        isClosable: true,
      })
    },
  })

  const handleValidationSubmit = () => {
    createValidationMutation.mutate(validationData)
  }

  const updateValidatedData = (field: string, value: any) => {
    setValidationData(prev => ({
      ...prev,
      validated_data: {
        ...prev.validated_data,
        [field]: value,
      },
    }))
  }

  const getConfidenceBadgeColor = (confidence?: string) => {
    switch (confidence) {
      case 'very_high': return 'green'
      case 'high': return 'blue'
      case 'medium': return 'yellow'
      case 'low': return 'red'
      default: return 'gray'
    }
  }

  if (isLoading) {
    return (
      <Card>
        <CardBody>
          <Text>Loading extracted information...</Text>
        </CardBody>
      </Card>
    )
  }

  if (error || !extractedInfo) {
    return (
      <Card>
        <CardBody>
          <Alert status="error">
            <AlertIcon />
            Failed to load extracted information
          </Alert>
        </CardBody>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <HStack justify="space-between">
          <Heading size="md">Validate Extracted Information</Heading>
          <HStack>
            <Badge colorScheme={getConfidenceBadgeColor(extractedInfo.case_type_confidence)}>
              {extractedInfo.case_type_confidence || 'unknown'} confidence
            </Badge>
            <Button
              size="sm"
              variant="outline"
              onClick={() => setIsEditing(!isEditing)}
            >
              {isEditing ? 'View Mode' : 'Edit Mode'}
            </Button>
          </HStack>
        </HStack>
      </CardHeader>
      <CardBody>
        <VStack spacing={6} align="stretch">
          {/* Case Information */}
          <Box>
            <Heading size="sm" mb={3}>Case Information</Heading>
            <Grid templateColumns="repeat(2, 1fr)" gap={4}>
              <GridItem>
                <FormControl>
                  <FormLabel>Case Title</FormLabel>
                  {isEditing ? (
                    <Input
                      value={validationData.suggested_case_title || ''}
                      onChange={(e) => setValidationData(prev => ({
                        ...prev,
                        suggested_case_title: e.target.value
                      }))}
                    />
                  ) : (
                    <Text p={2} bg="gray.50" borderRadius="md">
                      {validationData.suggested_case_title || 'Not specified'}
                    </Text>
                  )}
                </FormControl>
              </GridItem>
              
              <GridItem>
                <FormControl>
                  <FormLabel>Client Name</FormLabel>
                  {isEditing ? (
                    <Input
                      value={validationData.suggested_client_name || ''}
                      onChange={(e) => setValidationData(prev => ({
                        ...prev,
                        suggested_client_name: e.target.value
                      }))}
                    />
                  ) : (
                    <Text p={2} bg="gray.50" borderRadius="md">
                      {validationData.suggested_client_name || 'Not specified'}
                    </Text>
                  )}
                </FormControl>
              </GridItem>

              <GridItem>
                <FormControl>
                  <FormLabel>Case Type</FormLabel>
                  {isEditing ? (
                    <Select
                      value={validationData.validated_data?.case_type || ''}
                      onChange={(e) => updateValidatedData('case_type', e.target.value)}
                    >
                      <option value="civil">Civil</option>
                      <option value="criminal">Criminal</option>
                      <option value="family">Family</option>
                      <option value="corporate">Corporate</option>
                      <option value="immigration">Immigration</option>
                      <option value="real_estate">Real Estate</option>
                      <option value="intellectual_property">Intellectual Property</option>
                      <option value="employment">Employment</option>
                      <option value="other">Other</option>
                    </Select>
                  ) : (
                    <Text p={2} bg="gray.50" borderRadius="md">
                      {extractedInfo.suggested_case_type || 'Not specified'}
                    </Text>
                  )}
                </FormControl>
              </GridItem>
            </Grid>
          </Box>

          <Divider />

          {/* Parties */}
          <Box>
            <Heading size="sm" mb={3}>Parties Involved</Heading>
            <Grid templateColumns="repeat(2, 1fr)" gap={4}>
              <GridItem>
                <FormControl>
                  <FormLabel>Plaintiff/Demandeur</FormLabel>
                  <Text p={2} bg="gray.50" borderRadius="md">
                    {extractedInfo.plaintiff || 'Not identified'}
                  </Text>
                </FormControl>
              </GridItem>
              
              <GridItem>
                <FormControl>
                  <FormLabel>Defendant/Défendeur</FormLabel>
                  <Text p={2} bg="gray.50" borderRadius="md">
                    {extractedInfo.defendant || 'Not identified'}
                  </Text>
                </FormControl>
              </GridItem>
            </Grid>

            {extractedInfo.witnesses && extractedInfo.witnesses.length > 0 && (
              <Box mt={4}>
                <FormLabel>Witnesses/Témoins</FormLabel>
                <Wrap>
                  {extractedInfo.witnesses.map((witness, index) => (
                    <WrapItem key={index}>
                      <Tag size="md" colorScheme="blue">
                        <TagLabel>{witness}</TagLabel>
                      </Tag>
                    </WrapItem>
                  ))}
                </Wrap>
              </Box>
            )}

            {extractedInfo.lawyers && extractedInfo.lawyers.length > 0 && (
              <Box mt={4}>
                <FormLabel>Lawyers/Avocats</FormLabel>
                <Wrap>
                  {extractedInfo.lawyers.map((lawyer, index) => (
                    <WrapItem key={index}>
                      <Tag size="md" colorScheme="purple">
                        <TagLabel>{lawyer}</TagLabel>
                      </Tag>
                    </WrapItem>
                  ))}
                </Wrap>
              </Box>
            )}
          </Box>

          <Divider />

          {/* Document Summary */}
          {extractedInfo.document_summary && (
            <Box>
              <Heading size="sm" mb={3}>Document Summary</Heading>
              <Text p={3} bg="gray.50" borderRadius="md" whiteSpace="pre-wrap">
                {extractedInfo.document_summary}
              </Text>
            </Box>
          )}

          {/* Key Facts */}
          {extractedInfo.key_facts && extractedInfo.key_facts.length > 0 && (
            <Box>
              <Heading size="sm" mb={3}>Key Facts</Heading>
              <VStack align="stretch" spacing={2}>
                {extractedInfo.key_facts.map((fact, index) => (
                  <Text key={index} p={2} bg="blue.50" borderRadius="md" fontSize="sm">
                    • {fact}
                  </Text>
                ))}
              </VStack>
            </Box>
          )}

          <Divider />

          {/* Validation Controls */}
          <Box>
            <Heading size="sm" mb={3}>Validation</Heading>
            <VStack spacing={4} align="stretch">
              <FormControl display="flex" alignItems="center">
                <FormLabel htmlFor="create-case" mb="0">
                  Create legal case from this information
                </FormLabel>
                <Switch
                  id="create-case"
                  isChecked={validationData.should_create_case}
                  onChange={(e) => setValidationData(prev => ({
                    ...prev,
                    should_create_case: e.target.checked
                  }))}
                />
              </FormControl>

              <FormControl>
                <FormLabel>Validation Status</FormLabel>
                <Select
                  value={validationData.validation_status}
                  onChange={(e) => setValidationData(prev => ({
                    ...prev,
                    validation_status: e.target.value as any
                  }))}
                >
                  <option value="pending">Pending Review</option>
                  <option value="approved">Approved</option>
                  <option value="modified">Modified</option>
                  <option value="rejected">Rejected</option>
                </Select>
              </FormControl>

              <FormControl>
                <FormLabel>Validation Notes</FormLabel>
                <Textarea
                  value={validationData.validation_notes || ''}
                  onChange={(e) => setValidationData(prev => ({
                    ...prev,
                    validation_notes: e.target.value
                  }))}
                  placeholder="Add notes about the validation..."
                />
              </FormControl>

              <Button
                colorScheme="blue"
                onClick={handleValidationSubmit}
                isLoading={createValidationMutation.isPending}
                loadingText="Saving..."
              >
                Save Validation
              </Button>
            </VStack>
          </Box>
        </VStack>
      </CardBody>
    </Card>
  )
}
