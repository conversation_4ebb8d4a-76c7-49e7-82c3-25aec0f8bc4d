import React, { useState } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  CardContent,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  LinearProgress,
  Chip,
  Divider,
  Paper
} from '@mui/material';
import { Upload as UploadIcon, Visibility as VisibilityIcon } from '@mui/icons-material';

interface OCRResult {
  success: boolean;
  filename: string;
  file_size: number;
  file_type: string;
  extracted_text_length: number;
  extracted_text: string;
  ocr_confidence: number;
  is_fallback: boolean;
  error?: string;
}

export default function OCRTest() {
  const [file, setFile] = useState<File | null>(null);
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<OCRResult | null>(null);

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = event.target.files?.[0];
    if (selectedFile) {
      setFile(selectedFile);
      setResult(null);
    }
  };

  const handleUpload = async () => {
    if (!file) return;

    setLoading(true);
    try {
      const formData = new FormData();
      formData.append('file', file);

      const response = await fetch('http://localhost:8000/api/v1/ai-analysis/test-ocr', {
        method: 'POST',
        body: formData,
      });

      const data = await response.json();
      setResult(data);
    } catch (error) {
      console.error('OCR test failed:', error);
      setResult({
        success: false,
        filename: file.name,
        file_size: file.size,
        file_type: '',
        extracted_text_length: 0,
        extracted_text: '',
        ocr_confidence: 0,
        is_fallback: true,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Box sx={{ maxWidth: 800, mx: 'auto', p: 3 }}>
      <Typography variant="h4" gutterBottom>
        🔍 Test OCR Tesseract
      </Typography>
      
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            📤 Upload Document
          </Typography>
          
          <Box sx={{ mb: 2 }}>
            <input
              type="file"
              accept=".pdf,.jpg,.jpeg,.png,.txt"
              onChange={handleFileSelect}
              style={{ display: 'none' }}
              id="file-input"
            />
            <label htmlFor="file-input">
              <Button
                variant="outlined"
                component="span"
                startIcon={<UploadIcon />}
                sx={{ mr: 2 }}
              >
                Choisir un fichier
              </Button>
            </label>
            
            {file && (
              <Chip 
                label={`${file.name} (${(file.size / 1024).toFixed(1)} KB)`}
                color="primary"
                variant="outlined"
              />
            )}
          </Box>

          <Button
            variant="contained"
            onClick={handleUpload}
            disabled={!file || loading}
            startIcon={<VisibilityIcon />}
          >
            {loading ? 'Extraction en cours...' : 'Tester OCR'}
          </Button>

          {loading && <LinearProgress sx={{ mt: 2 }} />}
        </CardContent>
      </Card>

      {result && (
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              📋 Résultats OCR
            </Typography>

            <Box sx={{ mb: 2 }}>
              <Chip 
                label={result.success ? '✅ Succès' : '❌ Échec'} 
                color={result.success ? 'success' : 'error'}
                sx={{ mr: 1 }}
              />
              <Chip 
                label={`Confiance: ${(result.ocr_confidence * 100).toFixed(1)}%`}
                color={result.ocr_confidence > 0.7 ? 'success' : result.ocr_confidence > 0.4 ? 'warning' : 'error'}
                sx={{ mr: 1 }}
              />
              <Chip 
                label={result.is_fallback ? '🔄 Fallback' : '🤖 OCR'}
                color={result.is_fallback ? 'warning' : 'success'}
              />
            </Box>

            <Typography variant="body2" color="text.secondary" gutterBottom>
              📄 Fichier: {result.filename} | 📊 Taille: {result.file_size} bytes | 📝 Texte extrait: {result.extracted_text_length} caractères
            </Typography>

            <Divider sx={{ my: 2 }} />

            <Typography variant="h6" gutterBottom>
              📄 Texte extrait:
            </Typography>

            <Paper sx={{ p: 2, bgcolor: 'grey.50', maxHeight: 400, overflow: 'auto' }}>
              <Typography 
                variant="body2" 
                component="pre" 
                sx={{ 
                  whiteSpace: 'pre-wrap',
                  fontFamily: 'monospace',
                  fontSize: '0.875rem'
                }}
              >
                {result.extracted_text || 'Aucun texte extrait'}
              </Typography>
            </Paper>

            {result.error && (
              <Alert severity="error" sx={{ mt: 2 }}>
                Erreur: {result.error}
              </Alert>
            )}
          </CardContent>
        </Card>
      )}
    </Box>
  );
}
