import React from "react"
import ReactDOM from "react-dom/client"
import "./index.css"

// TunLeg avec Tailwind CSS
function App() {
  const [currentPage, setCurrentPage] = React.useState('dashboard')
  const [sidebarOpen, setSidebarOpen] = React.useState(false)

  const menuItems = [
    { id: 'dashboard', label: 'Dashboard', icon: '🏠', path: '/' },
    { id: 'cases', label: 'Legal Cases', icon: '⚖️', path: '/legal-cases' },
    { id: 'documents', label: 'Documents', icon: '📄', path: '/documents' },
    { id: 'ai-analysis', label: 'AI Analysis', icon: '🤖', path: '/ai-document-analysis' },
    { id: 'admin', label: 'Administration', icon: '👥', path: '/admin' },
  ]

  const renderPageContent = () => {
    switch (currentPage) {
      case 'dashboard':
        return (
          <div className="space-y-6">
            <div className="card">
              <div className="card-header">
                <h2 className="text-2xl font-bold text-gray-900">🏛️ Welcome to TunLeg</h2>
                <p className="text-gray-600 mt-2">Your professional legal management platform</p>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div className="bg-primary-50 p-4 rounded-lg">
                  <div className="text-primary-600 text-2xl font-bold">24</div>
                  <div className="text-primary-700 text-sm">Active Cases</div>
                </div>
                <div className="bg-success-50 p-4 rounded-lg">
                  <div className="text-success-600 text-2xl font-bold">156</div>
                  <div className="text-success-700 text-sm">Documents</div>
                </div>
                <div className="bg-warning-50 p-4 rounded-lg">
                  <div className="text-warning-600 text-2xl font-bold">8</div>
                  <div className="text-warning-700 text-sm">Pending Reviews</div>
                </div>
                <div className="bg-error-50 p-4 rounded-lg">
                  <div className="text-error-600 text-2xl font-bold">3</div>
                  <div className="text-error-700 text-sm">Urgent Tasks</div>
                </div>
              </div>
            </div>
          </div>
        )
      case 'ai-analysis':
        return (
          <div className="space-y-6">
            <div className="card">
              <div className="card-header">
                <h2 className="text-2xl font-bold text-gray-900 flex items-center">
                  🤖 AI Document Analysis
                </h2>
                <p className="text-gray-600 mt-2">
                  Upload legal documents and let AI extract relevant information automatically.
                </p>
              </div>
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-primary-400 transition-colors">
                  <div className="text-4xl mb-4">📄</div>
                  <h3 className="text-lg font-medium text-gray-900 mb-2">Upload Document</h3>
                  <p className="text-gray-600 mb-4">Drag & drop files here or click to browse</p>
                  <button className="btn-primary">Select Files</button>
                </div>
                <div className="space-y-4">
                  <h3 className="text-lg font-medium text-gray-900">Analysis Status</h3>
                  <div className="bg-gray-100 rounded-full h-2">
                    <div className="bg-primary-600 h-2 rounded-full w-0"></div>
                  </div>
                  <p className="text-sm text-gray-600">Ready to analyze documents</p>
                  <div className="flex flex-wrap gap-2">
                    <span className="bg-primary-100 text-primary-800 px-2 py-1 rounded text-sm">PDF Support</span>
                    <span className="bg-primary-100 text-primary-800 px-2 py-1 rounded text-sm">Word Documents</span>
                    <span className="bg-secondary-100 text-secondary-800 px-2 py-1 rounded text-sm">AI Extraction</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )
      default:
        return (
          <div className="card">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">
              {menuItems.find(item => item.id === currentPage)?.label || 'Page'}
            </h2>
            <p className="text-gray-600">
              Content for {currentPage} page will be implemented here.
            </p>
          </div>
        )
    }
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Mobile sidebar overlay */}
      {sidebarOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
          onClick={() => setSidebarOpen(false)}
        />
      )}

      {/* Sidebar */}
      <div className={`fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0 ${
        sidebarOpen ? 'translate-x-0' : '-translate-x-full'
      }`}>
        <div className="flex items-center justify-between h-16 px-6 border-b border-gray-200">
          <h1 className="text-xl font-bold text-primary-600">TunLeg</h1>
          <button
            className="lg:hidden"
            onClick={() => setSidebarOpen(false)}
          >
            ✕
          </button>
        </div>

        <nav className="mt-6 px-4">
          <div className="space-y-2">
            {menuItems.map((item) => (
              <button
                key={item.id}
                onClick={() => {
                  setCurrentPage(item.id)
                  setSidebarOpen(false)
                }}
                className={`sidebar-item w-full text-left ${
                  currentPage === item.id ? 'active' : ''
                }`}
              >
                <span className="mr-3 text-lg">{item.icon}</span>
                {item.label}
              </button>
            ))}
          </div>
        </nav>
      </div>

      {/* Main content */}
      <div className="lg:ml-64">
        {/* Top bar */}
        <header className="bg-white shadow-sm border-b border-gray-200">
          <div className="flex items-center justify-between h-16 px-6">
            <button
              className="lg:hidden"
              onClick={() => setSidebarOpen(true)}
            >
              <span className="text-2xl">☰</span>
            </button>
            <h2 className="text-lg font-semibold text-gray-900">
              {menuItems.find(item => item.id === currentPage)?.label || 'TunLeg'}
            </h2>
            <div className="flex items-center space-x-4">
              <button className="text-gray-600 hover:text-gray-900">🔔</button>
              <button className="text-gray-600 hover:text-gray-900">👤</button>
            </div>
          </div>
        </header>

        {/* Page content */}
        <main className="p-6">
          {renderPageContent()}

          {/* Status section */}
          <div className="mt-8 grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div className="card">
              <h3 className="text-lg font-semibold text-success-700 mb-4">✅ Working Components</h3>
              <div className="space-y-2">
                <div className="alert alert-success">React 18 - Perfect</div>
                <div className="alert alert-success">Tailwind CSS - Working!</div>
                <div className="alert alert-success">Vite - Fast and stable</div>
                <div className="alert alert-success">Responsive Design - Mobile ready</div>
              </div>
            </div>

            <div className="card">
              <h3 className="text-lg font-semibold text-primary-700 mb-4">🚀 Next Steps</h3>
              <div className="space-y-2">
                <div className="alert alert-info">Add routing functionality</div>
                <div className="alert alert-info">Implement file upload</div>
                <div className="alert alert-info">Add authentication</div>
                <div className="alert alert-info">Connect to backend API</div>
              </div>
            </div>
          </div>
        </main>
      </div>
    </div>
  )
}

ReactDOM.createRoot(document.getElementById("root")!).render(<App />)
