import React from "react"
import <PERSON>actD<PERSON> from "react-dom/client"
import { ThemeProvider, createTheme } from '@mui/material/styles'
import {
  CssBase<PERSON>,
  Container,
  Typography,
  Card,
  CardContent,
  Button,
  Box,
  Grid,
  Alert,
  AppBar,
  <PERSON>l<PERSON>,
  Drawer,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  IconButton,
  useTheme,
  useMediaQuery
} from '@mui/material'
import {
  Menu as MenuIcon,
  Home as HomeIcon,
  Gavel as GavelIcon,
  Description as DescriptionIcon,
  Psychology as PsychologyIcon,
  People as PeopleIcon,
  Notifications as NotificationsIcon,
  AccountCircle as AccountCircleIcon
} from '@mui/icons-material'

// Test Material-UI très simple
const theme = createTheme({
  palette: {
    primary: {
      main: '#1976d2',
    },
  },
})

const drawerWidth = 280;

const menuItems = [
  { id: 'dashboard', label: 'Dashboard', icon: HomeIcon, path: '/' },
  { id: 'cases', label: 'Legal Cases', icon: GavelIcon, path: '/legal-cases' },
  { id: 'documents', label: 'Documents', icon: DescriptionIcon, path: '/documents' },
  { id: 'ai-analysis', label: 'AI Analysis', icon: PsychologyIcon, path: '/ai-document-analysis' },
  { id: 'admin', label: 'Administration', icon: PeopleIcon, path: '/admin' },
];

function App() {
  const [currentPage, setCurrentPage] = React.useState('dashboard');
  const [mobileOpen, setMobileOpen] = React.useState(false);
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  const handleDrawerToggle = () => {
    setMobileOpen(!mobileOpen);
  };

  const renderPageContent = () => {
    switch (currentPage) {
      case 'dashboard':
        return (
          <Container maxWidth="xl">
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <Card>
                  <CardContent>
                    <Typography variant="h4" component="h1" gutterBottom>
                      🏛️ Welcome to TunLeg
                    </Typography>
                    <Typography variant="body1" color="text.secondary" paragraph>
                      Your professional legal management platform powered by Material-UI
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>

              <Grid item xs={12} sm={6} md={3}>
                <Card sx={{ bgcolor: 'primary.50' }}>
                  <CardContent>
                    <Typography variant="h4" color="primary.main">24</Typography>
                    <Typography variant="body2" color="primary.dark">Active Cases</Typography>
                  </CardContent>
                </Card>
              </Grid>

              <Grid item xs={12} sm={6} md={3}>
                <Card sx={{ bgcolor: 'success.50' }}>
                  <CardContent>
                    <Typography variant="h4" color="success.main">156</Typography>
                    <Typography variant="body2" color="success.dark">Documents</Typography>
                  </CardContent>
                </Card>
              </Grid>

              <Grid item xs={12} sm={6} md={3}>
                <Card sx={{ bgcolor: 'warning.50' }}>
                  <CardContent>
                    <Typography variant="h4" color="warning.main">8</Typography>
                    <Typography variant="body2" color="warning.dark">Pending Reviews</Typography>
                  </CardContent>
                </Card>
              </Grid>

              <Grid item xs={12} sm={6} md={3}>
                <Card sx={{ bgcolor: 'error.50' }}>
                  <CardContent>
                    <Typography variant="h4" color="error.main">3</Typography>
                    <Typography variant="body2" color="error.dark">Urgent Tasks</Typography>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          </Container>
        );
      case 'ai-analysis':
        return (
          <Container maxWidth="xl">
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <Card>
                  <CardContent>
                    <Typography variant="h4" component="h1" gutterBottom>
                      🤖 AI Document Analysis
                    </Typography>
                    <Typography variant="body1" color="text.secondary" paragraph>
                      Upload legal documents and let AI extract relevant information automatically.
                      Review, validate, and create legal cases from the extracted data.
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>

              {/* Upload Section */}
              <Grid item xs={12} lg={8}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" gutterBottom>📄 Document Upload</Typography>

                    {/* Drag & Drop Zone */}
                    <Box
                      sx={{
                        border: '2px dashed',
                        borderColor: 'primary.main',
                        borderRadius: 2,
                        p: 4,
                        textAlign: 'center',
                        bgcolor: 'primary.50',
                        cursor: 'pointer',
                        transition: 'all 0.3s',
                        '&:hover': {
                          bgcolor: 'primary.100',
                          borderColor: 'primary.dark'
                        }
                      }}
                    >
                      <Typography variant="h1" sx={{ fontSize: '4rem', mb: 2, color: 'primary.main' }}>
                        📄
                      </Typography>
                      <Typography variant="h6" gutterBottom>
                        Drag & Drop Documents Here
                      </Typography>
                      <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                        Or click to browse files (PDF, DOC, DOCX supported)
                      </Typography>
                      <Button variant="contained" size="large" sx={{ mb: 2 }}>
                        Select Files
                      </Button>
                      <Typography variant="caption" display="block" color="text.secondary">
                        Maximum file size: 10MB per file
                      </Typography>
                    </Box>

                    {/* File List Placeholder */}
                    <Box sx={{ mt: 3 }}>
                      <Typography variant="subtitle2" gutterBottom>
                        Uploaded Files:
                      </Typography>
                      <Alert severity="info" sx={{ mt: 1 }}>
                        No files uploaded yet. Select files to begin analysis.
                      </Alert>
                    </Box>
                  </CardContent>
                </Card>
              </Grid>

              {/* Analysis Panel */}
              <Grid item xs={12} lg={4}>
                <Grid container spacing={2}>
                  <Grid item xs={12}>
                    <Card>
                      <CardContent>
                        <Typography variant="h6" gutterBottom>🤖 Analysis Status</Typography>
                        <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                          Ready to analyze documents
                        </Typography>
                        <Box sx={{ width: '100%', mb: 2 }}>
                          <Typography variant="body2" color="text.secondary">
                            Progress: 0%
                          </Typography>
                          <Box sx={{ width: '100%', bgcolor: 'grey.200', borderRadius: 1, height: 8, mt: 1 }}>
                            <Box sx={{ width: '0%', bgcolor: 'primary.main', height: '100%', borderRadius: 1 }} />
                          </Box>
                        </Box>
                        <Button variant="outlined" fullWidth disabled>
                          Start Analysis
                        </Button>
                      </CardContent>
                    </Card>
                  </Grid>

                  <Grid item xs={12}>
                    <Card>
                      <CardContent>
                        <Typography variant="h6" gutterBottom>📋 Supported Features</Typography>
                        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                          <Button variant="outlined" size="small" startIcon="📄">PDF Documents</Button>
                          <Button variant="outlined" size="small" startIcon="📝">Word Documents</Button>
                          <Button variant="outlined" size="small" startIcon="🔍" color="secondary">Text Extraction</Button>
                          <Button variant="outlined" size="small" startIcon="🧠" color="secondary">AI Analysis</Button>
                          <Button variant="outlined" size="small" startIcon="⚖️" color="success">Legal Case Creation</Button>
                        </Box>
                      </CardContent>
                    </Card>
                  </Grid>
                </Grid>
              </Grid>

              {/* Results Section */}
              <Grid item xs={12}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" gutterBottom>📊 Analysis Results</Typography>
                    <Alert severity="warning">
                      Upload and analyze documents to see extracted information and AI insights here.
                    </Alert>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          </Container>
        );
      default:
        return (
          <Container maxWidth="xl">
            <Card>
              <CardContent>
                <Typography variant="h4" component="h1" gutterBottom>
                  {menuItems.find(item => item.id === currentPage)?.label || 'Page'}
                </Typography>
                <Typography variant="body1" color="text.secondary">
                  Content for {currentPage} page will be implemented here.
                </Typography>
              </CardContent>
            </Card>
          </Container>
        );
    }
  };

  const drawer = (
    <Box>
      <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider' }}>
        <Typography variant="h6" sx={{ fontWeight: 'bold', color: 'primary.main' }}>
          TunLeg
        </Typography>
        <Typography variant="caption" color="text.secondary">
          Legal Management System
        </Typography>
      </Box>

      <List>
        {menuItems.map((item) => (
          <ListItem key={item.id} disablePadding>
            <ListItemButton
              selected={currentPage === item.id}
              onClick={() => {
                setCurrentPage(item.id);
                if (isMobile) setMobileOpen(false);
              }}
            >
              <ListItemIcon>
                <item.icon />
              </ListItemIcon>
              <ListItemText primary={item.label} />
            </ListItemButton>
          </ListItem>
        ))}
      </List>
    </Box>
  );

  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <Box sx={{ display: 'flex' }}>
        {/* AppBar */}
        <AppBar
          position="fixed"
          sx={{
            width: { md: `calc(100% - ${drawerWidth}px)` },
            ml: { md: `${drawerWidth}px` },
          }}
        >
          <Toolbar>
            <IconButton
              color="inherit"
              aria-label="open drawer"
              edge="start"
              onClick={handleDrawerToggle}
              sx={{ mr: 2, display: { md: 'none' } }}
            >
              <MenuIcon />
            </IconButton>
            <Typography variant="h6" noWrap component="div" sx={{ flexGrow: 1 }}>
              TunLeg - {menuItems.find(item => item.id === currentPage)?.label || 'Legal Management'}
            </Typography>
            <IconButton color="inherit">
              <NotificationsIcon />
            </IconButton>
            <IconButton color="inherit">
              <AccountCircleIcon />
            </IconButton>
          </Toolbar>
        </AppBar>

        {/* Drawer */}
        <Box
          component="nav"
          sx={{ width: { md: drawerWidth }, flexShrink: { md: 0 } }}
        >
          <Drawer
            variant="temporary"
            open={mobileOpen}
            onClose={handleDrawerToggle}
            ModalProps={{ keepMounted: true }}
            sx={{
              display: { xs: 'block', md: 'none' },
              '& .MuiDrawer-paper': { boxSizing: 'border-box', width: drawerWidth },
            }}
          >
            {drawer}
          </Drawer>
          <Drawer
            variant="permanent"
            sx={{
              display: { xs: 'none', md: 'block' },
              '& .MuiDrawer-paper': { boxSizing: 'border-box', width: drawerWidth },
            }}
            open
          >
            {drawer}
          </Drawer>
        </Box>

        {/* Main content */}
        <Box
          component="main"
          sx={{
            flexGrow: 1,
            p: 3,
            width: { md: `calc(100% - ${drawerWidth}px)` },
            mt: '64px',
          }}
        >
          {renderPageContent()}

          {/* Status section */}
          <Container maxWidth="xl" sx={{ mt: 4 }}>
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <Alert severity="success" sx={{ mb: 2 }}>
                  🎉 Material-UI Layout complet fonctionne parfaitement !
                </Alert>
                <Alert severity="info">
                  📋 Interface professionnelle avec AppBar, Drawer et navigation
                </Alert>
              </Grid>
              <Grid item xs={12} md={6}>
                <Alert severity="warning" sx={{ mb: 2 }}>
                  🚀 Prochaines étapes : Ajouter les composants existants
                </Alert>
                <Alert severity="error">
                  ⚡ Puis implémenter l'upload et l'authentification
                </Alert>
              </Grid>
            </Grid>
          </Container>
        </Box>
      </Box>
    </ThemeProvider>
  );
}

ReactDOM.createRoot(document.getElementById("root")!).render(<App />)
