import React from "react"
import ReactDOM from "react-dom/client"
import { ChakraProvider, Box, Text, Button } from "@chakra-ui/react"

// Test avec Chakra UI de base
function App() {
  const [count, setCount] = React.useState(0)

  return (
    <ChakraProvider>
      <div style={{ padding: '20px', fontFamily: 'Arial, sans-serif' }}>
        <h1 style={{ color: 'green' }}>✅ Test Chakra UI de base</h1>
        <p>Compteur: {count}</p>
        <button
          onClick={() => setCount(count + 1)}
          style={{
            padding: '10px 20px',
            backgroundColor: '#007bff',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer'
          }}
        >
          Incrémenter
        </button>
        <div style={{
          marginTop: '20px',
          padding: '15px',
          backgroundColor: '#e8f5e8',
          borderRadius: '4px'
        }}>
          <p><strong>Test Chakra UI :</strong></p>
          <ul>
            <li>✅ React fonctionne</li>
            <li>✅ Vite fonctionne</li>
            <li>🔍 ChakraProvider en test...</li>
          </ul>
        </div>
      </div>
    </ChakraProvider>
  )
}

ReactDOM.createRoot(document.getElementById("root")!).render(<App />)
