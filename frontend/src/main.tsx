import React from "react"
import ReactDOM from "react-dom/client"
import { ThemeProvider, createTheme } from '@mui/material/styles'
import {
  CssBaseline,
  Container,
  Typography,
  Card,
  CardContent,
  Button,
  Box,
  Alert,
  AppBar,
  Tool<PERSON>,
  Drawer,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  IconButton,
  useTheme,
  useMediaQuery,
  Grid,
} from '@mui/material'
import {
  Menu as MenuIcon,
  Home as HomeIcon,
  Gavel as GavelIcon,
  Description as DescriptionIcon,
  Psychology as PsychologyIcon,
  People as PeopleIcon,
  Notifications as NotificationsIcon,
  AccountCircle as AccountCircleIcon,
} from '@mui/icons-material'
import { AuthProvider, useAuth } from './contexts/AuthContext';
import LoginForm from './components/Auth/LoginForm';
import AIDocumentAnalysisWorkflow from './components/AIAnalysis/AIDocumentAnalysisWorkflow';
import OCRTest from './components/OCRTest';
import CompleteAnalysisTest from './components/CompleteAnalysisTest';

// Theme Material-UI professionnel
const theme = createTheme({
  palette: {
    primary: {
      main: '#1976d2',
    },
    secondary: {
      main: '#dc004e',
    },
  },
})

const drawerWidth = 280;

const menuItems = [
  { id: 'dashboard', label: 'Dashboard', icon: HomeIcon, path: '/' },
  { id: 'cases', label: 'Legal Cases', icon: GavelIcon, path: '/legal-cases' },
  { id: 'documents', label: 'Documents', icon: DescriptionIcon, path: '/documents' },
  { id: 'ai-analysis', label: 'AI Analysis', icon: PsychologyIcon, path: '/ai-document-analysis' },
  { id: 'ocr-test', label: '🔍 Test OCR', icon: DescriptionIcon, path: '/ocr-test' },
  { id: 'complete-test', label: '🤖 Test Complet', icon: PsychologyIcon, path: '/complete-analysis' },
  { id: 'admin', label: 'Administration', icon: PeopleIcon, path: '/admin' },
];

// Interface complète avec authentification
function MainApp() {
  const [currentPage, setCurrentPage] = React.useState('dashboard');
  const [mobileOpen, setMobileOpen] = React.useState(false);
  const { user, logout } = useAuth();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  const handleDrawerToggle = () => {
    setMobileOpen(!mobileOpen);
  };

  const renderPageContent = () => {
    switch (currentPage) {
      case 'dashboard':
        return (
          <Container maxWidth="xl">
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <Card>
                  <CardContent>
                    <Typography variant="h4" component="h1" gutterBottom>
                      🏛️ Welcome to TunLeg, {user?.first_name}!
                    </Typography>
                    <Typography variant="body1" color="text.secondary" paragraph>
                      Your professional legal management platform powered by Material-UI
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Role: {user?.role} | Email: {user?.email}
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>

              <Grid item xs={12} sm={6} md={3}>
                <Card sx={{ bgcolor: 'primary.50' }}>
                  <CardContent>
                    <Typography variant="h4" color="primary.main">24</Typography>
                    <Typography variant="body2" color="primary.dark">Active Cases</Typography>
                  </CardContent>
                </Card>
              </Grid>

              <Grid item xs={12} sm={6} md={3}>
                <Card sx={{ bgcolor: 'success.50' }}>
                  <CardContent>
                    <Typography variant="h4" color="success.main">156</Typography>
                    <Typography variant="body2" color="success.dark">Documents</Typography>
                  </CardContent>
                </Card>
              </Grid>

              <Grid item xs={12} sm={6} md={3}>
                <Card sx={{ bgcolor: 'warning.50' }}>
                  <CardContent>
                    <Typography variant="h4" color="warning.main">8</Typography>
                    <Typography variant="body2" color="warning.dark">Pending Reviews</Typography>
                  </CardContent>
                </Card>
              </Grid>

              <Grid item xs={12} sm={6} md={3}>
                <Card sx={{ bgcolor: 'error.50' }}>
                  <CardContent>
                    <Typography variant="h4" color="error.main">3</Typography>
                    <Typography variant="body2" color="error.dark">Urgent Tasks</Typography>
                  </CardContent>
                </Card>
              </Grid>

              <Grid item xs={12}>
                <Alert severity="success">
                  ✅ Authentification et interface complète fonctionnelles !
                </Alert>
              </Grid>
            </Grid>
          </Container>
        );
      case 'ai-analysis':
        return (
          <Container maxWidth="xl">
            <AIDocumentAnalysisWorkflow
              onComplete={(cases) => {
                console.log('Cases created:', cases);
                // Could redirect to legal cases page or show success message
              }}
            />
          </Container>
        );
      case 'ocr-test':
        return (
          <Container maxWidth="xl">
            <OCRTest />
          </Container>
        );
      case 'complete-test':
        return (
          <Container maxWidth="xl">
            <CompleteAnalysisTest />
          </Container>
        );
      default:
        return (
          <Container maxWidth="xl">
            <Card>
              <CardContent>
                <Typography variant="h4" component="h1" gutterBottom>
                  {menuItems.find(item => item.id === currentPage)?.label || 'Page'}
                </Typography>
                <Typography variant="body1" color="text.secondary">
                  Content for {currentPage} page will be implemented here.
                </Typography>
              </CardContent>
            </Card>
          </Container>
        );
    }
  };

  const drawer = (
    <Box>
      <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider' }}>
        <Typography variant="h6" sx={{ fontWeight: 'bold', color: 'primary.main' }}>
          TunLeg
        </Typography>
        <Typography variant="caption" color="text.secondary">
          Legal Management System
        </Typography>
      </Box>

      <List>
        {menuItems.map((item) => (
          <ListItem key={item.id} disablePadding>
            <ListItemButton
              selected={currentPage === item.id}
              onClick={() => {
                setCurrentPage(item.id);
                if (isMobile) setMobileOpen(false);
              }}
            >
              <ListItemIcon>
                <item.icon />
              </ListItemIcon>
              <ListItemText primary={item.label} />
            </ListItemButton>
          </ListItem>
        ))}
      </List>
    </Box>
  );

  return (
    <Box sx={{ display: 'flex' }}>
      {/* AppBar */}
      <AppBar
        position="fixed"
        sx={{
          width: { md: `calc(100% - ${drawerWidth}px)` },
          ml: { md: `${drawerWidth}px` },
        }}
      >
        <Toolbar>
          <IconButton
            color="inherit"
            aria-label="open drawer"
            edge="start"
            onClick={handleDrawerToggle}
            sx={{ mr: 2, display: { md: 'none' } }}
          >
            <MenuIcon />
          </IconButton>
          <Typography variant="h6" noWrap component="div" sx={{ flexGrow: 1 }}>
            TunLeg - {menuItems.find(item => item.id === currentPage)?.label || 'Legal Management'}
          </Typography>

          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Typography variant="body2" sx={{ display: { xs: 'none', sm: 'block' } }}>
              {user?.first_name} {user?.last_name} ({user?.role})
            </Typography>
            <IconButton color="inherit">
              <NotificationsIcon />
            </IconButton>
            <IconButton color="inherit" onClick={logout}>
              <AccountCircleIcon />
            </IconButton>
          </Box>
        </Toolbar>
      </AppBar>

      {/* Drawer */}
      <Box
        component="nav"
        sx={{ width: { md: drawerWidth }, flexShrink: { md: 0 } }}
      >
        <Drawer
          variant="temporary"
          open={mobileOpen}
          onClose={handleDrawerToggle}
          ModalProps={{ keepMounted: true }}
          sx={{
            display: { xs: 'block', md: 'none' },
            '& .MuiDrawer-paper': { boxSizing: 'border-box', width: drawerWidth },
          }}
        >
          {drawer}
        </Drawer>
        <Drawer
          variant="permanent"
          sx={{
            display: { xs: 'none', md: 'block' },
            '& .MuiDrawer-paper': { boxSizing: 'border-box', width: drawerWidth },
          }}
          open
        >
          {drawer}
        </Drawer>
      </Box>

      {/* Main content */}
      <Box
        component="main"
        sx={{
          flexGrow: 1,
          p: 3,
          width: { md: `calc(100% - ${drawerWidth}px)` },
          mt: '64px',
        }}
      >
        {renderPageContent()}
      </Box>
    </Box>
  );
}

// Composant App avec gestion d'authentification
function App() {
  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <AuthProvider>
        <AppContent />
      </AuthProvider>
    </ThemeProvider>
  );
}

// Contenu de l'app qui dépend de l'authentification
function AppContent() {
  const { isAuthenticated, isLoading } = useAuth();

  if (isLoading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '100vh' }}>
        <Typography variant="h6">Loading...</Typography>
      </Box>
    );
  }

  if (!isAuthenticated) {
    return <LoginForm />;
  }

  return <MainApp />;
}

ReactDOM.createRoot(document.getElementById("root")!).render(<App />)
