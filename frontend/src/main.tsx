import React from "react"
import ReactDOM from "react-dom/client"

// Test React pur sans dépendances
function App() {
  const [count, setCount] = React.useState(0)

  return (
    <div style={{ padding: '20px', fontFamily: 'Arial, sans-serif' }}>
      <h1 style={{ color: 'green' }}>✅ React fonctionne avec Vite !</h1>
      <p>Compteur: {count}</p>
      <button
        onClick={() => setCount(count + 1)}
        style={{
          padding: '10px 20px',
          backgroundColor: '#007bff',
          color: 'white',
          border: 'none',
          borderRadius: '4px',
          cursor: 'pointer'
        }}
      >
        Incrémenter
      </button>
      <div style={{
        marginTop: '20px',
        padding: '15px',
        backgroundColor: '#e8f5e8',
        borderRadius: '4px'
      }}>
        <p><strong>Si vous voyez ce message :</strong></p>
        <ul>
          <li>✅ React fonctionne</li>
          <li>✅ Vite fonctionne</li>
          <li>✅ Le build fonctionne</li>
        </ul>
        <p>Le problème était dans Chakra UI ou TanStack Router.</p>
      </div>
    </div>
  )
}

ReactDOM.createRoot(document.getElementById("root")!).render(<App />)
