import React from "react"
import ReactD<PERSON> from "react-dom/client"
import { Box, Text, <PERSON>ing, <PERSON><PERSON>, VS<PERSON>ck, Container, <PERSON><PERSON>, AlertIcon } from "@chakra-ui/react"
import { CustomProvider } from "./components/ui/provider"

// Test avec simulation de routing simple
function App() {
  const [currentPage, setCurrentPage] = React.useState('home')

  const renderPage = () => {
    switch (currentPage) {
      case 'ai-analysis':
        return (
          <Box>
            <Heading size="xl" mb={4}>AI Document Analysis</Heading>
            <Text mb={4}>Cette page fonctionne avec un router simulé !</Text>
            <Alert status="success">
              <AlertIcon />
              La fonctionnalité d'upload sera ajoutée une fois l'API configurée.
            </Alert>
          </Box>
        )
      case 'diagnostic':
        return (
          <Box>
            <Heading size="xl" mb={4}>Diagnostic</Heading>
            <VStack spacing={4} align="stretch">
              <Text>✅ React fonctionne</Text>
              <Text>✅ Chakra UI fonctionne</Text>
              <Text>✅ CustomProvider fonctionne</Text>
              <Text>✅ Router simulé fonctionne</Text>
            </VStack>
          </Box>
        )
      default:
        return (
          <Box>
            <Heading size="xl" mb={4}>Accueil</Heading>
            <Text mb={4}>Bienvenue dans TunLeg !</Text>
            <Text>Utilisez les boutons ci-dessus pour naviguer.</Text>
          </Box>
        )
    }
  }

  return (
    <CustomProvider>
      <Container maxW="8xl" py={8}>
        <VStack spacing={8} align="stretch">
          {/* Navigation simple */}
          <Box p={4} bg="gray.100" borderRadius="md">
            <Text fontWeight="bold" mb={4}>Navigation Test</Text>
            <VStack spacing={2}>
              <Button
                onClick={() => setCurrentPage('home')}
                colorScheme={currentPage === 'home' ? 'blue' : 'gray'}
                size="sm"
              >
                Accueil
              </Button>
              <Button
                onClick={() => setCurrentPage('ai-analysis')}
                colorScheme={currentPage === 'ai-analysis' ? 'blue' : 'gray'}
                size="sm"
              >
                AI Document Analysis
              </Button>
              <Button
                onClick={() => setCurrentPage('diagnostic')}
                colorScheme={currentPage === 'diagnostic' ? 'blue' : 'gray'}
                size="sm"
              >
                Diagnostic
              </Button>
            </VStack>
          </Box>

          {/* Contenu de la page */}
          <Box p={8} bg="white" borderRadius="md" shadow="sm">
            {renderPage()}
          </Box>
        </VStack>
      </Container>
    </CustomProvider>
  )
}

ReactDOM.createRoot(document.getElementById("root")!).render(<App />)
