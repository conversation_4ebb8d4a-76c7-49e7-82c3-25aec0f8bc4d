import React from "react"
import ReactDOM from "react-dom/client"

// Styles CSS inline pour test immédiat
const styles = `
  * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }

  body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
    background-color: #f8fafc;
    color: #1e293b;
  }

  .min-h-screen { min-height: 100vh; }
  .bg-gray-50 { background-color: #f8fafc; }
  .bg-white { background-color: #ffffff; }
  .bg-primary-50 { background-color: #eff6ff; }
  .bg-success-50 { background-color: #f0fdf4; }
  .bg-warning-50 { background-color: #fffbeb; }
  .bg-error-50 { background-color: #fef2f2; }

  .text-primary-600 { color: #2563eb; }
  .text-primary-700 { color: #1d4ed8; }
  .text-success-600 { color: #16a34a; }
  .text-success-700 { color: #15803d; }
  .text-warning-600 { color: #d97706; }
  .text-warning-700 { color: #b45309; }
  .text-error-600 { color: #dc2626; }
  .text-error-700 { color: #b91c1c; }
  .text-gray-600 { color: #4b5563; }
  .text-gray-900 { color: #111827; }

  .fixed { position: fixed; }
  .inset-0 { top: 0; right: 0; bottom: 0; left: 0; }
  .inset-y-0 { top: 0; bottom: 0; }
  .left-0 { left: 0; }
  .z-50 { z-index: 50; }
  .z-40 { z-index: 40; }

  .w-64 { width: 16rem; }
  .h-16 { height: 4rem; }

  .flex { display: flex; }
  .grid { display: grid; }
  .hidden { display: none; }

  .items-center { align-items: center; }
  .justify-between { justify-content: space-between; }
  .text-left { text-align: left; }
  .text-center { text-align: center; }

  .p-4 { padding: 1rem; }
  .p-6 { padding: 1.5rem; }
  .p-8 { padding: 2rem; }
  .px-4 { padding-left: 1rem; padding-right: 1rem; }
  .px-6 { padding-left: 1.5rem; padding-right: 1.5rem; }
  .py-1 { padding-top: 0.25rem; padding-bottom: 0.25rem; }
  .py-2 { padding-top: 0.5rem; padding-bottom: 0.5rem; }
  .mt-2 { margin-top: 0.5rem; }
  .mt-4 { margin-top: 1rem; }
  .mt-6 { margin-top: 1.5rem; }
  .mt-8 { margin-top: 2rem; }
  .mb-2 { margin-bottom: 0.5rem; }
  .mb-4 { margin-bottom: 1rem; }
  .mr-3 { margin-right: 0.75rem; }

  .space-y-2 > * + * { margin-top: 0.5rem; }
  .space-y-4 > * + * { margin-top: 1rem; }
  .space-y-6 > * + * { margin-top: 1.5rem; }
  .space-x-4 > * + * { margin-left: 1rem; }

  .rounded-lg { border-radius: 0.5rem; }
  .rounded { border-radius: 0.25rem; }

  .border { border-width: 1px; }
  .border-b { border-bottom-width: 1px; }
  .border-gray-200 { border-color: #e5e7eb; }
  .border-dashed { border-style: dashed; }
  .border-2 { border-width: 2px; }
  .border-gray-300 { border-color: #d1d5db; }

  .shadow-sm { box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05); }
  .shadow-lg { box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1); }

  .text-xl { font-size: 1.25rem; line-height: 1.75rem; }
  .text-2xl { font-size: 1.5rem; line-height: 2rem; }
  .text-lg { font-size: 1.125rem; line-height: 1.75rem; }
  .text-sm { font-size: 0.875rem; line-height: 1.25rem; }

  .font-bold { font-weight: 700; }
  .font-semibold { font-weight: 600; }
  .font-medium { font-weight: 500; }

  .cursor-pointer { cursor: pointer; }
  .w-full { width: 100%; }

  .btn-primary {
    background-color: #2563eb;
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    border: none;
    cursor: pointer;
    font-weight: 500;
    transition: background-color 0.2s;
  }

  .btn-primary:hover {
    background-color: #1d4ed8;
  }

  .card {
    background-color: white;
    border-radius: 0.5rem;
    box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1);
    border: 1px solid #e5e7eb;
    padding: 1.5rem;
  }

  .sidebar-item {
    display: flex;
    align-items: center;
    padding: 0.5rem 1rem;
    color: #374151;
    border-radius: 0.5rem;
    cursor: pointer;
    transition: all 0.2s;
    width: 100%;
    border: none;
    background: none;
    text-align: left;
  }

  .sidebar-item:hover {
    background-color: #f3f4f6;
    color: #111827;
  }

  .sidebar-item.active {
    background-color: #eff6ff;
    color: #1d4ed8;
    border-right: 2px solid #2563eb;
  }

  .alert {
    padding: 1rem;
    border-radius: 0.5rem;
    border: 1px solid;
  }

  .alert-success {
    background-color: #f0fdf4;
    border-color: #bbf7d0;
    color: #166534;
  }

  .alert-info {
    background-color: #eff6ff;
    border-color: #bfdbfe;
    color: #1e40af;
  }

  .grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
  .grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
  .gap-4 { gap: 1rem; }
  .gap-6 { gap: 1.5rem; }

  .transform { transform: translateX(0); }
  .-translate-x-full { transform: translateX(-100%); }
  .translate-x-0 { transform: translateX(0); }

  .transition-transform { transition-property: transform; }
  .duration-300 { transition-duration: 300ms; }
  .ease-in-out { transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); }

  .bg-black { background-color: #000000; }
  .bg-opacity-50 { background-color: rgb(0 0 0 / 0.5); }

  @media (min-width: 1024px) {
    .lg\\:translate-x-0 { transform: translateX(0); }
    .lg\\:static { position: static; }
    .lg\\:inset-0 { top: 0; right: 0; bottom: 0; left: 0; }
    .lg\\:hidden { display: none; }
    .lg\\:ml-64 { margin-left: 16rem; }
    .lg\\:grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
  }

  @media (min-width: 768px) {
    .md\\:grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
    .md\\:grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
  }
`

// Injecter les styles
const styleSheet = document.createElement("style")
styleSheet.innerText = styles
document.head.appendChild(styleSheet)

// TunLeg avec Tailwind CSS
function App() {
  const [currentPage, setCurrentPage] = React.useState('dashboard')
  const [sidebarOpen, setSidebarOpen] = React.useState(false)

  const menuItems = [
    { id: 'dashboard', label: 'Dashboard', icon: '🏠', path: '/' },
    { id: 'cases', label: 'Legal Cases', icon: '⚖️', path: '/legal-cases' },
    { id: 'documents', label: 'Documents', icon: '📄', path: '/documents' },
    { id: 'ai-analysis', label: 'AI Analysis', icon: '🤖', path: '/ai-document-analysis' },
    { id: 'admin', label: 'Administration', icon: '👥', path: '/admin' },
  ]

  const renderPageContent = () => {
    switch (currentPage) {
      case 'dashboard':
        return (
          <div className="space-y-6">
            <div className="card">
              <div className="card-header">
                <h2 className="text-2xl font-bold text-gray-900">🏛️ Welcome to TunLeg</h2>
                <p className="text-gray-600 mt-2">Your professional legal management platform</p>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div className="bg-primary-50 p-4 rounded-lg">
                  <div className="text-primary-600 text-2xl font-bold">24</div>
                  <div className="text-primary-700 text-sm">Active Cases</div>
                </div>
                <div className="bg-success-50 p-4 rounded-lg">
                  <div className="text-success-600 text-2xl font-bold">156</div>
                  <div className="text-success-700 text-sm">Documents</div>
                </div>
                <div className="bg-warning-50 p-4 rounded-lg">
                  <div className="text-warning-600 text-2xl font-bold">8</div>
                  <div className="text-warning-700 text-sm">Pending Reviews</div>
                </div>
                <div className="bg-error-50 p-4 rounded-lg">
                  <div className="text-error-600 text-2xl font-bold">3</div>
                  <div className="text-error-700 text-sm">Urgent Tasks</div>
                </div>
              </div>
            </div>
          </div>
        )
      case 'ai-analysis':
        return (
          <div className="space-y-6">
            <div className="card">
              <div className="card-header">
                <h2 className="text-2xl font-bold text-gray-900 flex items-center">
                  🤖 AI Document Analysis
                </h2>
                <p className="text-gray-600 mt-2">
                  Upload legal documents and let AI extract relevant information automatically.
                </p>
              </div>
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-primary-400 transition-colors">
                  <div className="text-4xl mb-4">📄</div>
                  <h3 className="text-lg font-medium text-gray-900 mb-2">Upload Document</h3>
                  <p className="text-gray-600 mb-4">Drag & drop files here or click to browse</p>
                  <button className="btn-primary">Select Files</button>
                </div>
                <div className="space-y-4">
                  <h3 className="text-lg font-medium text-gray-900">Analysis Status</h3>
                  <div className="bg-gray-100 rounded-full h-2">
                    <div className="bg-primary-600 h-2 rounded-full w-0"></div>
                  </div>
                  <p className="text-sm text-gray-600">Ready to analyze documents</p>
                  <div className="flex flex-wrap gap-2">
                    <span className="bg-primary-100 text-primary-800 px-2 py-1 rounded text-sm">PDF Support</span>
                    <span className="bg-primary-100 text-primary-800 px-2 py-1 rounded text-sm">Word Documents</span>
                    <span className="bg-secondary-100 text-secondary-800 px-2 py-1 rounded text-sm">AI Extraction</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )
      default:
        return (
          <div className="card">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">
              {menuItems.find(item => item.id === currentPage)?.label || 'Page'}
            </h2>
            <p className="text-gray-600">
              Content for {currentPage} page will be implemented here.
            </p>
          </div>
        )
    }
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Mobile sidebar overlay */}
      {sidebarOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
          onClick={() => setSidebarOpen(false)}
        />
      )}

      {/* Sidebar */}
      <div className={`fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0 ${
        sidebarOpen ? 'translate-x-0' : '-translate-x-full'
      }`}>
        <div className="flex items-center justify-between h-16 px-6 border-b border-gray-200">
          <h1 className="text-xl font-bold text-primary-600">TunLeg</h1>
          <button
            className="lg:hidden"
            onClick={() => setSidebarOpen(false)}
          >
            ✕
          </button>
        </div>

        <nav className="mt-6 px-4">
          <div className="space-y-2">
            {menuItems.map((item) => (
              <button
                key={item.id}
                onClick={() => {
                  setCurrentPage(item.id)
                  setSidebarOpen(false)
                }}
                className={`sidebar-item w-full text-left ${
                  currentPage === item.id ? 'active' : ''
                }`}
              >
                <span className="mr-3 text-lg">{item.icon}</span>
                {item.label}
              </button>
            ))}
          </div>
        </nav>
      </div>

      {/* Main content */}
      <div className="lg:ml-64">
        {/* Top bar */}
        <header className="bg-white shadow-sm border-b border-gray-200">
          <div className="flex items-center justify-between h-16 px-6">
            <button
              className="lg:hidden"
              onClick={() => setSidebarOpen(true)}
            >
              <span className="text-2xl">☰</span>
            </button>
            <h2 className="text-lg font-semibold text-gray-900">
              {menuItems.find(item => item.id === currentPage)?.label || 'TunLeg'}
            </h2>
            <div className="flex items-center space-x-4">
              <button className="text-gray-600 hover:text-gray-900">🔔</button>
              <button className="text-gray-600 hover:text-gray-900">👤</button>
            </div>
          </div>
        </header>

        {/* Page content */}
        <main className="p-6">
          {renderPageContent()}

          {/* Status section */}
          <div className="mt-8 grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div className="card">
              <h3 className="text-lg font-semibold text-success-700 mb-4">✅ Working Components</h3>
              <div className="space-y-2">
                <div className="alert alert-success">React 18 - Perfect</div>
                <div className="alert alert-success">Tailwind CSS - Working!</div>
                <div className="alert alert-success">Vite - Fast and stable</div>
                <div className="alert alert-success">Responsive Design - Mobile ready</div>
              </div>
            </div>

            <div className="card">
              <h3 className="text-lg font-semibold text-primary-700 mb-4">🚀 Next Steps</h3>
              <div className="space-y-2">
                <div className="alert alert-info">Add routing functionality</div>
                <div className="alert alert-info">Implement file upload</div>
                <div className="alert alert-info">Add authentication</div>
                <div className="alert alert-info">Connect to backend API</div>
              </div>
            </div>
          </div>
        </main>
      </div>
    </div>
  )
}

ReactDOM.createRoot(document.getElementById("root")!).render(<App />)
