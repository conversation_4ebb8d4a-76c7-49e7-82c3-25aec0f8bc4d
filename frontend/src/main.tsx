import React from "react"
import ReactD<PERSON> from "react-dom/client"
import { Box, Text, Heading } from "@chakra-ui/react"
import { CustomProvider } from "./components/ui/provider"

// Test avec notre CustomProvider
function App() {
  return (
    <CustomProvider>
      <Box p={8}>
        <Heading mb={4}>Test avec CustomProvider</Heading>
        <Text>Si vous voyez ce message, notre thème personnalisé fonctionne !</Text>
      </Box>
    </CustomProvider>
  )
}

ReactDOM.createRoot(document.getElementById("root")!).render(<App />)
