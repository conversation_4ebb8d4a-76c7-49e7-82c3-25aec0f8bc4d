import React from "react"
import <PERSON>actD<PERSON> from "react-dom/client"
import { ThemeProvider, createTheme } from '@mui/material/styles'
import {
  CssBaseline,
  Container,
  Typography,
  Button,
  Card,
  CardContent,
  Box,
  Alert,
  AppBar,
  Toolbar,
  Paper,
  Grid,
  Chip,
  LinearProgress
} from '@mui/material'
import {
  Upload as UploadIcon,
  Assessment as AssessmentIcon,
  Home as HomeIcon,
  Description as DescriptionIcon
} from '@mui/icons-material'

// Thème Material-UI
const theme = createTheme({
  palette: {
    primary: {
      main: '#1976d2',
    },
    secondary: {
      main: '#dc004e',
    },
  },
})

// App Material-UI avec router simulé
function App() {
  const [currentPage, setCurrentPage] = React.useState('home')

  const renderPage = () => {
    switch (currentPage) {
      case 'ai-analysis':
        return (
          <Grid container spacing={3} sx={{ mt: 1 }}>
            <Grid item xs={12}>
              <Card>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <DescriptionIcon sx={{ mr: 1, color: 'primary.main' }} />
                    <Typography variant="h4" component="h2">
                      AI Document Analysis
                    </Typography>
                  </Box>
                  <Typography variant="body1" color="text.secondary" paragraph>
                    Upload legal documents and let AI extract relevant information automatically.
                    Review, validate, and create legal cases from the extracted data.
                  </Typography>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    📄 Upload Document
                  </Typography>
                  <Paper
                    sx={{
                      p: 3,
                      textAlign: 'center',
                      border: '2px dashed #ccc',
                      backgroundColor: '#fafafa',
                      cursor: 'pointer',
                      '&:hover': { backgroundColor: '#f0f0f0' }
                    }}
                  >
                    <UploadIcon sx={{ fontSize: 48, color: 'text.secondary', mb: 1 }} />
                    <Typography variant="body1" color="text.secondary">
                      Drag & drop files here or click to browse
                    </Typography>
                    <Button variant="contained" sx={{ mt: 2 }} disabled>
                      Select Files
                    </Button>
                  </Paper>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    🤖 Analysis Status
                  </Typography>
                  <Box sx={{ mb: 2 }}>
                    <Typography variant="body2" color="text.secondary">
                      Ready to analyze documents
                    </Typography>
                    <LinearProgress variant="determinate" value={0} sx={{ mt: 1 }} />
                  </Box>
                  <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                    <Chip label="PDF Support" color="primary" size="small" />
                    <Chip label="Word Documents" color="primary" size="small" />
                    <Chip label="AI Extraction" color="secondary" size="small" />
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        )
      case 'diagnostic':
        return (
          <Grid container spacing={3} sx={{ mt: 1 }}>
            <Grid item xs={12}>
              <Card>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <AssessmentIcon sx={{ mr: 1, color: 'primary.main' }} />
                    <Typography variant="h4" component="h2">
                      Diagnostic Système
                    </Typography>
                  </Box>
                  <Typography variant="body1" color="text.secondary" paragraph>
                    État des composants et bibliothèques de l'application.
                  </Typography>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom color="success.main">
                    ✅ Composants Fonctionnels
                  </Typography>
                  <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                    <Alert severity="success">React 18 - Parfait</Alert>
                    <Alert severity="success">Vite - Rapide et stable</Alert>
                    <Alert severity="success">Material-UI - Interface moderne</Alert>
                    <Alert severity="success">TypeScript - Support complet</Alert>
                  </Box>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom color="error.main">
                    ❌ Composants Problématiques
                  </Typography>
                  <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                    <Alert severity="error">Chakra UI - Pages blanches</Alert>
                    <Alert severity="error">TanStack Router - Incompatibilité</Alert>
                    <Alert severity="warning">API Client - Non configuré</Alert>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        )
      default:
        return (
          <Card sx={{ mt: 2 }}>
            <CardContent>
              <Typography variant="h4" component="h2" gutterBottom>
                Accueil TunLeg
              </Typography>
              <Typography variant="body1" color="text.secondary" paragraph>
                Bienvenue dans l'application de gestion juridique TunLeg.
              </Typography>
              <Typography variant="body1" color="text.secondary">
                Utilisez la navigation ci-dessus pour explorer les fonctionnalités.
              </Typography>
            </CardContent>
          </Card>
        )
    }
  }

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      {/* Navigation */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h3" component="h1" gutterBottom>
            TunLeg - Material-UI (Router Simulé)
          </Typography>
          <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
            {[
              { key: 'home', label: 'Accueil' },
              { key: 'ai-analysis', label: 'AI Document Analysis' },
              { key: 'diagnostic', label: 'Diagnostic' }
            ].map(({ key, label }) => (
              <Button
                key={key}
                variant={currentPage === key ? 'contained' : 'outlined'}
                onClick={() => setCurrentPage(key)}
                size="small"
              >
                {label}
              </Button>
            ))}
          </Box>
        </CardContent>
      </Card>

      {/* Contenu */}
      {renderPage()}
    </Container>
  )
}

ReactDOM.createRoot(document.getElementById("root")!).render(
  <ThemeProvider theme={theme}>
    <CssBaseline />
    <App />
  </ThemeProvider>
)
