import React from "react"
import ReactDOM from "react-dom/client"
import { <PERSON><PERSON><PERSON><PERSON>ider, Box, Text, Heading, Button, VStack, Container } from "@chakra-ui/react"

// Test avec ChakraProvider standard
function App() {
  return (
    <ChakraProvider>
      <Container maxW="8xl" py={8}>
        <VStack spacing={8} align="stretch">
          <Box textAlign="center">
            <Heading size="xl" mb={4}>
              AI Document Analysis (ChakraProvider Test)
            </Heading>
            <Text fontSize="lg" color="gray.600" maxW="2xl" mx="auto">
              Test avec ChakraProvider standard
            </Text>
          </Box>

          <Box p={8} bg="gray.50" borderRadius="md">
            <Text mb={4}>
              ✅ React fonctionne
            </Text>
            <Text mb={4}>
              ✅ Chakra UI standard fonctionne
            </Text>
            <Button colorScheme="blue">
              Test Button
            </Button>
          </Box>
        </VStack>
      </Container>
    </ChakraProvider>
  )
}

ReactDOM.createRoot(document.getElementById("root")!).render(<App />)
