import React from "react"
import ReactDOM from "react-dom/client"
import { ThemeProvider, createTheme } from '@mui/material/styles'
import { CssBaseline } from '@mui/material'
import MaterialLayout from './components/Layout/MaterialLayout'
import MaterialDashboard from './components/Dashboard/MaterialDashboard'

// Thème Material-UI professionnel
const theme = createTheme({
  palette: {
    primary: {
      main: '#1976d2', // Bleu professionnel
    },
    secondary: {
      main: '#dc004e', // Rouge accent
    },
    background: {
      default: '#f5f5f5',
    },
  },
  typography: {
    fontFamily: '"Roboto", "Helvetica", "Arial", sans-serif',
    h4: {
      fontWeight: 600,
    },
    h6: {
      fontWeight: 600,
    },
  },
  components: {
    MuiCard: {
      styleOverrides: {
        root: {
          boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
          borderRadius: 8,
        },
      },
    },
  },
})

// App principale avec layout Material-UI complet
function App() {
  return (
    <MaterialLayout>
      <MaterialDashboard />
    </MaterialLayout>
  )
}

ReactDOM.createRoot(document.getElementById("root")!).render(
  <ThemeProvider theme={theme}>
    <CssBaseline />
    <App />
  </ThemeProvider>
)
