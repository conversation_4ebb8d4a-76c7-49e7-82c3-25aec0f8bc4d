import React from "react"
import ReactD<PERSON> from "react-dom/client"
import { Cha<PERSON>Provider, Box, Text } from "@chakra-ui/react"

// Test ultra-simple
function App() {
  return (
    <ChakraProvider>
      <Box p={8}>
        <Text fontSize="2xl" mb={4}>
          Test Ultra-Simple
        </Text>
        <Text>
          Si vous voyez ce message, Chakra UI fonctionne !
        </Text>
      </Box>
    </ChakraProvider>
  )
}

ReactDOM.createRoot(document.getElementById("root")!).render(<App />)
