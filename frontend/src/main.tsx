import React from "react"
import ReactDOM from "react-dom/client"

// Version fonctionnelle - React pur avec interface complète
function App() {
  const [currentPage, setCurrentPage] = React.useState('home')

  const renderPage = () => {
    switch (currentPage) {
      case 'ai-analysis':
        return (
          <div style={{ padding: '20px', backgroundColor: '#f8f9fa', borderRadius: '8px' }}>
            <h2 style={{ color: '#2d3748', marginBottom: '16px' }}>AI Document Analysis</h2>
            <p style={{ marginBottom: '16px', color: '#4a5568' }}>
              Upload legal documents and let AI extract relevant information automatically.
            </p>
            <div style={{
              padding: '16px',
              backgroundColor: '#e6fffa',
              borderRadius: '8px',
              border: '1px solid #38b2ac'
            }}>
              <p style={{ margin: '0', color: '#2c7a7b' }}>
                📄 Fonctionnalité d'upload en développement...
              </p>
            </div>
          </div>
        )
      case 'diagnostic':
        return (
          <div style={{ padding: '20px', backgroundColor: '#f8f9fa', borderRadius: '8px' }}>
            <h2 style={{ color: '#2d3748', marginBottom: '16px' }}>Diagnostic</h2>
            <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
              <div style={{ color: '#38a169' }}>✅ React fonctionne</div>
              <div style={{ color: '#38a169' }}>✅ Vite fonctionne</div>
              <div style={{ color: '#38a169' }}>✅ Routing simulé fonctionne</div>
              <div style={{ color: '#e53e3e' }}>❌ Chakra UI ne fonctionne pas</div>
              <div style={{ color: '#e53e3e' }}>❌ TanStack Router ne fonctionne pas</div>
            </div>
          </div>
        )
      default:
        return (
          <div style={{ padding: '20px', backgroundColor: '#f8f9fa', borderRadius: '8px' }}>
            <h2 style={{ color: '#2d3748', marginBottom: '16px' }}>Accueil TunLeg</h2>
            <p style={{ marginBottom: '16px', color: '#4a5568' }}>
              Bienvenue dans l'application de gestion juridique TunLeg.
            </p>
            <p style={{ color: '#4a5568' }}>
              Utilisez la navigation ci-dessus pour explorer les fonctionnalités.
            </p>
          </div>
        )
    }
  }

  return (
    <div style={{
      minHeight: '100vh',
      backgroundColor: '#f7fafc',
      fontFamily: 'system-ui, -apple-system, sans-serif'
    }}>
      <div style={{ maxWidth: '1200px', margin: '0 auto', padding: '20px' }}>
        {/* Navigation */}
        <div style={{
          padding: '20px',
          backgroundColor: 'white',
          borderRadius: '8px',
          marginBottom: '20px',
          boxShadow: '0 1px 3px rgba(0,0,0,0.1)'
        }}>
          <h1 style={{ margin: '0 0 16px 0', color: '#2d3748' }}>TunLeg - Version React Pure</h1>
          <div style={{ display: 'flex', gap: '12px', flexWrap: 'wrap' }}>
            {[
              { key: 'home', label: 'Accueil' },
              { key: 'ai-analysis', label: 'AI Document Analysis' },
              { key: 'diagnostic', label: 'Diagnostic' }
            ].map(({ key, label }) => (
              <button
                key={key}
                onClick={() => setCurrentPage(key)}
                style={{
                  padding: '8px 16px',
                  backgroundColor: currentPage === key ? '#3182ce' : '#e2e8f0',
                  color: currentPage === key ? 'white' : '#2d3748',
                  border: 'none',
                  borderRadius: '6px',
                  cursor: 'pointer',
                  fontSize: '14px',
                  fontWeight: '500'
                }}
              >
                {label}
              </button>
            ))}
          </div>
        </div>

        {/* Contenu */}
        <div style={{
          backgroundColor: 'white',
          borderRadius: '8px',
          boxShadow: '0 1px 3px rgba(0,0,0,0.1)'
        }}>
          {renderPage()}
        </div>
      </div>
    </div>
  )
}

ReactDOM.createRoot(document.getElementById("root")!).render(<App />)
