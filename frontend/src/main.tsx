import React from "react"
import <PERSON>actD<PERSON> from "react-dom/client"
import { ThemeProvider, createTheme } from '@mui/material/styles'
import {
  CssBaseline,
  Container,
  Typography,
  Card,
  CardContent,
  Button,
  Box,
  Alert,
} from '@mui/material'
import { AuthProvider, useAuth } from './contexts/AuthContext';
import LoginForm from './components/Auth/LoginForm';

// Test Material-UI avec authentification
const theme = createTheme({
  palette: {
    primary: {
      main: '#1976d2',
    },
  },
})

// Test simple avec authentification
function MainApp() {
  const { user, logout } = useAuth();

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Card>
        <CardContent>
          <Typography variant="h3" component="h1" gutterBottom>
            🎉 TunLeg - Authentification Réussie !
          </Typography>
          <Typography variant="body1" paragraph>
            Bienvenue {user?.first_name} {user?.last_name} !
          </Typography>
          <Typography variant="body2" color="text.secondary" paragraph>
            Rôle: {user?.role} | Email: {user?.email}
          </Typography>
          
          <Box sx={{ mt: 3 }}>
            <Button variant="contained" onClick={logout} sx={{ mr: 2 }}>
              Se déconnecter
            </Button>
            <Button variant="outlined">
              Accéder au Dashboard
            </Button>
          </Box>
        </CardContent>
      </Card>
      
      <Box sx={{ mt: 3 }}>
        <Alert severity="success">
          ✅ Authentification fonctionnelle avec API backend
        </Alert>
        <Alert severity="info" sx={{ mt: 1 }}>
          📋 Prochaine étape : Ajouter l'interface complète
        </Alert>
        <Alert severity="warning" sx={{ mt: 1 }}>
          🚀 Upload de documents avec vrais appels API
        </Alert>
        <Alert severity="error" sx={{ mt: 1 }}>
          ⚡ Création de cas juridiques et export des résultats
        </Alert>
      </Box>
    </Container>
  );
}

// Composant App avec gestion d'authentification
function App() {
  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <AuthProvider>
        <AppContent />
      </AuthProvider>
    </ThemeProvider>
  );
}

// Contenu de l'app qui dépend de l'authentification
function AppContent() {
  const { isAuthenticated, isLoading } = useAuth();

  if (isLoading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '100vh' }}>
        <Typography variant="h6">Loading...</Typography>
      </Box>
    );
  }

  if (!isAuthenticated) {
    return <LoginForm />;
  }

  return <MainApp />;
}

ReactDOM.createRoot(document.getElementById("root")!).render(<App />)
