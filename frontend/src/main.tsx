import React from "react"
import ReactDOM from "react-dom/client"

// Test React pur - diagnostic
function App() {
  const [count, setCount] = React.useState(0)

  return (
    <div style={{
      padding: '40px',
      fontFamily: 'Arial, sans-serif',
      maxWidth: '1200px',
      margin: '0 auto'
    }}>
      <div style={{
        backgroundColor: 'white',
        padding: '30px',
        borderRadius: '8px',
        boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
        marginBottom: '20px'
      }}>
        <h1 style={{ color: '#1976d2', marginBottom: '20px' }}>
          🎉 TunLeg - Diagnostic Complet
        </h1>
        <p style={{ fontSize: '18px', marginBottom: '20px' }}>
          Test de tous les composants après tentative de migration Material-UI.
        </p>

        <div style={{
          backgroundColor: '#e8f5e9',
          padding: '15px',
          borderRadius: '4px',
          marginBottom: '20px'
        }}>
          <h3 style={{ color: '#2e7d32', margin: '0 0 10px 0' }}>✅ Composants Fonctionnels</h3>
          <ul style={{ margin: 0, paddingLeft: '20px' }}>
            <li>React 18 - Parfait</li>
            <li>Vite - Rapide et stable</li>
            <li>Docker - Conteneurs actifs</li>
            <li>HTML/CSS - Fonctionne</li>
          </ul>
        </div>

        <div style={{
          backgroundColor: '#ffebee',
          padding: '15px',
          borderRadius: '4px',
          marginBottom: '20px'
        }}>
          <h3 style={{ color: '#c62828', margin: '0 0 10px 0' }}>❌ Problèmes Identifiés</h3>
          <ul style={{ margin: 0, paddingLeft: '20px' }}>
            <li>Material-UI - Cause des pages blanches</li>
            <li>Chakra UI - Incompatible</li>
            <li>TanStack Router - Problématique</li>
          </ul>
        </div>

        <div style={{
          backgroundColor: '#e3f2fd',
          padding: '15px',
          borderRadius: '4px',
          marginBottom: '20px'
        }}>
          <h3 style={{ color: '#1565c0', margin: '0 0 10px 0' }}>🚀 Solutions Recommandées</h3>
          <ul style={{ margin: 0, paddingLeft: '20px' }}>
            <li>Continuer avec React pur + CSS personnalisé</li>
            <li>Ou essayer Tailwind CSS</li>
            <li>Ou essayer Ant Design</li>
            <li>Développer les fonctionnalités métier d'abord</li>
          </ul>
        </div>

        <div style={{
          backgroundColor: '#f3e5f5',
          padding: '15px',
          borderRadius: '4px'
        }}>
          <h3 style={{ color: '#7b1fa2', margin: '0 0 10px 0' }}>🎯 Test Interactif</h3>
          <p>Compteur: <strong>{count}</strong></p>
          <button
            onClick={() => setCount(count + 1)}
            style={{
              padding: '10px 20px',
              backgroundColor: '#1976d2',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer',
              fontSize: '16px'
            }}
          >
            Incrémenter (+1)
          </button>
          <button
            onClick={() => setCount(0)}
            style={{
              padding: '10px 20px',
              backgroundColor: '#d32f2f',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer',
              fontSize: '16px',
              marginLeft: '10px'
            }}
          >
            Reset
          </button>
        </div>
      </div>
    </div>
  )
}

ReactDOM.createRoot(document.getElementById("root")!).render(<App />)
