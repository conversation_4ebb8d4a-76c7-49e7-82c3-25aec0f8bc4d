import React from "react"
import ReactD<PERSON> from "react-dom/client"
import { ThemeProvider, createTheme } from '@mui/material/styles'
import {
  CssBaseline,
  Container,
  Typography,
  Card,
  CardContent,
  Button,
  Box,
  Grid,
  Alert
} from '@mui/material'

// Test Material-UI très simple
const theme = createTheme({
  palette: {
    primary: {
      main: '#1976d2',
    },
  },
})

function App() {
  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Card>
          <CardContent>
            <Typography variant="h3" component="h1" gutterBottom>
              🎉 Material-UI Test Simple
            </Typography>
            <Typography variant="body1" paragraph>
              Test de Material-UI avec configuration minimale.
            </Typography>
            <Box sx={{ mt: 2 }}>
              <Button variant="contained" sx={{ mr: 2 }}>
                Bouton Primary
              </Button>
              <Button variant="outlined">
                Bouton Outlined
              </Button>
            </Box>
          </CardContent>
        </Card>

        <Grid container spacing={3} sx={{ mt: 2 }}>
          <Grid item xs={12} md={6}>
            <Alert severity="success">
              ✅ Si vous voyez ce message stylé, Material-UI fonctionne !
            </Alert>
          </Grid>
          <Grid item xs={12} md={6}>
            <Alert severity="info">
              📋 Nous pouvons maintenant ajouter progressivement les composants
            </Alert>
          </Grid>
        </Grid>
      </Container>
    </ThemeProvider>
  )
}

ReactDOM.createRoot(document.getElementById("root")!).render(<App />)

ReactDOM.createRoot(document.getElementById("root")!).render(<App />)
