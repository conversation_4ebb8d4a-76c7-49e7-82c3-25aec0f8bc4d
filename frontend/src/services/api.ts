// API Service for TunLeg Backend
const API_BASE_URL = 'http://localhost:8000/api/v1';

// Types
export interface User {
  id: number;
  email: string;
  first_name: string;
  last_name: string;
  role: 'admin' | 'lawyer' | 'assistant' | 'client';
  is_active: boolean;
}

export interface LoginRequest {
  email: string;
  password: string;
}

export interface LoginResponse {
  access_token: string;
  token_type: string;
  user: User;
}

export interface UploadResponse {
  id: string;
  filename: string;
  size: number;
  content_type: string;
  upload_url: string;
  status: 'uploaded' | 'processing' | 'completed' | 'error';
}

export interface AnalysisResult {
  id: string;
  document_id: string;
  document_type: string;
  extracted_text: string;
  key_entities: string[];
  suggested_actions: string[];
  confidence: number;
  created_at: string;
}

export interface LegalCase {
  id: number;
  title: string;
  description: string;
  status: 'open' | 'in_progress' | 'closed';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  created_at: string;
  updated_at: string;
  assigned_lawyer_id?: number;
  client_id?: number;
}

// API Client Class
class ApiClient {
  private baseURL: string;
  private token: string | null = null;

  constructor(baseURL: string) {
    this.baseURL = baseURL;
    this.token = localStorage.getItem('access_token');
  }

  // Set authentication token
  setToken(token: string) {
    this.token = token;
    localStorage.setItem('access_token', token);
  }

  // Clear authentication token
  clearToken() {
    this.token = null;
    localStorage.removeItem('access_token');
  }

  // Get headers with authentication
  private getHeaders(includeAuth = true): HeadersInit {
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
    };

    if (includeAuth && this.token) {
      headers['Authorization'] = `Bearer ${this.token}`;
    }

    return headers;
  }

  // Generic request method
  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${this.baseURL}${endpoint}`;
    
    const response = await fetch(url, {
      ...options,
      headers: {
        ...this.getHeaders(),
        ...options.headers,
      },
    });

    if (!response.ok) {
      if (response.status === 401) {
        this.clearToken();
        throw new Error('Authentication required');
      }
      
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.detail || `HTTP ${response.status}: ${response.statusText}`);
    }

    return response.json();
  }

  // Authentication endpoints
  async login(credentials: LoginRequest): Promise<LoginResponse> {
    const formData = new FormData();
    formData.append('username', credentials.email);
    formData.append('password', credentials.password);

    const response = await fetch(`${this.baseURL}/login/access-token`, {
      method: 'POST',
      body: formData,
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.detail || 'Login failed');
    }

    const data = await response.json();
    this.setToken(data.access_token);
    return data;
  }

  async logout(): Promise<void> {
    try {
      await this.request('/auth/logout', { method: 'POST' });
    } finally {
      this.clearToken();
    }
  }

  async getCurrentUser(): Promise<User> {
    return this.request<User>('/users/me');
  }

  // File upload endpoints
  async uploadFile(file: File, onProgress?: (progress: number) => void): Promise<UploadResponse> {
    const formData = new FormData();
    formData.append('file', file);

    return new Promise((resolve, reject) => {
      const xhr = new XMLHttpRequest();

      // Track upload progress
      if (onProgress) {
        xhr.upload.addEventListener('progress', (event) => {
          if (event.lengthComputable) {
            const progress = (event.loaded / event.total) * 100;
            onProgress(progress);
          }
        });
      }

      xhr.addEventListener('load', () => {
        if (xhr.status >= 200 && xhr.status < 300) {
          try {
            const response = JSON.parse(xhr.responseText);
            resolve(response);
          } catch (error) {
            reject(new Error('Invalid response format'));
          }
        } else {
          reject(new Error(`Upload failed: ${xhr.statusText}`));
        }
      });

      xhr.addEventListener('error', () => {
        reject(new Error('Upload failed'));
      });

      xhr.open('POST', `${this.baseURL}/ai-analysis/upload`);
      
      if (this.token) {
        xhr.setRequestHeader('Authorization', `Bearer ${this.token}`);
      }

      xhr.send(formData);
    });
  }

  // Document analysis endpoints
  async analyzeDocument(documentId: string): Promise<AnalysisResult> {
    return this.request<AnalysisResult>(`/ai-analysis/documents/${documentId}/analyze`, {
      method: 'POST',
    });
  }

  async getAnalysisResult(analysisId: string): Promise<AnalysisResult> {
    return this.request<AnalysisResult>(`/analysis/${analysisId}`);
  }

  async getAllAnalysisResults(): Promise<AnalysisResult[]> {
    return this.request<AnalysisResult[]>('/analysis');
  }

  // Legal cases endpoints
  async createLegalCase(caseData: Partial<LegalCase>): Promise<LegalCase> {
    return this.request<LegalCase>('/legal-cases', {
      method: 'POST',
      body: JSON.stringify(caseData),
    });
  }

  async createCaseFromAnalysis(analysisId: string, caseData: Partial<LegalCase>): Promise<LegalCase> {
    return this.request<LegalCase>(`/analysis/${analysisId}/create-case`, {
      method: 'POST',
      body: JSON.stringify(caseData),
    });
  }

  async getLegalCases(): Promise<LegalCase[]> {
    return this.request<LegalCase[]>('/legal-cases');
  }

  async getLegalCase(caseId: number): Promise<LegalCase> {
    return this.request<LegalCase>(`/legal-cases/${caseId}`);
  }

  async updateLegalCase(caseId: number, updates: Partial<LegalCase>): Promise<LegalCase> {
    return this.request<LegalCase>(`/legal-cases/${caseId}`, {
      method: 'PUT',
      body: JSON.stringify(updates),
    });
  }

  // Export endpoints
  async exportAnalysisResults(analysisIds: string[], format: 'json' | 'csv' | 'pdf' = 'json'): Promise<Blob> {
    const response = await fetch(`${this.baseURL}/analysis/export`, {
      method: 'POST',
      headers: this.getHeaders(),
      body: JSON.stringify({
        analysis_ids: analysisIds,
        format,
      }),
    });

    if (!response.ok) {
      throw new Error('Export failed');
    }

    return response.blob();
  }

  // Health check
  async healthCheck(): Promise<{ status: string }> {
    return this.request<{ status: string }>('/health', {
      headers: this.getHeaders(false), // No auth required for health check
    });
  }
}

// Create and export API client instance
export const apiClient = new ApiClient(API_BASE_URL);

// Utility functions
export const downloadBlob = (blob: Blob, filename: string) => {
  const url = window.URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.href = url;
  link.download = filename;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  window.URL.revokeObjectURL(url);
};

export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

export const isAuthenticated = (): boolean => {
  return !!localStorage.getItem('access_token');
};
