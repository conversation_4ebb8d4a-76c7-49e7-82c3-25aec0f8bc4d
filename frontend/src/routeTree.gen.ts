/* prettier-ignore-start */

/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file is auto-generated by TanStack Router

// Import Routes

import { Route as rootRoute } from './routes/__root'
import { Route as TestImport } from './routes/test'
import { Route as SignupImport } from './routes/signup'
import { Route as ResetPasswordImport } from './routes/reset-password'
import { Route as RecoverPasswordImport } from './routes/recover-password'
import { Route as LoginImport } from './routes/login'
import { Route as LayoutImport } from './routes/_layout'
import { Route as LayoutIndexImport } from './routes/_layout/index'
import { Route as LayoutTemplatesImport } from './routes/_layout/templates'
import { Route as LayoutTeamImport } from './routes/_layout/team'
import { Route as LayoutSettingsImport } from './routes/_layout/settings'
import { Route as LayoutReportsImport } from './routes/_layout/reports'
import { Route as LayoutNotificationsImport } from './routes/_layout/notifications'
import { Route as LayoutMaterialLegalCasesImport } from './routes/_layout/material-legal-cases'
import { Route as LayoutMaterialDemoImport } from './routes/_layout/material-demo'
import { Route as LayoutMaterialDashboardImport } from './routes/_layout/material-dashboard'
import { Route as LayoutMaterialAdminImport } from './routes/_layout/material-admin'
import { Route as LayoutLegalCasesImport } from './routes/_layout/legal-cases'
import { Route as LayoutItemsImport } from './routes/_layout/items'
import { Route as LayoutDocumentsImport } from './routes/_layout/documents'
import { Route as LayoutClientPortalImport } from './routes/_layout/client-portal'
import { Route as LayoutAdminImport } from './routes/_layout/admin'
import { Route as LayoutAiDocumentAnalysisImport } from './routes/_layout/ai-document-analysis'
import { Route as LayoutLegalCasesCaseIdImport } from './routes/_layout/legal-cases.$caseId'

// Create/Update Routes

const TestRoute = TestImport.update({
  path: '/test',
  getParentRoute: () => rootRoute,
} as any)

const SignupRoute = SignupImport.update({
  path: '/signup',
  getParentRoute: () => rootRoute,
} as any)

const ResetPasswordRoute = ResetPasswordImport.update({
  path: '/reset-password',
  getParentRoute: () => rootRoute,
} as any)

const RecoverPasswordRoute = RecoverPasswordImport.update({
  path: '/recover-password',
  getParentRoute: () => rootRoute,
} as any)

const LoginRoute = LoginImport.update({
  path: '/login',
  getParentRoute: () => rootRoute,
} as any)

const LayoutRoute = LayoutImport.update({
  id: '/_layout',
  getParentRoute: () => rootRoute,
} as any)

const LayoutIndexRoute = LayoutIndexImport.update({
  path: '/',
  getParentRoute: () => LayoutRoute,
} as any)

const LayoutTemplatesRoute = LayoutTemplatesImport.update({
  path: '/templates',
  getParentRoute: () => LayoutRoute,
} as any)

const LayoutTeamRoute = LayoutTeamImport.update({
  path: '/team',
  getParentRoute: () => LayoutRoute,
} as any)

const LayoutSettingsRoute = LayoutSettingsImport.update({
  path: '/settings',
  getParentRoute: () => LayoutRoute,
} as any)

const LayoutReportsRoute = LayoutReportsImport.update({
  path: '/reports',
  getParentRoute: () => LayoutRoute,
} as any)

const LayoutNotificationsRoute = LayoutNotificationsImport.update({
  path: '/notifications',
  getParentRoute: () => LayoutRoute,
} as any)

const LayoutMaterialLegalCasesRoute = LayoutMaterialLegalCasesImport.update({
  path: '/material-legal-cases',
  getParentRoute: () => LayoutRoute,
} as any)

const LayoutMaterialDemoRoute = LayoutMaterialDemoImport.update({
  path: '/material-demo',
  getParentRoute: () => LayoutRoute,
} as any)

const LayoutMaterialDashboardRoute = LayoutMaterialDashboardImport.update({
  path: '/material-dashboard',
  getParentRoute: () => LayoutRoute,
} as any)

const LayoutMaterialAdminRoute = LayoutMaterialAdminImport.update({
  path: '/material-admin',
  getParentRoute: () => LayoutRoute,
} as any)

const LayoutLegalCasesRoute = LayoutLegalCasesImport.update({
  path: '/legal-cases',
  getParentRoute: () => LayoutRoute,
} as any)

const LayoutItemsRoute = LayoutItemsImport.update({
  path: '/items',
  getParentRoute: () => LayoutRoute,
} as any)

const LayoutDocumentsRoute = LayoutDocumentsImport.update({
  path: '/documents',
  getParentRoute: () => LayoutRoute,
} as any)

const LayoutClientPortalRoute = LayoutClientPortalImport.update({
  path: '/client-portal',
  getParentRoute: () => LayoutRoute,
} as any)

const LayoutAdminRoute = LayoutAdminImport.update({
  path: '/admin',
  getParentRoute: () => LayoutRoute,
} as any)

const LayoutLegalCasesCaseIdRoute = LayoutLegalCasesCaseIdImport.update({
  path: '/$caseId',
  getParentRoute: () => LayoutLegalCasesRoute,
} as any)

// Populate the FileRoutesByPath interface

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/_layout': {
      preLoaderRoute: typeof LayoutImport
      parentRoute: typeof rootRoute
    }
    '/login': {
      preLoaderRoute: typeof LoginImport
      parentRoute: typeof rootRoute
    }
    '/recover-password': {
      preLoaderRoute: typeof RecoverPasswordImport
      parentRoute: typeof rootRoute
    }
    '/reset-password': {
      preLoaderRoute: typeof ResetPasswordImport
      parentRoute: typeof rootRoute
    }
    '/signup': {
      preLoaderRoute: typeof SignupImport
      parentRoute: typeof rootRoute
    }
    '/test': {
      preLoaderRoute: typeof TestImport
      parentRoute: typeof rootRoute
    }
    '/_layout/admin': {
      preLoaderRoute: typeof LayoutAdminImport
      parentRoute: typeof LayoutImport
    }
    '/_layout/client-portal': {
      preLoaderRoute: typeof LayoutClientPortalImport
      parentRoute: typeof LayoutImport
    }
    '/_layout/documents': {
      preLoaderRoute: typeof LayoutDocumentsImport
      parentRoute: typeof LayoutImport
    }
    '/_layout/items': {
      preLoaderRoute: typeof LayoutItemsImport
      parentRoute: typeof LayoutImport
    }
    '/_layout/legal-cases': {
      preLoaderRoute: typeof LayoutLegalCasesImport
      parentRoute: typeof LayoutImport
    }
    '/_layout/material-admin': {
      preLoaderRoute: typeof LayoutMaterialAdminImport
      parentRoute: typeof LayoutImport
    }
    '/_layout/material-dashboard': {
      preLoaderRoute: typeof LayoutMaterialDashboardImport
      parentRoute: typeof LayoutImport
    }
    '/_layout/material-demo': {
      preLoaderRoute: typeof LayoutMaterialDemoImport
      parentRoute: typeof LayoutImport
    }
    '/_layout/material-legal-cases': {
      preLoaderRoute: typeof LayoutMaterialLegalCasesImport
      parentRoute: typeof LayoutImport
    }
    '/_layout/notifications': {
      preLoaderRoute: typeof LayoutNotificationsImport
      parentRoute: typeof LayoutImport
    }
    '/_layout/reports': {
      preLoaderRoute: typeof LayoutReportsImport
      parentRoute: typeof LayoutImport
    }
    '/_layout/settings': {
      preLoaderRoute: typeof LayoutSettingsImport
      parentRoute: typeof LayoutImport
    }
    '/_layout/team': {
      preLoaderRoute: typeof LayoutTeamImport
      parentRoute: typeof LayoutImport
    }
    '/_layout/templates': {
      preLoaderRoute: typeof LayoutTemplatesImport
      parentRoute: typeof LayoutImport
    }
    '/_layout/': {
      preLoaderRoute: typeof LayoutIndexImport
      parentRoute: typeof LayoutImport
    }
    '/_layout/legal-cases/$caseId': {
      preLoaderRoute: typeof LayoutLegalCasesCaseIdImport
      parentRoute: typeof LayoutLegalCasesImport
    }
  }
}

// Create and export the route tree

export const routeTree = rootRoute.addChildren([
  LayoutRoute.addChildren([
    LayoutAdminRoute,
    LayoutClientPortalRoute,
    LayoutDocumentsRoute,
    LayoutItemsRoute,
    LayoutLegalCasesRoute.addChildren([LayoutLegalCasesCaseIdRoute]),
    LayoutMaterialAdminRoute,
    LayoutMaterialDashboardRoute,
    LayoutMaterialDemoRoute,
    LayoutMaterialLegalCasesRoute,
    LayoutNotificationsRoute,
    LayoutReportsRoute,
    LayoutSettingsRoute,
    LayoutTeamRoute,
    LayoutTemplatesRoute,
    LayoutIndexRoute,
  ]),
  LoginRoute,
  RecoverPasswordRoute,
  ResetPasswordRoute,
  SignupRoute,
  TestRoute,
])

/* prettier-ignore-end */
