/* prettier-ignore-start */

/* eslint-disable */

// @ts-nocheck

// noinint

// This file is auto-generated by TanStack Router

import { createFileRoute, createRootRoute } from '@tanstack/react-router'

// Import Routes

import { Route as rootRoute } from './routes/__root'
import { Route as TestImport } from './routes/test'
import { Route as SignupImport } from './routes/signup'
import { Route as ResetPasswordImport } from './routes/reset-password'
import { Route as RecoverPasswordImport } from './routes/recover-password'
import { Route as LoginImport } from './routes/login'
import { Route as LayoutImport } from './routes/_layout'
import { Route as LayoutTemplatesImport } from './routes/_layout/templates'
import { Route as LayoutTeamImport } from './routes/_layout/team'
import { Route as LayoutSettingsImport } from './routes/_layout/settings'
import { Route as LayoutReportsImport } from './routes/_layout/reports'
import { Route as LayoutNotificationsImport } from './routes/_layout/notifications'
import { Route as LayoutMaterialLegalCasesImport } from './routes/_layout/material-legal-cases'
import { Route as LayoutMaterialDemoImport } from './routes/_layout/material-demo'
import { Route as LayoutMaterialDashboardImport } from './routes/_layout/material-dashboard'
import { Route as LayoutMaterialAdminImport } from './routes/_layout/material-admin'
import { Route as LayoutLegalCasesImport } from './routes/_layout/legal-cases'
import { Route as LayoutItemsImport } from './routes/_layout/items'
import { Route as LayoutDocumentsImport } from './routes/_layout/documents'
import { Route as LayoutClientPortalImport } from './routes/_layout/client-portal'
import { Route as LayoutAdminImport } from './routes/_layout/admin'
import { Route as LayoutAiDocumentAnalysisImport } from './routes/_layout/ai-document-analysis'
import { Route as LayoutLegalCasesCaseIdImport } from './routes/_layout/legal-cases.$caseId'
import { Route as LayoutIndexImport } from './routes/_layout/index'

// Create and export the route tree

export const routeTree = rootRoute.addChildren([
  LayoutImport.addChildren([
    LayoutAdminImport,
    LayoutAiDocumentAnalysisImport,
    LayoutClientPortalImport,
    LayoutDocumentsImport,
    LayoutItemsImport,
    LayoutLegalCasesImport.addChildren([LayoutLegalCasesCaseIdImport]),
    LayoutMaterialAdminImport,
    LayoutMaterialDashboardImport,
    LayoutMaterialDemoImport,
    LayoutMaterialLegalCasesImport,
    LayoutNotificationsImport,
    LayoutReportsImport,
    LayoutSettingsImport,
    LayoutTeamImport,
    LayoutTemplatesImport,
    LayoutIndexImport,
  ]),
  LoginImport,
  RecoverPasswordImport,
  ResetPasswordImport,
  SignupImport,
  TestImport,
])

/* prettier-ignore-end */
