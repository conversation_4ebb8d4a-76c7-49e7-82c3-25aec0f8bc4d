{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "biome check --apply-unsafe --no-errors-on-unmatched --files-ignore-unknown=true ./", "preview": "vite preview", "generate-client": "openapi-ts"}, "dependencies": {"@chakra-ui/react": "^3.8.0", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.1", "@mui/icons-material": "^5.18.0", "@mui/lab": "^5.0.0-alpha.150", "@mui/material": "^5.18.0", "@mui/x-data-grid": "^6.18.0", "@tanstack/react-query": "^5.28.14", "@tanstack/react-query-devtools": "^5.28.14", "@tanstack/react-router": "1.19.1", "axios": "1.7.4", "form-data": "4.0.0", "next-themes": "^0.4.4", "react": "^18.2.0", "react-dom": "^18.2.0", "react-dropzone": "^14.3.8", "react-error-boundary": "^4.0.13", "react-hook-form": "7.49.3", "react-icons": "^5.4.0", "recharts": "^2.15.3"}, "devDependencies": {"@biomejs/biome": "1.6.1", "@hey-api/openapi-ts": "^0.57.0", "@playwright/test": "^1.45.2", "@tanstack/router-devtools": "1.19.1", "@tanstack/router-vite-plugin": "1.19.0", "@types/node": "^20.10.5", "@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@vitejs/plugin-react-swc": "^3.5.0", "autoprefixer": "^10.4.21", "dotenv": "^16.4.5", "postcss": "^8.5.6", "tailwindcss": "^4.1.11", "typescript": "^5.2.2", "vite": "^5.4.14"}}