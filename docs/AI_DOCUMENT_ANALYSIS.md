# AI Document Analysis - Création automatique d'affaires

## Vue d'ensemble

Cette fonctionnalité permet de créer automatiquement des affaires légales à partir de documents scannés en utilisant l'intelligence artificielle pour extraire les informations pertinentes.

## Fonctionnalités principales

### 1. Analyse intelligente de documents
- Upload de documents (PDF, images scannées)
- Extraction automatique de texte (OCR si nécessaire)
- Analyse par OpenAI pour identifier et extraire :
  - Les parties impliquées (demandeur, défendeur, témoins)
  - Le type d'affaire (civil, pénal, commercial, etc.)
  - Résumé automatique du document
  - Dates importantes (échéances, audiences)
  - Montants en jeu
  - Références légales (articles de loi, jurisprudence)

### 2. Validation et création d'affaire
- Interface de validation des informations extraites
- Possibilité de modifier/corriger les données avant création
- Création automatique de l'affaire avec les informations validées
- Association des documents source à l'affaire

### 3. Mise à jour incrémentale
- Analyse de nouveaux documents ajoutés à une affaire existante
- Mise à jour automatique des informations de l'affaire
- Validation utilisateur des modifications proposées
- Historique des changements et analyses

## Architecture technique

### Backend

#### Modèles de données
- **DocumentAnalysis** : Résultats d'analyse IA
- **ExtractedInformation** : Informations structurées extraites
- **UserValidation** : Validations utilisateur
- **AnalysisHistory** : Historique des analyses

#### Services
- **AIDocumentAnalysisService** : Service principal d'analyse IA
- **AICaseCreationService** : Service de création d'affaires

#### API Endpoints
- `POST /api/v1/ai-analysis/documents/{document_id}/analyze` - Analyser un document
- `GET /api/v1/ai-analysis/analysis/{analysis_id}/extracted-info` - Récupérer les informations extraites
- `POST /api/v1/ai-analysis/extracted-info/{extracted_info_id}/validate` - Créer une validation
- `POST /api/v1/ai-analysis/validations/{validation_id}/create-case` - Créer une affaire

### Frontend

#### Composants
- **DocumentAnalysisUpload** : Upload et déclenchement d'analyse
- **ExtractedInfoValidation** : Validation des informations extraites
- **AIDocumentAnalysisWorkflow** : Workflow complet étape par étape

#### Route
- `/ai-document-analysis` : Interface principale

## Configuration requise

### Variables d'environnement
```bash
OPENAI_API_KEY=your_openai_api_key
OPENAI_MODEL=gpt-4
OPENAI_MAX_TOKENS=2000
OPENAI_TEMPERATURE=0.1
```

### Dépendances Python
```
openai>=1.0.0
pytesseract>=0.3.10
pdf2image>=1.16.3
pillow>=10.0.0
pypdf>=3.17.0
```

### Dépendances système
- Tesseract OCR
- Poppler (pour pdf2image)

## Utilisation

### 1. Accès à la fonctionnalité
- Connectez-vous en tant qu'avocat ou administrateur
- Naviguez vers "AI Document Analysis" dans le menu latéral

### 2. Workflow d'analyse

#### Étape 1 : Upload du document
1. Sélectionnez un fichier (PDF, JPEG, PNG, TIFF)
2. Choisissez le type d'analyse :
   - **Full AI Analysis** : Extraction complète d'informations légales
   - **OCR Only** : Extraction de texte uniquement
3. Ajoutez une description optionnelle
4. Cliquez sur "Upload & Analyze"

#### Étape 2 : Révision des informations extraites
- Examinez les informations extraites par l'IA
- Vérifiez la précision des données
- Passez à l'étape de validation

#### Étape 3 : Validation
1. Modifiez les informations si nécessaire
2. Définissez le titre de l'affaire et le nom du client
3. Choisissez le statut de validation :
   - **Pending** : En attente de révision
   - **Approved** : Approuvé pour création d'affaire
   - **Modified** : Modifié par l'utilisateur
   - **Rejected** : Rejeté
4. Activez "Create legal case" si vous souhaitez créer une affaire
5. Ajoutez des notes de validation
6. Sauvegardez la validation

#### Étape 4 : Création d'affaire
- Cliquez sur "Create Legal Case" pour créer l'affaire
- L'affaire est créée avec les informations validées
- Le document original est automatiquement associé à l'affaire

### 3. Mise à jour incrémentale
Pour ajouter de nouveaux documents à une affaire existante :
1. Uploadez le nouveau document dans l'affaire
2. Analysez le document via l'API
3. Validez les nouvelles informations
4. Utilisez l'endpoint de mise à jour pour enrichir l'affaire

## Informations extraites

### Parties impliquées
- **Plaintiff/Demandeur** : Partie qui initie l'action
- **Defendant/Défendeur** : Partie contre qui l'action est intentée
- **Witnesses/Témoins** : Liste des témoins mentionnés
- **Lawyers/Avocats** : Avocats mentionnés dans le document

### Informations de l'affaire
- **Type d'affaire** : Classification automatique
- **Niveau de confiance** : Précision de la classification
- **Résumé** : Synthèse automatique du document
- **Faits clés** : Points importants extraits

### Données financières et temporelles
- **Montants** : Sommes mentionnées dans le document
- **Dates importantes** : Échéances, audiences, etc.

### Références légales
- **Articles de loi** : Références juridiques mentionnées
- **Jurisprudence** : Cas précédents cités

## Sécurité et confidentialité

### Accès
- Fonctionnalité réservée aux avocats et administrateurs
- Validation des permissions avant chaque opération

### Données
- Les documents sont stockés de manière sécurisée
- L'historique complet des analyses est conservé
- Les validations utilisateur sont tracées

### API OpenAI
- Les données sont envoyées à OpenAI pour analyse
- Assurez-vous de respecter les politiques de confidentialité
- Considérez l'utilisation d'un modèle local pour les données sensibles

## Limitations actuelles

### Formats supportés
- PDF (avec ou sans texte intégré)
- Images : JPEG, PNG, TIFF
- Taille maximale : 50MB

### Langues
- Optimisé pour le français et l'anglais
- OCR configuré pour ces langues

### Précision
- La précision dépend de la qualité du document
- Les documents manuscrits peuvent avoir une précision réduite
- Validation humaine toujours recommandée

## Développements futurs

### Améliorations prévues
- Support de formats additionnels (DOCX, RTF)
- Amélioration de la précision OCR
- Modèles IA spécialisés par type d'affaire
- Interface de correction collaborative
- Intégration avec des bases de données juridiques

### Optimisations
- Cache des résultats d'analyse
- Traitement en arrière-plan pour gros documents
- API de batch processing
- Métriques de performance et qualité

## Support et dépannage

### Problèmes courants
1. **Erreur d'analyse** : Vérifiez la configuration OpenAI
2. **OCR échoue** : Vérifiez l'installation de Tesseract
3. **Upload échoue** : Vérifiez la taille et le format du fichier

### Logs
- Les erreurs sont loggées dans les services backend
- L'historique des analyses est disponible via l'API
- Les métriques de performance sont trackées

### Contact
Pour toute question ou problème, contactez l'équipe de développement.
