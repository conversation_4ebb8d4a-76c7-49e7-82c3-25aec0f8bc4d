# AI Document Analysis - Automatic Legal Case Creation

## Overview

This feature enables automatic creation of legal cases from scanned documents using artificial intelligence to extract relevant information.

## Main Features

### 1. Intelligent Document Analysis
- Document upload (PDF, scanned images)
- Automatic text extraction (OCR when needed)
- OpenAI analysis to identify and extract:
  - Involved parties (plaintiff, defendant, witnesses)
  - Case type (civil, criminal, commercial, etc.)
  - Automatic document summary
  - Important dates (deadlines, hearings)
  - Amounts involved
  - Legal references (law articles, jurisprudence)

### 2. Validation and Case Creation
- Validation interface for extracted information
- Ability to modify/correct data before creation
- Automatic case creation with validated information
- Association of source documents to the case

### 3. Incremental Updates
- Analysis of new documents added to an existing case
- Automatic update of case information
- User validation of proposed modifications
- History of changes and analyses

## Technical Architecture

### Backend

#### Data Models
- **DocumentAnalysis**: AI analysis results
- **ExtractedInformation**: Structured extracted information
- **UserValidation**: User validations
- **AnalysisHistory**: Analysis history

#### Services
- **AIDocumentAnalysisService**: Main AI analysis service
- **AICaseCreationService**: Case creation service

#### API Endpoints
- `POST /api/v1/ai-analysis/documents/{document_id}/analyze` - Analyze a document
- `GET /api/v1/ai-analysis/analysis/{analysis_id}/extracted-info` - Get extracted information
- `POST /api/v1/ai-analysis/extracted-info/{extracted_info_id}/validate` - Create validation
- `POST /api/v1/ai-analysis/validations/{validation_id}/create-case` - Create case

### Frontend

#### Components
- **DocumentAnalysisUpload**: Upload and trigger analysis
- **ExtractedInfoValidation**: Validation of extracted information
- **AIDocumentAnalysisWorkflow**: Complete step-by-step workflow

#### Route
- `/ai-document-analysis`: Main interface

## Required Configuration

### Environment Variables
```bash
OPENAI_API_KEY=your_openai_api_key
OPENAI_MODEL=gpt-4
OPENAI_MAX_TOKENS=2000
OPENAI_TEMPERATURE=0.1
```

### Python Dependencies
```
openai>=1.0.0
pytesseract>=0.3.10
pdf2image>=1.16.3
pillow>=10.0.0
pypdf>=3.17.0
```

### System Dependencies
- Tesseract OCR
- Poppler (for pdf2image)

## Usage

### 1. Accessing the Feature
- Log in as a lawyer or administrator
- Navigate to "AI Document Analysis" in the sidebar menu

### 2. Analysis Workflow

#### Step 1: Document Upload
1. Select a file (PDF, JPEG, PNG, TIFF)
2. Choose analysis type:
   - **Full AI Analysis**: Complete legal information extraction
   - **OCR Only**: Text extraction only
3. Add optional description
4. Click "Upload & Analyze"

#### Step 2: Review Extracted Information
- Examine information extracted by AI
- Verify data accuracy
- Proceed to validation step

#### Step 3: Validation
1. Modify information if necessary
2. Set case title and client name
3. Choose validation status:
   - **Pending**: Awaiting review
   - **Approved**: Approved for case creation
   - **Modified**: Modified by user
   - **Rejected**: Rejected
4. Enable "Create legal case" if you want to create a case
5. Add validation notes
6. Save validation

#### Step 4: Case Creation
- Click "Create Legal Case" to create the case
- Case is created with validated information
- Original document is automatically associated with the case

### 3. Incremental Updates
To add new documents to an existing case:
1. Upload the new document to the case
2. Analyze the document via API
3. Validate the new information
4. Use the update endpoint to enrich the case

## Extracted Information

### Involved Parties
- **Plaintiff**: Party initiating the action
- **Defendant**: Party against whom the action is brought
- **Witnesses**: List of mentioned witnesses
- **Lawyers**: Lawyers mentioned in the document

### Case Information
- **Case Type**: Automatic classification
- **Confidence Level**: Classification accuracy
- **Summary**: Automatic document synthesis
- **Key Facts**: Important extracted points

### Financial and Temporal Data
- **Amounts**: Sums mentioned in the document
- **Important Dates**: Deadlines, hearings, etc.

### Legal References
- **Law Articles**: Legal references mentioned
- **Jurisprudence**: Previous cases cited

## Security and Privacy

### Access
- Feature restricted to lawyers and administrators
- Permission validation before each operation

### Data
- Documents are stored securely
- Complete analysis history is preserved
- User validations are tracked

### OpenAI API
- Data is sent to OpenAI for analysis
- Ensure compliance with privacy policies
- Consider using local models for sensitive data

## Current Limitations

### Supported Formats
- PDF (with or without embedded text)
- Images: JPEG, PNG, TIFF
- Maximum size: 50MB

### Languages
- Optimized for French and English
- OCR configured for these languages

### Accuracy
- Accuracy depends on document quality
- Handwritten documents may have reduced accuracy
- Human validation always recommended

## Future Developments

### Planned Improvements
- Support for additional formats (DOCX, RTF)
- OCR accuracy improvements
- Specialized AI models by case type
- Collaborative correction interface
- Integration with legal databases

### Optimizations
- Analysis result caching
- Background processing for large documents
- Batch processing API
- Performance and quality metrics

## Support and Troubleshooting

### Common Issues
1. **Analysis Error**: Check OpenAI configuration
2. **OCR Fails**: Check Tesseract installation
3. **Upload Fails**: Check file size and format

### Logs
- Errors are logged in backend services
- Analysis history is available via API
- Performance metrics are tracked

### Contact
For any questions or issues, contact the development team.
